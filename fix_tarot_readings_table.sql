-- 修复 tarot_readings 表结构
-- 文件名: fix_tarot_readings_table.sql  
-- 创建时间: 2024年12月

-- 为 tarot_readings 表添加缺失的字段
ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS reading_type VARCHAR(50) DEFAULT 'general';

ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS spread_type VARCHAR(50);

ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS cards_drawn INTEGER DEFAULT 1;

ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS user_feedback JSONB;

ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS ai_model VARCHAR(50) DEFAULT 'deepseek-chat';

ALTER TABLE public.tarot_readings 
ADD COLUMN IF NOT EXISTS trace_id VARCHAR(100);

-- 添加列注释
COMMENT ON COLUMN public.tarot_readings.reading_type IS '解读类型: general, love, career, health 等';
COMMENT ON COLUMN public.tarot_readings.spread_type IS '牌阵类型: single, three_card, celtic_cross 等';
COMMENT ON COLUMN public.tarot_readings.cards_drawn IS '抽取的卡牌数量';
COMMENT ON COLUMN public.tarot_readings.user_feedback IS '用户反馈数据 (JSON格式)';
COMMENT ON COLUMN public.tarot_readings.ai_model IS '使用的AI模型';
COMMENT ON COLUMN public.tarot_readings.trace_id IS 'Langfuse追踪ID';

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tarot_readings_type ON public.tarot_readings(reading_type);
CREATE INDEX IF NOT EXISTS idx_tarot_readings_trace ON public.tarot_readings(trace_id);
CREATE INDEX IF NOT EXISTS idx_tarot_readings_user_date ON public.tarot_readings(user_id, created_at DESC);

-- 输出确认信息
DO $$
BEGIN
    RAISE NOTICE '✅ tarot_readings 表结构已修复完成';
    RAISE NOTICE '✅ 添加了 reading_type, spread_type, cards_drawn, user_feedback, ai_model, trace_id 字段';
    RAISE NOTICE '✅ 相关索引已创建';
END $$; 