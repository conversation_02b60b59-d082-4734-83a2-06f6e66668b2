# DeepSeek + Langfuse AI 解读追踪集成指南

## 🎯 概述

本项目已集成 DeepSeek AI 和 Langfuse 追踪系统，实现了：
- 完整的 AI 解读过程追踪
- 用户反馈收集与分析
- Prompt 优化建议生成
- 性能监控与问题诊断

## 🔧 配置步骤

### 1. Langfuse 配置（已完成）

当前配置：
```
Host: https://cloud.langfuse.com
Public Key: pk-lf-84abb16e-ff7e-4cb5-b05d-ee11ac542420
Secret Key: sk-lf-01338a20-1b5c-4ece-b7d2-68c5c80f76c1
```

### 2. DeepSeek API 配置（已配置在 Supabase）

DeepSeek API Key 已配置在 Supabase Edge Function 的环境变量中：
```
环境变量名: DEEPSEEK_API_KEY
配置位置: Supabase Dashboard → Edge Functions → Secrets
```

**如需更新 API Key**：
1. 登录 Supabase Dashboard
2. 进入 Edge Functions 设置
3. 更新 `DEEPSEEK_API_KEY` 环境变量

### 3. 启用功能

在 `lib/config/langfuse_config.dart` 中确认：
```dart
static const bool enableTracking = true;
```

## 📊 追踪的数据

### 自动追踪
- **用户问题输入**: 问题内容、类型、时间戳
- **AI 生成过程**: 模型参数、响应时间、Token 使用量
- **心理模式识别**: 识别结果、置信度
- **重复问题检测**: 重复级别、处理策略
- **回复生成**: 内容长度、生成方式（AI/模板）

### 用户反馈
- **有用性评分**: 1-5 星
- **准确性评分**: 1-5 星  
- **共情度评分**: 1-5 星
- **文字反馈**: 可选的具体建议

## 🔍 Langfuse 仪表板使用

### 1. 访问仪表板
- 前往: https://cloud.langfuse.com
- 使用您的账号登录

### 2. 主要功能
- **Traces**: 查看完整的解读会话
- **Generations**: 查看每次 AI 调用详情
- **Scores**: 查看用户反馈评分
- **Analytics**: 查看性能统计

### 3. 关键指标监控
- **平均响应时间**: < 3秒为良好
- **用户满意度**: 目标 > 4.0/5.0
- **错误率**: 目标 < 5%
- **Token 使用量**: 监控成本

## 🛠 开发者功能

### 1. 查看实时追踪
```dart
// 在代码中可以看到追踪日志
print('✅ Langfuse Trace创建成功: $traceId');
print('✅ Langfuse Generation记录成功: $generationId');
```

### 2. 添加自定义事件
```dart
await LangfuseService.createEvent(
  traceId: traceId,
  name: 'custom_event',
  input: {'data': 'value'},
  metadata: {'context': 'additional_info'},
);
```

### 3. 记录性能指标
```dart
await LangfuseService.createScore(
  traceId: traceId,
  generationId: generationId,
  name: 'custom_metric',
  value: 4.2,
  comment: 'Performance measurement',
);
```

## 📈 优化建议系统

### 自动分析维度
1. **有用性**: 回复是否解决用户问题
2. **准确性**: 塔罗解读是否专业准确  
3. **共情度**: 是否体现温暖的关怀
4. **重复处理**: 重复问题识别准确性
5. **响应速度**: AI 生成响应时间

### 优化触发条件
- 某项评分 < 3.5 分
- 重复问题处理成功率 < 80%
- 用户投诉率 > 10%
- 响应时间 > 5 秒

### 优化建议示例
```
🎯 提高实用性：增加更具体的行动建议和指导
🔍 提升准确性：优化心理模式识别算法，减少误判  
💜 加强共情：使用更温暖、理解性的语言表达
🔄 优化重复问题处理：提升深层挖掘的效果
```

## 🚀 最佳实践

### 1. 定期查看报告
- 每周查看 Langfuse 仪表板
- 关注用户反馈趋势
- 识别性能瓶颈

### 2. A/B 测试 Prompt
- 创建不同版本的 Prompt
- 通过 Langfuse 对比效果
- 基于数据决策优化方向

### 3. 监控异常
- 设置评分过低的警报
- 监控 API 错误率
- 跟踪用户流失

### 4. 持续优化
- 基于反馈调整 Prompt
- 优化心理模式识别
- 改进重复问题处理

## 🔧 故障排除

### 常见问题

**Q: Langfuse 追踪不工作**
A: 检查 API 密钥是否正确，网络连接是否正常

**Q: DeepSeek API 调用失败**  
A: 确认 API Key 有效，检查余额是否充足

**Q: 用户反馈不显示**
A: 检查评分组件是否正确渲染，网络请求是否成功

### 调试模式
在开发中启用详细日志：
```dart
// 在 LangfuseService 中查看详细响应
print('Langfuse响应: ${response.body}');
```

## 📊 成本优化

### Token 使用优化
- 限制 Prompt 长度
- 设置合理的 max_tokens
- 使用缓存减少重复调用

### 批量处理
- 启用 Langfuse 批量模式
- 定期批量上传数据
- 减少单次网络调用

### 监控预算
- 设置 DeepSeek API 调用限制
- 监控月度使用量
- 优化模型参数

---

通过这套系统，您将获得：
✅ 完整的 AI 解读质量监控
✅ 基于数据的 Prompt 优化建议  
✅ 用户体验持续改善
✅ 成本效益优化 