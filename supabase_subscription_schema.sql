-- 🎯 AI塔罗应用会员订阅数据库表结构
-- 在Supabase SQL Editor中执行此脚本

-- ============================================================================
-- 1. 订阅计划表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.subscription_plans (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL UNIQUE, -- 'basic', 'premium'
    display_name TEXT NOT NULL, -- '基础会员', '高级会员'
    description TEXT,
    price DECIMAL(10,2) NOT NULL, -- 价格（美元）
    currency TEXT DEFAULT 'USD',
    period TEXT NOT NULL CHECK (period IN ('weekly', 'monthly', 'yearly')),
    period_days INTEGER NOT NULL, -- 周期天数
    daily_usage_limit INTEGER NOT NULL, -- 每日使用次数限制
    features JSONB DEFAULT '{}', -- 功能特性
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 2. 用户订阅表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.user_subscriptions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    plan_id UUID REFERENCES public.subscription_plans(id) NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('active', 'expired', 'cancelled', 'pending')),
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE NOT NULL,
    payment_method TEXT, -- 'manual', 'stripe', 'paypal'
    payment_status TEXT DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_id TEXT, -- 支付交易ID
    amount_paid DECIMAL(10,2),
    currency TEXT DEFAULT 'USD',
    auto_renew BOOLEAN DEFAULT FALSE,
    cancelled_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, status) -- 每个用户只能有一个活跃订阅
);

-- ============================================================================
-- 3. 使用记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.usage_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    feature_type TEXT NOT NULL, -- 'tarot_reading', 'daily_tarot', 'manifestation'
    usage_date DATE NOT NULL,
    usage_count INTEGER DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, feature_type, usage_date)
);

-- ============================================================================
-- 4. 支付记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.payment_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    subscription_id UUID REFERENCES public.user_subscriptions(id),
    amount DECIMAL(10,2) NOT NULL,
    currency TEXT DEFAULT 'USD',
    payment_method TEXT NOT NULL,
    payment_status TEXT NOT NULL CHECK (payment_status IN ('pending', 'completed', 'failed', 'refunded')),
    transaction_id TEXT,
    payment_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 5. 创建索引
-- ============================================================================
CREATE INDEX idx_subscription_plans_active ON public.subscription_plans(is_active, sort_order);
CREATE INDEX idx_user_subscriptions_user_id ON public.user_subscriptions(user_id);
CREATE INDEX idx_user_subscriptions_status ON public.user_subscriptions(status);
CREATE INDEX idx_user_subscriptions_end_date ON public.user_subscriptions(end_date);
CREATE INDEX idx_usage_records_user_date ON public.usage_records(user_id, usage_date);
CREATE INDEX idx_payment_records_user_id ON public.payment_records(user_id);
CREATE INDEX idx_payment_records_status ON public.payment_records(payment_status);

-- ============================================================================
-- 6. 启用行级安全 (RLS)
-- ============================================================================
ALTER TABLE public.subscription_plans ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_records ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 7. 安全策略
-- ============================================================================

-- 订阅计划策略（所有用户可读）
CREATE POLICY "Anyone can view active subscription plans" ON public.subscription_plans
    FOR SELECT USING (is_active = true);

-- 用户订阅策略
CREATE POLICY "Users can view own subscriptions" ON public.user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscriptions" ON public.user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscriptions" ON public.user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

-- 使用记录策略
CREATE POLICY "Users can view own usage records" ON public.usage_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own usage records" ON public.usage_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own usage records" ON public.usage_records
    FOR UPDATE USING (auth.uid() = user_id);

-- 支付记录策略
CREATE POLICY "Users can view own payment records" ON public.payment_records
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment records" ON public.payment_records
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- ============================================================================
-- 8. 触发器
-- ============================================================================

-- 更新updated_at字段的触发器
CREATE TRIGGER update_subscription_plans_updated_at 
    BEFORE UPDATE ON public.subscription_plans 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_subscriptions_updated_at 
    BEFORE UPDATE ON public.user_subscriptions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- 9. 插入默认订阅计划
-- ============================================================================
INSERT INTO public.subscription_plans (name, display_name, description, price, period, period_days, daily_usage_limit, features, sort_order) VALUES
-- 免费计划
('free', '免费会员', '每周1次塔罗占卜', 0.00, 'weekly', 7, 1, '{"tarot_readings": 1, "spreads": ["single"], "features": ["basic_interpretation"]}', 0),

-- 基础会员
('basic_weekly', '基础会员（周付）', '每日1次塔罗占卜，所有牌阵', 2.99, 'weekly', 7, 1, '{"tarot_readings": 7, "spreads": ["single", "three", "celtic"], "features": ["ai_interpretation", "reading_history", "daily_tarot"]}', 1),
('basic_monthly', '基础会员（月付）', '每日1次塔罗占卜，所有牌阵，8折优惠', 9.99, 'monthly', 30, 1, '{"tarot_readings": 30, "spreads": ["single", "three", "celtic"], "features": ["ai_interpretation", "reading_history", "daily_tarot", "discount"]}', 2),
('basic_yearly', '基础会员（年付）', '每日1次塔罗占卜，所有牌阵，6折优惠', 89.99, 'yearly', 365, 1, '{"tarot_readings": 365, "spreads": ["single", "three", "celtic"], "features": ["ai_interpretation", "reading_history", "daily_tarot", "discount", "priority_support"]}', 3),

-- 高级会员
('premium_weekly', '高级会员（周付）', '每日5次塔罗占卜，所有牌阵，高级功能', 4.99, 'weekly', 7, 5, '{"tarot_readings": 35, "spreads": ["single", "three", "celtic", "custom"], "features": ["ai_interpretation", "reading_history", "daily_tarot", "manifestation_tools", "advanced_analytics"]}', 4),
('premium_monthly', '高级会员（月付）', '每日5次塔罗占卜，所有牌阵，高级功能，8折优惠', 17.99, 'monthly', 30, 5, '{"tarot_readings": 150, "spreads": ["single", "three", "celtic", "custom"], "features": ["ai_interpretation", "reading_history", "daily_tarot", "manifestation_tools", "advanced_analytics", "discount"]}', 5),
('premium_yearly', '高级会员（年付）', '每日5次塔罗占卜，所有牌阵，高级功能，6折优惠', 149.99, 'yearly', 365, 5, '{"tarot_readings": 1825, "spreads": ["single", "three", "celtic", "custom"], "features": ["ai_interpretation", "reading_history", "daily_tarot", "manifestation_tools", "advanced_analytics", "discount", "priority_support", "exclusive_content"]}', 6)
ON CONFLICT (name) DO NOTHING;

-- ============================================================================
-- 10. 创建视图
-- ============================================================================

-- 用户订阅状态视图
CREATE OR REPLACE VIEW user_subscription_status AS
SELECT 
    u.id as user_id,
    u.email,
    u.full_name,
    COALESCE(us.status, 'free') as subscription_status,
    COALESCE(sp.name, 'free') as plan_name,
    COALESCE(sp.display_name, '免费会员') as plan_display_name,
    COALESCE(sp.daily_usage_limit, 1) as daily_usage_limit,
    COALESCE(sp.features, '{}') as plan_features,
    us.start_date,
    us.end_date,
    us.auto_renew,
    CASE 
        WHEN us.end_date IS NULL THEN NULL
        WHEN us.end_date > NOW() THEN us.end_date - NOW()
        ELSE INTERVAL '0 days'
    END as time_remaining,
    CASE 
        WHEN us.end_date IS NULL THEN FALSE
        WHEN us.end_date > NOW() THEN TRUE
        ELSE FALSE
    END as is_active
FROM public.users u
LEFT JOIN public.user_subscriptions us ON u.id = us.user_id AND us.status = 'active'
LEFT JOIN public.subscription_plans sp ON us.plan_id = sp.id;

-- 用户使用统计视图
CREATE OR REPLACE VIEW user_usage_stats AS
SELECT 
    ur.user_id,
    ur.feature_type,
    ur.usage_date,
    ur.usage_count,
    uss.daily_usage_limit,
    uss.plan_name,
    (uss.daily_usage_limit - ur.usage_count) as remaining_usage
FROM public.usage_records ur
JOIN user_subscription_status uss ON ur.user_id = uss.user_id
WHERE ur.usage_date = CURRENT_DATE;

-- ============================================================================
-- 11. 创建函数
-- ============================================================================

-- 检查用户是否可以使用的函数
CREATE OR REPLACE FUNCTION check_user_usage_availability(
    p_user_id UUID,
    p_feature_type TEXT DEFAULT 'tarot_reading'
)
RETURNS TABLE(
    can_use BOOLEAN,
    remaining_usage INTEGER,
    daily_limit INTEGER,
    plan_name TEXT
) AS $$
DECLARE
    v_usage_count INTEGER := 0;
    v_daily_limit INTEGER := 1;
    v_plan_name TEXT := 'free';
BEGIN
    -- 获取用户今日使用次数
    SELECT COALESCE(usage_count, 0) INTO v_usage_count
    FROM public.usage_records
    WHERE user_id = p_user_id 
    AND feature_type = p_feature_type 
    AND usage_date = CURRENT_DATE;
    
    -- 获取用户订阅信息
    SELECT daily_usage_limit, plan_name INTO v_daily_limit, v_plan_name
    FROM user_subscription_status
    WHERE user_id = p_user_id;
    
    -- 返回结果
    RETURN QUERY SELECT 
        v_usage_count < v_daily_limit as can_use,
        GREATEST(0, v_daily_limit - v_usage_count) as remaining_usage,
        v_daily_limit as daily_limit,
        v_plan_name as plan_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 记录用户使用的函数
CREATE OR REPLACE FUNCTION record_user_usage(
    p_user_id UUID,
    p_feature_type TEXT DEFAULT 'tarot_reading'
)
RETURNS BOOLEAN AS $$
DECLARE
    v_can_use BOOLEAN;
BEGIN
    -- 检查是否可以继续使用
    SELECT can_use INTO v_can_use
    FROM check_user_usage_availability(p_user_id, p_feature_type);
    
    IF NOT v_can_use THEN
        RETURN FALSE;
    END IF;
    
    -- 插入或更新使用记录
    INSERT INTO public.usage_records (user_id, feature_type, usage_date, usage_count)
    VALUES (p_user_id, p_feature_type, CURRENT_DATE, 1)
    ON CONFLICT (user_id, feature_type, usage_date)
    DO UPDATE SET usage_count = usage_records.usage_count + 1;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建用户订阅的函数
CREATE OR REPLACE FUNCTION create_user_subscription(
    p_user_id UUID,
    p_plan_name TEXT,
    p_payment_method TEXT DEFAULT 'manual',
    p_transaction_id TEXT DEFAULT NULL,
    p_amount_paid DECIMAL DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_plan_id UUID;
    v_subscription_id UUID;
    v_period_days INTEGER;
BEGIN
    -- 获取计划信息
    SELECT id, period_days INTO v_plan_id, v_period_days
    FROM public.subscription_plans
    WHERE name = p_plan_name AND is_active = true;
    
    IF v_plan_id IS NULL THEN
        RAISE EXCEPTION 'Invalid subscription plan: %', p_plan_name;
    END IF;
    
    -- 取消现有订阅
    UPDATE public.user_subscriptions
    SET status = 'cancelled', cancelled_at = NOW()
    WHERE user_id = p_user_id AND status = 'active';
    
    -- 创建新订阅
    INSERT INTO public.user_subscriptions (
        user_id, plan_id, status, start_date, end_date, 
        payment_method, payment_status, transaction_id, amount_paid
    ) VALUES (
        p_user_id, v_plan_id, 'active', NOW(), NOW() + (v_period_days || ' days')::INTERVAL,
        p_payment_method, 'completed', p_transaction_id, p_amount_paid
    ) RETURNING id INTO v_subscription_id;
    
    -- 记录支付
    IF p_amount_paid IS NOT NULL THEN
        INSERT INTO public.payment_records (
            user_id, subscription_id, amount, payment_method, 
            payment_status, transaction_id, description
        ) VALUES (
            p_user_id, v_subscription_id, p_amount_paid, p_payment_method,
            'completed', p_transaction_id, 'Subscription payment for ' || p_plan_name
        );
    END IF;
    
    RETURN v_subscription_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- 12. 授予权限
-- ============================================================================
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL VIEWS IN SCHEMA public TO anon, authenticated;

-- 注释
COMMENT ON TABLE public.subscription_plans IS '订阅计划表';
COMMENT ON TABLE public.user_subscriptions IS '用户订阅表';
COMMENT ON TABLE public.usage_records IS '使用记录表';
COMMENT ON TABLE public.payment_records IS '支付记录表';
COMMENT ON VIEW user_subscription_status IS '用户订阅状态视图';
COMMENT ON VIEW user_usage_stats IS '用户使用统计视图';
COMMENT ON FUNCTION check_user_usage_availability(UUID, TEXT) IS '检查用户使用可用性';
COMMENT ON FUNCTION record_user_usage(UUID, TEXT) IS '记录用户使用';
COMMENT ON FUNCTION create_user_subscription(UUID, TEXT, TEXT, TEXT, DECIMAL) IS '创建用户订阅'; 