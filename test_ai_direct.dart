import 'dart:convert';
import 'package:http/http.dart' as http;

void main() async {
  print('🧪 直接测试Edge Function...');
  
  await testEdgeFunction();
}

Future<void> testEdgeFunction() async {
  try {
    final url = 'https://ktqlxbcauxomczubqasp.supabase.co/functions/v1/deepseek-tarot-reading';
    final headers = {
      'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0cWx4YmNhdXhvbWN6dWJxYXNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Mjc5OTksImV4cCI6MjA2MTQwMzk5OX0.BKL7B2Mbpl1sOsPI5QsX6EmbmHZu_RWPSh8knHkUEno',
      'Content-Type': 'application/json',
    };
    
    final requestData = {
      'question': '我今天的运势如何？',
      'cards': [
        {
          'id': 'test-card',
          'name': '愚者',
          'description': '新的开始',
          'meaning': '代表新的开始和无限可能',
          'keywords': ['新开始', '冒险', '纯真'],
          'isMajorArcana': true,
          'isReversed': false,
        }
      ],
      'spreadType': 'simple_reading',
      'requestType': 'initial_reading',
      'userLanguage': 'zh',
    };
    
    print('📋 发送请求到: $url');
    print('📋 请求数据: ${jsonEncode(requestData)}');
    
    final response = await http.post(
      Uri.parse(url),
      headers: headers,
      body: jsonEncode(requestData),
    );
    
    print('📨 响应状态码: ${response.statusCode}');
    print('📨 响应头: ${response.headers}');
    print('📨 响应体: ${response.body}');
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        print('✅ Edge Function调用成功!');
        print('📖 解读内容: ${data['reading']}');
      } else {
        print('❌ Edge Function返回失败: ${data['error']}');
      }
    } else {
      print('❌ HTTP请求失败: ${response.statusCode}');
    }
    
  } catch (e) {
    print('❌ 测试异常: $e');
  }
}
