# 🔮 AI 塔罗应用月度会员订阅功能设置指南

## 📋 **月度会员功能概述**

### 会员权益建议：
- ✨ 无限次塔罗占卜
- 🎯 高级牌阵（凯尔特十字等）
- 📊 详细运势分析报告
- 🎨 独家塔罗牌面设计
- 📅 每日运势提醒
- 💎 AI 深度解读功能

## 🛠️ **技术实现步骤**

### 1. **在 Xcode 中添加 In-App Purchase**
```
步骤：
1. 在 Signing & Capabilities 中
2. 点击 + Capability
3. 搜索 "In-App Purchase"
4. 添加到项目中
```

### 2. **在苹果开发者后台配置订阅**

#### A. 创建 App Store Connect 应用
```
1. 登录 App Store Connect
2. 创建新应用
3. 填写应用信息
4. 设置分级和定价
```

#### B. 配置订阅产品
```
产品 ID: com.G3RHCPDDQR.aitarotreading.monthly_premium
产品名称: AI塔罗高级会员
价格: ¥18/月 (建议价格)
免费试用: 7天 (可选)
订阅群组: Premium Access
```

### 3. **集成 StoreKit 框架**

#### A. 添加依赖
在 `pubspec.yaml` 中添加：
```yaml
dependencies:
  in_app_purchase: ^3.1.11
  purchases_flutter: ^6.20.1  # RevenueCat (推荐)
```

#### B. 创建订阅服务
```dart
// lib/services/subscription_service.dart
class SubscriptionService {
  static const String monthlyPremium = 'com.G3RHCPDDQR.aitarotreading.monthly_premium';
  
  Future<bool> purchaseMonthlySubscription() async {
    // 实现订阅购买逻辑
  }
  
  Future<bool> isSubscribed() async {
    // 检查订阅状态
  }
  
  Future<void> restorePurchases() async {
    // 恢复购买
  }
}
```

## 🔐 **权限和配置**

### iOS 配置文件更新
确保包含：
- ✅ In-App Purchase capability
- ✅ 正确的 Bundle ID
- ✅ 有效的证书和配置文件

### 测试环境
```
1. Xcode 中添加 StoreKit Configuration 文件
2. 创建测试产品
3. 使用沙盒环境测试
```

## 📊 **定价策略建议**

### 市场调研定价：
```
基础版: 免费 (每日3次占卜)
高级版: ¥18/月 或 ¥128/年
- 无限次占卜
- 高级牌阵
- 详细报告
- 无广告体验
```

### 促销策略：
```
✨ 7天免费试用
🎁 年付送2个月
🆕 新用户首月5折
```

## ⚠️ **重要合规要求**

### 苹果审核要点：
1. **必须使用苹果内购** - 不能引导用户到外部支付
2. **订阅管理** - 提供取消订阅入口
3. **免费试用条款** - 清楚说明试用和付费条件
4. **恢复购买** - 允许用户恢复之前的购买

### 隐私政策更新：
```
需要说明：
- 订阅信息收集
- 支付数据处理
- 用户权益说明
- 取消政策
```

## 🚀 **实施计划**

### 阶段1: 基础配置 (本周)
- [x] 添加 In-App Purchase capability
- [ ] 配置 App Store Connect
- [ ] 创建订阅产品

### 阶段2: 代码实现 (下周)
- [ ] 集成 StoreKit
- [ ] 实现订阅逻辑
- [ ] 创建会员专享功能

### 阶段3: 测试上线 (第三周)
- [ ] 沙盒测试
- [ ] 真机测试
- [ ] 提交审核

## 💡 **现在立即行动**

1. **在 Xcode 中添加 In-App Purchase capability**
2. **去 App Store Connect 创建应用**
3. **配置月度订阅产品**

需要我详细指导每一步的操作吗？🔮✨ 