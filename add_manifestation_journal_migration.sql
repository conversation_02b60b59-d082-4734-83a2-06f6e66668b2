-- 添加显化日记字段迁移
-- 文件名: add_manifestation_journal_migration.sql
-- 创建时间: 2024年6月

-- 为daily_tarot表添加manifestation_journal字段
ALTER TABLE public.daily_tarot 
ADD COLUMN IF NOT EXISTS manifestation_journal TEXT;

-- 添加列注释
COMMENT ON COLUMN public.daily_tarot.manifestation_journal IS '用户的显化日记记录';

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_daily_tarot_user_journal 
ON public.daily_tarot(user_id) 
WHERE manifestation_journal IS NOT NULL;

-- 输出确认信息
DO $$
BEGIN
    RAISE NOTICE '✅ 显化日记字段已成功添加到daily_tarot表';
END $$; 