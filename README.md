# AI塔罗牌阅读应用 - Figma UI版本

一个基于Flutter开发的AI驱动的塔罗牌阅读应用，采用现代神秘主义设计风格，支持Figma设计集成。

## ✨ 功能特色

### 🔮 核心功能
- **塔罗解读**: 主要功能界面，支持问题输入和牌阵选择
- **每日塔罗**: 日历视图的每日抽卡功能
- **历史记录**: 完整的解读历史和评分系统
- **个人中心**: 用户统计和设置

### 🎨 设计特色
- **Figma集成**: 支持从Figma导入设计资源
- **神秘主义风格**: 深色主题配合紫色和金色渐变
- **流畅动画**: 使用flutter_animate实现丰富的动画效果
- **响应式设计**: 移动端优先的响应式布局

### 🃏 牌阵系统
- **单张牌**: 简单快速的单卡解读
- **三张牌**: 过去-现在-未来的经典布局
- **六芒星选择器**: 直观的牌阵选择界面

## 🎯 Figma集成指南

### 方案1: 导出图片资源
1. 在Figma中选择需要的组件
2. 右键选择"Export" → PNG/SVG
3. 将图片保存到 `assets/images/` 目录
4. 更新 `lib/utils/figma_assets.dart` 中的路径映射

### 方案2: 使用设计Token
1. 在Figma中切换到Dev Mode
2. 复制颜色、字体、间距等设计规范
3. 更新 `lib/theme/figma_theme.dart` 中的设计token
4. 使用 `lib/services/figma_service.dart` 进行API集成

### 方案3: Figma API集成
```dart
// 使用Figma API获取设计资源
final figmaService = FigmaService(accessToken: 'your_token');
final fileData = await figmaService.getFile('your_file_key');
final images = await figmaService.getImages('your_file_key', nodeIds);
```

## 🚀 快速开始

### 环境要求
- Flutter 3.1.3+
- Dart 3.0+
- Web浏览器支持

### 安装步骤
```bash
# 克隆项目
git clone <repository_url>
cd ai-tarot-reading-app

# 安装依赖
flutter pub get

# 运行Web版本
flutter run -d chrome

# 构建Web版本
flutter build web
```

### 本地服务器
```bash
# 在build/web目录启动HTTP服务器
cd build/web
python3 -m http.server 8080
# 访问 http://localhost:8080
```

## 📁 项目结构

```
lib/
├── main.dart                 # 应用入口
├── models/                   # 数据模型
│   ├── tarot_card.dart      # 塔罗牌模型
│   ├── tarot_reading.dart   # 解读记录模型
│   └── daily_tarot.dart     # 每日塔罗模型
├── providers/               # 状态管理
│   └── app_state_provider.dart
├── screens/                 # 主要屏幕
│   ├── home_screen.dart     # 主屏幕
│   ├── tarot_reading_screen.dart
│   ├── daily_tarot_screen.dart
│   ├── history_screen.dart
│   └── user_account_screen.dart
├── widgets/                 # 可复用组件
│   ├── hexagram_selector.dart
│   ├── card_selection_view.dart
│   ├── ai_reading_view.dart
│   ├── figma_card_component.dart
│   └── placeholder_card_image.dart
├── theme/                   # 主题配置
│   ├── figma_theme.dart     # Figma设计主题
│   └── app_theme.dart       # 应用主题
├── services/                # 服务层
│   └── figma_service.dart   # Figma API服务
└── utils/                   # 工具类
    └── figma_assets.dart    # 资源管理
```

## 🎨 设计系统

### 颜色方案
- **主色**: `#6B46C1` (紫色)
- **次色**: `#EC4899` (粉色)
- **强调色**: `#F59E0B` (金色)
- **背景**: `#1F2937` (深灰)
- **表面**: `#374151` (中灰)

### 字体系统
- **标题**: Inter Bold (32px, 24px, 20px)
- **正文**: Inter Regular (16px, 14px)
- **标签**: Inter Medium (14px, 12px)

### 间距系统
- **XS**: 4px
- **SM**: 8px
- **MD**: 16px
- **LG**: 24px
- **XL**: 32px
- **XXL**: 48px

## 🔧 自定义配置

### 添加新的塔罗牌
1. 在 `lib/models/tarot_card.dart` 中添加卡牌数据
2. 在 `lib/utils/figma_assets.dart` 中添加图片路径
3. 将对应图片放入 `assets/images/` 目录

### 自定义主题
1. 修改 `lib/theme/figma_theme.dart` 中的颜色和样式
2. 更新 `lib/services/figma_service.dart` 中的设计token
3. 重新构建应用

### 集成AI服务
1. 在 `lib/services/` 中添加AI服务类
2. 更新 `lib/providers/app_state_provider.dart` 中的解读逻辑
3. 配置API密钥和端点

## 📱 支持平台

- ✅ Web (Chrome, Firefox, Safari, Edge)
- 🚧 Android (开发中)
- 🚧 iOS (开发中)
- 🚧 Desktop (计划中)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- Flutter团队提供的优秀框架
- Figma提供的设计工具和API
- 塔罗牌传统文化的启发
- 开源社区的贡献

## 📱 版本历史

### v1.0.8+11 (2025-01-09) - 最新版本
- 🔧 **修复App Store收据验证问题**
- ✅ **实现服务器端收据验证**（Supabase Edge Function）
- 🧪 **支持沙盒测试环境**
- 🛡️ **改善错误处理和用户反馈**
- 📦 **完整的GitHub私有仓库备份**
- 💳 **应用内购买功能完善**
- 🌍 **多语言支持优化**

### v1.0.0 (2024-01-08)
- 初始版本发布
- 基础塔罗解读功能
- Figma UI集成

---

**注意**: 这是一个演示项目，AI解读功能仅供娱乐，不应用于重要决策。

*🔒 本项目为私有仓库，包含完整的iOS应用源码、后端配置和部署文档。*
