# 🍎 AI 塔罗占卜应用 iOS App Store 上架完整指南

## 📋 **上架前检查清单**

### 1. **苹果开发者账号配置**

#### A. 证书 (Certificates)
需要创建以下证书：

✅ **开发证书**
- 类型：`Apple Development`
- 用途：真机调试和测试

✅ **发布证书**
- 类型：`iOS Distribution (App Store Connect and Ad Hoc)`
- 用途：App Store 上架

#### B. 应用标识符 (App IDs)
```
描述：AI Tarot Reading App
Bundle ID：com.[你的团队名].aitarotreading
平台：iOS

必须启用的功能：
✅ Sign in with Apple (因为你的应用有苹果登录)
✅ Push Notifications (用于日常提醒)
- Associated Domains (如果需要深度链接)
- In-App Purchase (如果有内购功能)
```

#### C. 配置文件 (Provisioning Profiles)
```
开发用：iOS App Development
发布用：App Store Distribution
```

---

## 🛠️ **详细配置步骤**

### 步骤 1：创建应用标识符 (App ID)

1. **登录苹果开发者后台**
   - 进入 [Apple Developer Portal](https://developer.apple.com/)

2. **创建 App ID**
   ```
   导航：Certificates, Identifiers & Profiles → Identifiers → App IDs
   点击：+ (添加新的)
   选择：App IDs
   
   配置信息：
   - Description: AI Tarot Reading App
   - Bundle ID: com.yourcompany.aitarotreading
   - Platform: iOS
   ```

3. **配置功能 (Capabilities)**
   ```
   必须启用：
   ✅ Sign in with Apple
   ✅ Push Notifications
   
   可选启用：
   - Associated Domains
   - Background Modes
   - In-App Purchase
   ```

### 步骤 2：创建证书

#### 开发证书：
1. **创建 CSR 文件**
   ```
   Mac 上操作：
   - 打开 Keychain Access
   - 菜单：Certificate Assistant → Request a Certificate From a Certificate Authority
   - 填写信息并保存到磁盘
   ```

2. **在开发者后台创建证书**
   ```
   导航：Certificates, Identifiers & Profiles → Certificates
   点击：+ (添加新的)
   选择：Apple Development
   上传：刚才创建的 CSR 文件
   下载：生成的证书文件并双击安装
   ```

#### 发布证书：
```
重复上述步骤，但选择：
iOS Distribution (App Store Connect and Ad Hoc)
```

### 步骤 3：创建配置文件

#### 开发配置文件：
```
导航：Certificates, Identifiers & Profiles → Profiles
点击：+ (添加新的)
选择：iOS App Development
选择：你创建的 App ID
选择：开发证书
选择：测试设备
命名：AI Tarot Reading Development
下载并安装
```

#### 发布配置文件：
```
重复上述步骤，但选择：
- App Store
- 发布证书
- 不需要选择设备
```

---

## 📱 **Xcode 项目配置**

### 1. **Bundle Identifier 配置**
```
在 Xcode 中：
- 打开 ios/Runner.xcworkspace
- 选择 Runner target
- General → Identity
- Bundle Identifier: com.yourcompany.aitarotreading
- 必须与 App ID 完全一致！
```

### 2. **签名配置**
```
Signing & Capabilities：
- Team: 选择你的开发团队
- Provisioning Profile: 选择对应的配置文件
- Automatically manage signing: 建议关闭，手动选择
```

### 3. **苹果登录配置**
```
Signing & Capabilities → + Capability → Sign in with Apple
确保这个功能已添加到项目中
```

### 4. **版本信息**
```
General：
- Version: 1.0.0
- Build: 1
- Deployment Target: iOS 12.0 (建议)
```

---

## 🔧 **应用特定配置**

### 1. **隐私政策必需**
你的应用收集用户数据，需要隐私政策：
```
内容包括：
- 收集什么数据（邮箱、塔罗记录）
- 如何使用数据
- 数据存储位置（Supabase）
- 用户权利
- 联系方式
```

### 2. **App Store 元数据**
准备以下材料：
```
应用图标：1024x1024px
截图：不同尺寸的 iPhone 截图
应用描述：中英文描述
关键词：塔罗，占卜，AI，运势
分类：娱乐 或 生活方式
年龄评级：4+ 或 9+ (因为有占卜内容)
```

### 3. **内容审核准备**
```
测试账号：为审核人员准备测试账号
功能说明：解释AI塔罗功能不是真实预测
免责声明：添加"仅供娱乐"声明
```

---

## 📤 **上架流程**

### 1. **构建应用**
```bash
# 清理项目
flutter clean
flutter pub get

# iOS 打包
flutter build ios --release

# 在 Xcode 中
Product → Archive
```

### 2. **上传到 App Store Connect**
```
在 Xcode Organizer 中：
- 选择 Archive
- 点击 Distribute App
- 选择 App Store Connect
- 上传
```

### 3. **App Store Connect 配置**
```
登录 App Store Connect：
- 创建新应用
- 填写应用信息
- 上传截图和元数据
- 选择构建版本
- 提交审核
```

---

## ⚠️ **常见问题和解决方案**

### 1. **签名问题**
```
错误：Code signing error
解决：检查证书和配置文件是否匹配
```

### 2. **苹果登录问题**
```
错误：Sign in with Apple not working
解决：确保 App ID 启用了该功能，且配置文件包含该权限
```

### 3. **隐私权限**
```
错误：Missing privacy descriptions
解决：在 Info.plist 中添加相机、相册使用说明
```

---

## 🎯 **立即行动步骤**

### 现在就可以开始：

1. **创建 App ID**
   - Bundle ID: `com.yourcompany.aitarotreading`
   - 启用：Sign in with Apple + Push Notifications

2. **创建开发证书**
   - 类型：Apple Development

3. **创建开发配置文件**
   - 选择你的 App ID 和证书

4. **在 Xcode 中配置**
   - 设置正确的 Bundle ID
   - 选择配置文件

5. **测试苹果登录**
   - 在真机上测试登录功能

准备好了吗？我可以详细指导你完成每一步！🚀 