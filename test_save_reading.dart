import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:uuid/uuid.dart';

/// 测试保存解读功能
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🧪 开始测试保存解读功能');
  
  try {
    final supabaseService = SupabaseDataService();
    
    // 检查用户登录状态
    print('🔍 检查用户登录状态...');
    print('  - isAuthenticated: ${supabaseService.isAuthenticated}');
    print('  - currentUserId: ${supabaseService.currentUserId}');
    
    if (!supabaseService.isAuthenticated) {
      print('❌ 用户未登录，无法测试保存功能');
      return;
    }
    
    // 创建测试数据
    final testCards = TarotCardsData.getRandomCards(3);
    final readingId = const Uuid().v4();
    
    print('🔄 开始保存测试解读...');
    print('📝 解读ID: $readingId');
    print('🃏 测试卡牌: ${testCards.map((c) => c.name).join(', ')}');
    
    // 保存到数据库
    await supabaseService.saveTarotReading({
      'id': readingId,
      'question': '这是一个测试问题',
      'spread_type': '三张牌阵',
      'cards': testCards.map((card) => {
        'id': card.id,
        'name': card.name,
        'description': card.description,
        'meaning': card.meaning,
        'keywords': card.keywords,
        'imageUrl': card.imageUrl,
        'isMajorArcana': card.isMajorArcana,
        'isReversed': card.isReversed,
      }).toList(),
      'interpretation': '这是一个测试解读内容，用于验证保存功能是否正常工作。',
      'reading_type': 'test_reading',
      'cards_drawn': testCards.length,
    });
    
    print('✅ 测试解读保存成功！');
    
    // 验证保存结果
    print('🔍 验证保存结果...');
    final readings = await supabaseService.getTarotReadings(5);
    print('📊 最近的解读记录数量: ${readings.length}');
    
    if (readings.isNotEmpty) {
      final latestReading = readings.first;
      print('📖 最新解读:');
      print('  - ID: ${latestReading['id']}');
      print('  - 问题: ${latestReading['question']}');
      print('  - 解读类型: ${latestReading['reading_type']}');
      print('  - 创建时间: ${latestReading['created_at']}');
    }
    
  } catch (e) {
    print('❌ 测试失败: $e');
    print('📊 错误详情: ${e.toString()}');
  }
}
