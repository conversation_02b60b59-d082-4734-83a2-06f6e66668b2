# 🌐 Apple Sign In 网络要求说明

## 📡 网络连接要求

### ✅ **必需的网络服务**

Apple登录功能需要以下网络服务正常工作：

#### 1. **Apple ID服务**
- **域名**: `appleid.apple.com`
- **用途**: Apple ID认证和授权
- **协议**: HTTPS
- **状态**: 必需

#### 2. **Apple身份管理服务**
- **域名**: `idmsa.apple.com`
- **用途**: 身份管理和用户信息验证
- **协议**: HTTPS
- **状态**: 必需

#### 3. **Supabase后端服务**
- **域名**: `ktqlxbcauxomczubqasp.supabase.co`
- **用途**: 用户数据存储和同步
- **协议**: HTTPS
- **状态**: 必需（用于数据同步）

## 🔧 网络配置

### iOS网络权限配置

已在 `ios/Runner/Info.plist` 中添加了必要的网络权限：

```xml
<key>NSAppTransportSecurity</key>
<dict>
    <key>NSAllowsArbitraryLoads</key>
    <false/>
    <key>NSExceptionDomains</key>
    <dict>
        <key>appleid.apple.com</key>
        <dict>
            <key>NSIncludesSubdomains</key>
            <true/>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
        </dict>
        <key>idmsa.apple.com</key>
        <dict>
            <key>NSIncludesSubdomains</key>
            <true/>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
        </dict>
        <key>supabase.co</key>
        <dict>
            <key>NSIncludesSubdomains</key>
            <true/>
            <key>NSExceptionRequiresForwardSecrecy</key>
            <false/>
            <key>NSExceptionMinimumTLSVersion</key>
            <string>TLSv1.2</string>
        </dict>
    </dict>
</dict>
```

## 🧪 网络测试工具

### 内置网络测试功能

应用已集成网络测试工具，可以：

1. **测试DNS解析**
   - 检查域名是否能正确解析
   - 验证网络基础连接

2. **测试HTTPS连接**
   - 验证SSL/TLS连接
   - 检查服务响应状态

3. **实时状态监控**
   - 显示每个服务的连接状态
   - 提供详细的错误信息

### 使用方法

1. **打开调试面板**
   - 在登录界面点击调试按钮

2. **运行网络测试**
   - 点击WiFi图标进行网络测试

3. **查看结果**
   - 查看详细的连接状态
   - 获取问题解决建议

## 🚨 常见网络问题

### 1. **DNS解析失败**
```
错误: appleid.apple.com 无法解析
解决: 检查网络连接，尝试切换WiFi或移动网络
```

### 2. **HTTPS连接失败**
```
错误: SSL/TLS握手失败
解决: 检查网络代理设置，确保TLS 1.2支持
```

### 3. **服务响应超时**
```
错误: 连接超时
解决: 检查网络速度，稍后重试
```

### 4. **防火墙阻止**
```
错误: 连接被拒绝
解决: 检查防火墙设置，确保允许HTTPS连接
```

## 📱 网络环境要求

### 最低要求
- **网络类型**: WiFi或移动网络
- **网络速度**: 至少1Mbps
- **延迟**: 小于500ms
- **稳定性**: 连接稳定，无频繁断线

### 推荐配置
- **网络类型**: 稳定的WiFi连接
- **网络速度**: 5Mbps以上
- **延迟**: 小于200ms
- **稳定性**: 持续稳定的连接

## 🔍 故障排除

### 步骤1: 基础网络检查
```bash
# 测试基本网络连接
ping appleid.apple.com
ping idmsa.apple.com
ping ktqlxbcauxomczubqasp.supabase.co
```

### 步骤2: 使用应用内测试
1. 打开应用登录界面
2. 点击调试按钮
3. 运行网络连接测试
4. 查看详细结果

### 步骤3: 网络环境切换
- 尝试切换WiFi网络
- 尝试使用移动网络
- 检查是否有网络代理

### 步骤4: 设备设置检查
- 确保设备时间正确
- 检查iOS版本是否支持
- 验证网络权限设置

## 💡 优化建议

### 1. **网络连接优化**
- 使用稳定的WiFi连接
- 避免在信号弱的区域使用
- 关闭不必要的网络应用

### 2. **应用设置优化**
- 确保应用有网络权限
- 检查后台应用刷新设置
- 验证位置服务权限

### 3. **系统设置优化**
- 更新iOS到最新版本
- 重置网络设置（如需要）
- 检查VPN设置

## 📞 技术支持

如果网络问题持续存在：

1. **收集信息**
   - 网络测试结果截图
   - 设备型号和iOS版本
   - 网络环境描述

2. **联系支持**
   - 提供详细的错误信息
   - 说明已尝试的解决方案
   - 附上网络测试日志

---

🌐 **网络连接是Apple登录的基础要求**，请确保网络环境稳定后再尝试登录。 