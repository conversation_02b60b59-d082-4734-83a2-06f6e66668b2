import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// Langfuse配置
const LANGFUSE_PUBLIC_KEY = "pk-lf-84abb16e-ff7e-4cb5-b05d-ee11ac542420"
const LANGFUSE_SECRET_KEY = "******************************************"
const LANGFUSE_BASE_URL = "https://cloud.langfuse.com"

// DeepSeek配置
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY')
const DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

// Langfuse工具函数
async function createLangfuseGeneration(traceId, name, input, output, model, metadata = {}) {
  try {
    const auth = btoa(`${LANGFUSE_PUBLIC_KEY}:${LANGFUSE_SECRET_KEY}`)
    const generationId = `gen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    
    const body = {
      id: generationId,
      traceId: traceId,
      name: name,
      startTime: new Date().toISOString(),
      endTime: new Date().toISOString(),
      model: model,
      input: input,
      output: output,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString(),
        source: 'supabase_edge_function'
      }
    }

    const response = await fetch(`${LANGFUSE_BASE_URL}/api/public/generations`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${auth}`
      },
      body: JSON.stringify(body)
    })

    if (response.ok) {
      console.log(`✅ Langfuse Generation记录成功: ${generationId}`)
      return generationId
    } else {
      console.error(`❌ Langfuse Generation记录失败: ${response.status}`)
      return generationId
    }
  } catch (error) {
    console.error(`❌ Langfuse Generation异常: ${error}`)
    return null
  }
}

async function createLangfuseEvent(traceId, name, input, output, metadata = {}) {
  try {
    const auth = btoa(`${LANGFUSE_PUBLIC_KEY}:${LANGFUSE_SECRET_KEY}`)
    
    const body = {
      traceId: traceId,
      name: name,
      startTime: new Date().toISOString(),
      input: input,
      output: output,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString(),
        source: 'supabase_edge_function'
      }
    }

    await fetch(`${LANGFUSE_BASE_URL}/api/public/events`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${auth}`
      },
      body: JSON.stringify(body)
    })

    console.log(`✅ Langfuse Event记录成功: ${name}`)
  } catch (error) {
    console.error(`❌ Langfuse Event异常: ${error}`)
  }
}

// DeepSeek API调用
async function callDeepSeekAPI(prompt, temperature = 0.7, maxTokens = 1000) {
  const requestBody = {
    model: "deepseek-chat",
    messages: [
      {
        role: "system", 
        content: "你是一位专业的塔罗解读师和心理引导师，擅长通过塔罗牌为用户提供深度的心理分析和人生指导。请用简洁而有深度的语言回复，控制在60字以内。"
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: temperature,
    max_tokens: maxTokens,
    stream: false
  }

  const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
    },
    body: JSON.stringify(requestBody)
  })

  if (!response.ok) {
    throw new Error(`DeepSeek API错误: ${response.status} - ${await response.text()}`)
  }

  return await response.json()
}

serve(async (req) => {
  // CORS处理
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS'
      }
    })
  }

  try {
    const { 
      question, 
      cards, 
      spreadType, 
      prompt,
      request_type,
      trace_id,
      temperature = 0.7,
      max_tokens = 1000,
      metadata = {}
    } = await req.json()

    // 创建或使用现有的trace ID
    const traceId = trace_id || `trace_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    const startTime = Date.now()

    // 记录输入事件
    await createLangfuseEvent(
      traceId,
      'edge_function_input',
      { 
        question: question || prompt, 
        cards_count: cards?.length || 0, 
        spread_type: spreadType,
        request_type: request_type
      },
      null,
      { 
        function_name: 'deepseek-tarot-reading',
        ...metadata 
      }
    )

    let finalPrompt = prompt
    
    // 如果是塔罗解读请求，构建完整的prompt
    if (question && cards && spreadType) {
      const cardDescriptions = cards.map(card => 
        `${card.name}${card.isReversed ? '(逆位)' : '(正位)'}: ${card.meaning}`
      ).join('\n')
      
      finalPrompt = `
问题: ${question}
牌阵: ${spreadType}
抽取的卡牌:
${cardDescriptions}

请基于以上信息提供专业的塔罗解读，简洁而有深度，60字以内。
`
    }

    // 调用DeepSeek API
    const deepseekResponse = await callDeepSeekAPI(finalPrompt, temperature, max_tokens)
    const content = deepseekResponse.choices[0].message.content
    const usage = deepseekResponse.usage
    const endTime = Date.now()

    // 记录DeepSeek API调用到Langfuse
    const generationId = await createLangfuseGeneration(
      traceId,
      'deepseek_api_completion',
      { 
        prompt: finalPrompt, 
        model_params: { 
          temperature, 
          max_tokens, 
          model: 'deepseek-chat' 
        } 
      },
      { 
        response: content,
        usage: usage
      },
      'deepseek-chat',
      {
        duration_ms: endTime - startTime,
        api_provider: 'deepseek',
        function_type: request_type || 'tarot_reading',
        ...metadata
      }
    )

    // 记录成功事件
    await createLangfuseEvent(
      traceId,
      'edge_function_success',
      { prompt: finalPrompt },
      { response: content, generation_id: generationId },
      { 
        duration_ms: endTime - startTime,
        token_usage: usage
      }
    )

    return new Response(
      JSON.stringify({
        success: true,
        response: content,
        reading: content, // 向后兼容
        usage: usage,
        trace_id: traceId,
        generation_id: generationId,
        duration_ms: endTime - startTime
      }),
      {
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    )

  } catch (error) {
    console.error('Edge Function错误:', error)
    
    // 记录错误事件到Langfuse
    if (trace_id) {
      await createLangfuseEvent(
        trace_id,
        'edge_function_error',
        await req.json().catch(() => ({})),
        { error: error.message },
        { error_type: error.name }
      )
    }

    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
        trace_id: trace_id
      }),
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*'
        }
      }
    )
  }
}) 