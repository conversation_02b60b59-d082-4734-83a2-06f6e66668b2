# 🧪 Apple订阅沙盒测试完整指南

## 📋 **测试前准备清单**

### ✅ **环境确认**
- [x] 应用已在模拟器中运行
- [x] Products.storekit文件已正确配置
- [x] Flutter代码中的ProductID与App Store Connect一致
- [x] 已登录Apple ID（沙盒环境）

### 🔧 **沙盒环境设置**

#### 1. **创建沙盒测试账号**
1. 访问 [App Store Connect](https://appstoreconnect.apple.com)
2. 进入 **Users and Access** → **Sandbox Testers**
3. 点击 **+** 创建新的沙盒测试账号
4. 填写信息：
   - **First Name**: Test
   - **Last Name**: User
   - **Email**: 使用一个未注册Apple ID的邮箱
   - **Password**: 设置密码
   - **Confirm Password**: 确认密码
   - **Date of Birth**: 选择成年日期
   - **App Store Territory**: 选择美国
   - **Consent**: 勾选同意条款

#### 2. **在模拟器中登录沙盒账号**
1. 在模拟器中打开 **Settings** → **App Store**
2. 点击 **Sign Out**（如果已登录其他账号）
3. 点击 **Sign In**
4. 输入刚创建的沙盒测试账号邮箱和密码
5. 系统会提示这是沙盒环境，点击 **Continue**

## 🧪 **测试步骤**

### **步骤1: 验证产品加载**
1. 在应用中进入订阅页面
2. 观察控制台输出，确认：
   ```
   🔍 查询产品结果:
   ✅ 找到的产品: [com.G3RHCPDDQR.aitarotreading.basic_weekly_usd, ...]
   ❌ 未找到的产品: []
   ```
3. 如果看到"未找到的产品"，检查ProductID是否正确

### **步骤2: 测试基础会员订阅**
1. 选择 **基础会员** → **周付** ($2.99)
2. 点击 **订阅** 按钮
3. 系统会弹出Apple的订阅确认界面
4. 确认价格和订阅条款
5. 点击 **Subscribe** 或 **订阅**
6. 使用沙盒账号完成购买

### **步骤3: 验证订阅激活**
1. 购买成功后，观察控制台输出：
   ```
   ✅ Subscription activated successfully! Tier: basic, Period: weekly
   ✅ 订阅激活成功 - Apple管理订阅状态，本地存储用户权限
   ```
2. 检查应用中的会员状态是否更新
3. 测试会员功能（如塔罗占卜次数限制）

### **步骤4: 测试高级会员订阅**
1. 选择 **高级会员** → **月付** ($17.99)
2. 重复购买流程
3. 验证订阅升级是否成功

### **步骤5: 测试订阅恢复**
1. 在应用中点击 **恢复购买** 按钮
2. 验证之前的订阅是否恢复
3. 检查会员状态是否正确

### **步骤6: 测试使用次数限制**
1. 尝试进行塔罗占卜
2. 验证使用次数是否正确记录
3. 测试达到限制时的提示

## 🔍 **测试检查点**

### **✅ 成功指标**
- [ ] 产品列表正确加载
- [ ] 购买流程顺利完成
- [ ] 订阅状态正确更新
- [ ] 会员权限正确应用
- [ ] 使用次数限制正常工作
- [ ] 恢复购买功能正常

### **❌ 常见问题排查**

#### **问题1: 产品未找到**
```
❌ 未找到的产品: [com.G3RHCPDDQR.aitarotreading.basic_weekly_usd]
```
**解决方案**：
1. 检查App Store Connect中产品状态是否为"Ready for Sale"
2. 确认ProductID拼写正确
3. 等待几分钟让产品同步到沙盒环境

#### **问题2: 购买失败**
```
❌ Purchase failed: [错误信息]
```
**解决方案**：
1. 确认已登录沙盒测试账号
2. 检查沙盒账号是否有足够的余额
3. 确认网络连接正常

#### **问题3: 订阅状态未更新**
```
❌ 订阅激活失败
```
**解决方案**：
1. 检查控制台错误信息
2. 确认购买验证逻辑
3. 检查本地存储权限

## 📱 **真机测试（推荐）**

### **为什么推荐真机测试？**
- 模拟器中的订阅功能有限制
- 真机测试更接近真实用户环境
- 可以测试完整的Apple支付流程

### **真机测试步骤**
1. 在真机上安装应用
2. 登录沙盒测试账号
3. 按照上述步骤进行测试
4. 使用真实的Apple支付流程

## 🎯 **测试完成标准**

### **功能测试**
- ✅ 所有6个订阅产品都能正常加载
- ✅ 基础会员和高级会员都能正常购买
- ✅ 订阅状态正确更新和保存
- ✅ 会员权限正确应用
- ✅ 使用次数限制正常工作
- ✅ 恢复购买功能正常

### **用户体验测试**
- ✅ 购买流程流畅
- ✅ 错误提示清晰
- ✅ 会员状态显示正确
- ✅ 功能限制提示友好

## 📞 **故障排除**

### **如果测试失败**
1. **检查日志** - 查看控制台输出的详细错误信息
2. **验证配置** - 确认ProductID、Bundle ID等配置正确
3. **重启应用** - 有时需要重启应用来刷新状态
4. **清理缓存** - 删除应用重新安装
5. **联系支持** - 如果问题持续，可能需要检查Apple Developer配置

---

🎉 **测试完成后，您的Apple订阅功能就准备好在生产环境中使用了！** 