# 🎯 Bundle ID 配置更新指南

## ✅ **你的 Bundle ID**
```
com.G3RHCPDDQR.aitarotreading
```

## 📱 **已自动更新的文件**
- ✅ `lib/config/supabase_config.dart` - Supabase 配置
- ✅ `ios/Runner/Info.plist` - URL Schemes 配置

## 🛠️ **需要在 Xcode 中手动配置**

### 1. **打开 Xcode 项目**
```bash
open ios/Runner.xcworkspace
```

### 2. **设置 Bundle Identifier**
1. 选择 **Runner** 项目
2. 选择 **Runner** target
3. 在 **General** 标签页中
4. 找到 **Identity** 部分
5. 将 **Bundle Identifier** 改为：
   ```
   com.G3RHCPDDQR.aitarotreading
   ```

### 3. **配置签名**
1. 切换到 **Signing & Capabilities** 标签页
2. 确保 **Team** 选择了你的开发团队
3. **Bundle Identifier** 会自动匹配
4. 添加 **Sign in with Apple** capability：
   - 点击 **+ Capability**
   - 搜索并添加 **Sign in with Apple**

### 4. **验证配置**
确保以下配置正确：
```
✅ Bundle Identifier: com.G3RHCPDDQR.aitarotreading
✅ Team: 你的苹果开发者团队
✅ Capabilities 包含: Sign in with Apple
✅ Provisioning Profile: 匹配你的 Bundle ID
```

## 🚀 **下一步：创建证书**

配置完成后，你需要：

1. **创建开发证书**
   - 在苹果开发者后台选择 "Apple Development"

2. **创建配置文件**
   - 选择你刚创建的 App ID
   - 选择开发证书

3. **测试苹果登录**
   - 在真机上运行应用
   - 测试苹果登录功能

## 📝 **重要提醒**

- Bundle ID 必须在 Xcode 和开发者后台完全一致
- Sign in with Apple 需要在 App ID 中启用
- 需要在真机上测试，模拟器可能不支持苹果登录

配置完成后告诉我，我们继续下一步！🔮✨ 