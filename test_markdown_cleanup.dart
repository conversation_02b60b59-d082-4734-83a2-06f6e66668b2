import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';
import 'package:ai_tarot_reading/services/simple_ai_tarot_service.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  runApp(MarkdownCleanupTestApp());
}

class MarkdownCleanupTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Markdown清理测试',
      theme: ThemeData(primarySwatch: Colors.purple),
      home: MarkdownCleanupTestPage(),
    );
  }
}

class MarkdownCleanupTestPage extends StatefulWidget {
  @override
  _MarkdownCleanupTestPageState createState() => _MarkdownCleanupTestPageState();
}

class _MarkdownCleanupTestPageState extends State<MarkdownCleanupTestPage> {
  String _result = '点击按钮测试Markdown清理...';
  bool _isLoading = false;

  Future<void> _testMarkdownCleanup() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试Markdown清理...';
    });

    try {
      // 创建测试卡牌
      final testCard = TarotCard(
        id: 'test-card',
        name: '愚者',
        description: '新的开始',
        meaning: '代表新的开始和无限可能',
        keywords: ['新开始', '冒险', '纯真'],
        imageUrl: '',
        isMajorArcana: true,
        isReversed: false,
      );
      
      print('🧪 开始测试Markdown清理');
      
      final reading = await SimpleAITarotService.getInitialReading(
        question: '测试Markdown格式清理功能',
        cards: [testCard],
        userLanguage: 'zh',
      );
      
      // 检查是否还有**符号
      final hasMarkdown = reading.contains('**');
      final markdownCount = '**'.allMatches(reading).length;
      
      setState(() {
        _result = '''✅ Markdown清理测试完成!

📊 测试结果:
- 是否包含**符号: ${hasMarkdown ? '是 ❌' : '否 ✅'}
- **符号数量: $markdownCount
- 解读长度: ${reading.length}字

📖 解读内容:
$reading

${hasMarkdown ? '⚠️ 发现未清理的Markdown符号！' : '🎉 Markdown符号已成功清理！'}''';
        _isLoading = false;
      });
      
      print('✅ Markdown清理测试完成');
      print('📊 包含**符号: $hasMarkdown');
      print('📊 **符号数量: $markdownCount');
    } catch (e) {
      setState(() {
        _result = '❌ 测试失败:\n$e';
        _isLoading = false;
      });
      
      print('❌ Markdown清理测试失败: $e');
    }
  }

  Future<void> _testFollowUpCleanup() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试追问Markdown清理...';
    });

    try {
      final testCard = TarotCard(
        id: 'test-card',
        name: '愚者',
        description: '新的开始',
        meaning: '代表新的开始和无限可能',
        keywords: ['新开始', '冒险', '纯真'],
        imageUrl: '',
        isMajorArcana: true,
        isReversed: false,
      );
      
      final previousReading = '''🔮 当前状况
根据愚者牌，我看到您正处在一个新的开始阶段。

💡 深层洞察
您内心有着对未知的好奇和勇气。

🌟 指引建议
保持开放的心态，勇敢迈出第一步。

✨ 能量祝福
愿您在新的旅程中收获成长。''';
      
      print('🧪 开始测试追问Markdown清理');
      
      final followUpReading = await SimpleAITarotService.getFollowUpReading(
        userMessage: '我担心这个新开始会失败，怎么办？',
        previousReading: previousReading,
        cards: [testCard],
        userLanguage: 'zh',
      );
      
      // 检查是否还有**符号
      final hasMarkdown = followUpReading.contains('**');
      final markdownCount = '**'.allMatches(followUpReading).length;
      
      setState(() {
        _result = '''✅ 追问Markdown清理测试完成!

📊 测试结果:
- 是否包含**符号: ${hasMarkdown ? '是 ❌' : '否 ✅'}
- **符号数量: $markdownCount
- 回复长度: ${followUpReading.length}字

📖 追问回复:
$followUpReading

${hasMarkdown ? '⚠️ 发现未清理的Markdown符号！' : '🎉 Markdown符号已成功清理！'}''';
        _isLoading = false;
      });
      
      print('✅ 追问Markdown清理测试完成');
    } catch (e) {
      setState(() {
        _result = '❌ 追问测试失败:\n$e';
        _isLoading = false;
      });
      
      print('❌ 追问Markdown清理测试失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Markdown清理测试'),
        backgroundColor: Colors.purple,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testMarkdownCleanup,
                    child: Text('测试初始解读清理'),
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _testFollowUpCleanup,
                    child: Text('测试追问清理'),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            if (_isLoading)
              CircularProgressIndicator()
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _result,
                      style: TextStyle(fontSize: 14),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
