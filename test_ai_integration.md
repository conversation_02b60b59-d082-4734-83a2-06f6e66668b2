# AI 塔罗解读功能测试指南

## 🧪 测试步骤

### 1. 前置条件检查

确保以下条件已满足：
- ✅ Supabase 项目已配置
- ✅ DeepSeek API 密钥已设置在 Supabase 环境变量中
- ✅ Edge Function `deepseek-tarot-reading` 已部署
- ✅ Flutter 应用已连接到 Supabase

### 2. 基础功能测试

#### 测试初始AI解读
1. 打开应用
2. 选择占卜主题（如"Yes or No"）
3. 输入问题："我今天的运势如何？"
4. 选择自动抽牌或手动选牌
5. 等待AI解读生成

**预期结果**：
- 显示选中的塔罗牌
- 生成包含🔮、✨、🌟、💫四个部分的结构化解读
- 解读内容与选中的牌相关
- 语调温暖专业

#### 测试后续对话
1. 在解读完成后
2. 输入后续问题："你能详细解释一下这张牌的含义吗？"
3. 发送消息

**预期结果**：
- AI基于之前的解读回答
- 回复与上下文相关
- 提供更深入的分析

### 3. 错误处理测试

#### 测试API失败情况
1. 临时在Supabase中删除`DEEPSEEK_API_KEY`环境变量
2. 尝试获取AI解读

**预期结果**：
- 应用不会崩溃
- 显示fallback解读内容
- 解读质量仍然可接受

#### 测试网络问题
1. 关闭网络连接
2. 尝试获取AI解读

**预期结果**：
- 应用显示网络错误
- 提供离线解读选项

### 4. 性能测试

#### 测试响应时间
1. 记录从发送请求到收到回复的时间
2. 多次测试确保稳定性

**预期基准**：
- 初始解读：3-8秒
- 后续对话：2-5秒

#### 测试并发请求
1. 快速连续发送多个问题
2. 观察系统表现

**预期结果**：
- 请求按顺序处理
- 没有请求丢失
- 系统保持稳定

### 5. 内容质量测试

#### 测试不同类型问题
- **爱情问题**："我和他的关系会如何发展？"
- **事业问题**："我应该换工作吗？"
- **财运问题**："我的投资会成功吗？"
- **健康问题**："我最近的身体状况如何？"

#### 测试不同牌阵
- 单张牌
- 三张牌阵
- 凯尔特十字牌阵

**预期结果**：
- 解读与问题类型匹配
- 考虑牌阵的特殊含义
- 提供具体可行的建议

### 6. 边界情况测试

#### 测试特殊输入
- 空问题
- 超长问题（>500字符）
- 特殊字符问题
- 重复问题

#### 测试异常牌组合
- 全部大阿卡纳牌
- 全部逆位牌
- 相互矛盾的牌

**预期结果**：
- 系统优雅处理异常情况
- 提供合理的解读
- 不会出现技术错误

## 🔍 调试技巧

### 查看Supabase日志
1. 登录 Supabase Dashboard
2. 进入 Functions → deepseek-tarot-reading
3. 查看 Logs 标签页
4. 搜索错误信息

### 检查网络请求
在Flutter应用中添加日志：
```dart
print('AI请求数据: $requestData');
print('AI响应结果: ${response.data}');
```

### 验证API密钥
测试DeepSeek API是否可用：
```bash
curl -X POST "https://api.deepseek.com/v1/chat/completions" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "测试"}]
  }'
```

## ✅ 验收标准

AI解读功能通过测试的标准：
- [ ] 成功生成初始解读（90%成功率）
- [ ] 后续对话功能正常
- [ ] Fallback机制工作正常
- [ ] 响应时间在可接受范围内
- [ ] 解读内容质量符合要求
- [ ] 错误处理机制完善
- [ ] 用户体验流畅

## 🚀 上线前检查清单

- [ ] DeepSeek API密钥已正确配置
- [ ] 所有测试用例通过
- [ ] 性能表现满足要求
- [ ] 错误日志监控已设置
- [ ] API使用量监控已配置
- [ ] 用户反馈收集机制已建立

---

完成这些测试后，你的AI塔罗解读功能就可以正式上线使用了！🎉 