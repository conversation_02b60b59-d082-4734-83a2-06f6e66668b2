# 🚀 GrowCard App Store 发布指南

## 📱 **App信息**
- **App名称**: GrowCard
- **Bundle ID**: com.G3RHCPDDQR.aitarotreading
- **版本**: 1.0.1 (Build 2)

## 🎨 **第一步：准备App图标**

### **📍 图标位置**：
```
ios/Runner/Assets.xcassets/AppIcon.appiconset/
```

### **📏 需要替换的图标文件**：

| 文件名 | 实际尺寸 | 用途 |
|--------|----------|------|
| `<EMAIL>` | 1024×1024 | App Store展示 |
| `<EMAIL>` | 180×180 | iPhone主屏幕 |
| `<EMAIL>` | 120×120 | iPhone主屏幕 |
| `<EMAIL>` | 120×120 | Spotlight搜索 |
| `<EMAIL>` | 80×80 | Spotlight搜索 |
| `<EMAIL>` | 40×40 | iPad Spotlight |
| `<EMAIL>` | 87×87 | iPhone设置 |
| `<EMAIL>` | 58×58 | iPhone设置 |
| `<EMAIL>` | 29×29 | iPad设置 |
| `<EMAIL>` | 60×60 | iPhone通知 |
| `<EMAIL>` | 40×40 | iPhone通知 |
| `<EMAIL>` | 20×20 | iPad通知 |
| `<EMAIL>` | 152×152 | iPad主屏幕 |
| `<EMAIL>` | 76×76 | iPad主屏幕 |
| `<EMAIL>` | 167×167 | iPad Pro |

### **🎨 图标设计要求**：
- **格式**: PNG（无透明背景）
- **内容**: 避免文字，使用简洁的图形
- **风格**: 符合iOS设计规范
- **边距**: 留出安全边距，iOS会自动添加圆角

## 🔧 **第二步：构建发布版本**

### **1. 清理项目**：
```bash
flutter clean
flutter pub get
```

### **2. 构建iOS发布版本**：
```bash
flutter build ios --release
```

### **3. 在Xcode中打开项目**：
```bash
open ios/Runner.xcworkspace
```

## 📦 **第三步：Xcode配置**

### **1. 选择发布配置**：
- 选择 `Runner` 项目
- 选择 `Release` 配置
- 选择 `Any iOS Device (arm64)`

### **2. 签名配置**：
- **Team**: 选择你的Apple Developer账户
- **Bundle Identifier**: com.G3RHCPDDQR.aitarotreading
- **Provisioning Profile**: 自动管理或选择发布证书

### **3. 版本信息**：
- **Version**: 1.0.1
- **Build**: 2（每次提交需要递增）

## 🚀 **第四步：上传到App Store Connect**

### **1. Archive构建**：
- 在Xcode中：`Product` → `Archive`
- 等待构建完成

### **2. 上传到App Store**：
- 构建完成后会打开Organizer
- 选择你的Archive
- 点击 `Distribute App`
- 选择 `App Store Connect`
- 选择 `Upload`
- 等待上传完成

## 📝 **第五步：App Store Connect配置**

### **1. 登录App Store Connect**：
- 访问：https://appstoreconnect.apple.com
- 使用你的Apple Developer账户登录

### **2. 创建新App**：
- 点击 `My Apps` → `+` → `New App`
- **Platform**: iOS
- **Name**: GrowCard
- **Primary Language**: 英语（或中文）
- **Bundle ID**: com.G3RHCPDDQR.aitarotreading
- **SKU**: 唯一标识符（如：growcard-2025）

### **3. App信息配置**：

#### **基本信息**：
- **App名称**: GrowCard
- **副标题**: AI Tarot Reading & Manifestation
- **类别**: 生活方式 或 娱乐
- **内容评级**: 4+ (适合所有年龄)

#### **App描述**：
```
GrowCard - Your Personal AI Tarot Guide

Discover your path with AI-powered tarot readings and manifestation tools. GrowCard combines ancient wisdom with modern technology to provide personalized insights for love, career, and personal growth.

Features:
• AI-powered tarot readings with goddess-level guidance
• Multiple spread types for different life questions
• Daily tarot inspiration and manifestation goals
• Beautiful, intuitive interface with mystical design
• Multi-language support
• Personal reading history and progress tracking

Whether you're seeking clarity in relationships, career decisions, or personal development, GrowCard offers the wisdom you need to grow and manifest your best life.

Download GrowCard today and start your journey of self-discovery!
```

#### **关键词**：
```
tarot, AI, manifestation, spirituality, guidance, oracle, divination, self-discovery, personal growth, mystical
```

#### **支持URL**：
- 你的网站或支持页面URL

#### **隐私政策URL**：
- 必须提供隐私政策链接

### **4. 定价和可用性**：
- **价格**: 免费（带内购）
- **可用性**: 全球或选择特定国家/地区

### **5. App审核信息**：
- **联系信息**: 你的邮箱和电话
- **演示账户**: 如果需要登录，提供测试账户
- **备注**: 说明App的主要功能

### **6. 版本信息**：
- **版本号**: 1.0.1
- **版本说明**: "Initial release of GrowCard - AI Tarot Reading App"

## 📸 **第六步：App截图和预览**

### **必需的截图尺寸**：
- **iPhone 6.7"** (iPhone 14 Pro Max): 1290×2796
- **iPhone 6.5"** (iPhone 11 Pro Max): 1242×2688
- **iPhone 5.5"** (iPhone 8 Plus): 1242×2208
- **iPad Pro 12.9"**: 2048×2732

### **截图内容建议**：
1. **主页面** - 显示GrowCard的美丽界面
2. **塔罗解读** - 展示AI解读功能
3. **卡牌选择** - 显示卡牌选择界面
4. **个人资料** - 展示用户功能
5. **历史记录** - 显示解读历史

## ✅ **第七步：提交审核**

### **提交前检查清单**：
- ✅ App图标已更新为GrowCard设计
- ✅ App名称已改为GrowCard
- ✅ 所有开发者工具已移除
- ✅ 功能测试完成
- ✅ 截图已上传
- ✅ App描述已完善
- ✅ 隐私政策已提供
- ✅ 内购产品已配置

### **提交步骤**：
1. 在App Store Connect中选择你的构建版本
2. 填写所有必需信息
3. 点击 `Submit for Review`
4. 等待苹果审核（通常1-7天）

## 📋 **重要注意事项**

### **Apple审核要点**：
- 确保App功能完整且稳定
- 内购功能必须正常工作
- 不能有测试或调试功能
- 隐私政策必须完整
- App描述必须准确

### **常见拒绝原因**：
- 功能不完整或有bug
- 缺少隐私政策
- 内购配置错误
- App描述误导
- 包含测试内容

## 🎯 **下一步行动**

1. **准备GrowCard图标设计**
2. **替换所有图标文件**
3. **测试App功能**
4. **构建发布版本**
5. **上传到App Store Connect**
6. **配置App信息**
7. **提交审核**

需要我帮你进行任何具体步骤吗？
