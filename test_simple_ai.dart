import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/simple_ai_tarot_service.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  print('🧪 开始测试简化版AI塔罗解读...');
  
  // 测试1: 初始解读
  await testInitialReading();
  
  // 测试2: 追问解读
  await testFollowUpReading();
  
  print('✅ 测试完成');
}

Future<void> testInitialReading() async {
  print('\n🔮 测试初始解读...');
  
  try {
    // 获取一张测试卡牌
    final cards = TarotCardsData.getRandomCards(1);
    
    final reading = await SimpleAITarotService.getInitialReading(
      question: '我今天的运势如何？',
      cards: cards,
      userLanguage: 'zh',
    );
    
    print('✅ 初始解读成功:');
    print('   - 卡牌: ${cards.first.name}');
    print('   - 解读长度: ${reading.length}字');
    print('   - 解读预览: ${reading.substring(0, reading.length > 100 ? 100 : reading.length)}...');
    
  } catch (e) {
    print('❌ 初始解读失败: $e');
  }
}

Future<void> testFollowUpReading() async {
  print('\n💬 测试追问解读...');
  
  try {
    final cards = TarotCardsData.getRandomCards(1);
    
    final previousReading = '''🔮 **当前状况**
根据愚者牌，我看到您正处在一个新的开始阶段。

💡 **深层洞察**
您内心有着对未知的好奇和勇气。

🌟 **指引建议**
保持开放的心态，勇敢迈出第一步。

✨ **能量祝福**
愿您在新的旅程中收获成长。''';
    
    final followUpReading = await SimpleAITarotService.getFollowUpReading(
      userMessage: '我有点担心这个新开始会失败，怎么办？',
      previousReading: previousReading,
      cards: cards,
      userLanguage: 'zh',
    );
    
    print('✅ 追问解读成功:');
    print('   - 用户追问: 我有点担心这个新开始会失败，怎么办？');
    print('   - 回复长度: ${followUpReading.length}字');
    print('   - 回复内容: $followUpReading');
    
  } catch (e) {
    print('❌ 追问解读失败: $e');
  }
}
