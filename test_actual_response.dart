import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';
import 'package:ai_tarot_reading/services/simple_ai_tarot_service.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  print('🧪 测试应用实际收到的AI响应...');
  
  // 创建测试卡牌
  final testCard = TarotCard(
    id: 'fool',
    name: 'The Fool',
    description: 'New beginnings',
    meaning: 'New beginnings, innocence, spontaneity',
    keywords: ['new beginnings', 'adventure', 'innocence'],
    imageUrl: '',
    isMajorArcana: true,
    isReversed: false,
  );
  
  try {
    print('📋 测试英文模式...');
    final englishReading = await SimpleAITarotService.getInitialReading(
      question: 'What does my future hold?',
      cards: [testCard],
      userLanguage: 'en',
    );
    
    print('📨 英文响应原文:');
    print('=' * 50);
    print(englishReading);
    print('=' * 50);
    
    // 检查问题
    final hasMarkdown = englishReading.contains('**');
    final hasCardsList = englishReading.contains('The tarot cards you drew are');
    final hasChineseContent = RegExp(r'[\u4e00-\u9fff]').hasMatch(englishReading);
    
    print('🔍 问题检查:');
    print('- 包含**符号: ${hasMarkdown ? "是 ❌" : "否 ✅"}');
    print('- 包含卡牌列表: ${hasCardsList ? "是 ❌" : "否 ✅"}');
    print('- 包含中文内容: ${hasChineseContent ? "是 ❌" : "否 ✅"}');
    
    if (hasMarkdown) {
      print('📝 发现的**符号位置:');
      final matches = RegExp(r'\*\*.*?\*\*').allMatches(englishReading);
      for (final match in matches) {
        print('  - "${match.group(0)}"');
      }
    }
    
    if (hasCardsList) {
      print('📝 发现的卡牌列表:');
      final lines = englishReading.split('\n');
      for (int i = 0; i < lines.length; i++) {
        if (lines[i].contains('The tarot cards you drew are') || 
            lines[i].contains('1.') || 
            lines[i].contains('2.') || 
            lines[i].contains('3.')) {
          print('  第${i+1}行: "${lines[i]}"');
        }
      }
    }
    
  } catch (e) {
    print('❌ 测试失败: $e');
  }
}
