# 📋 App Store Connect配置检查清单

## 🎯 必须完成的配置项

### 1. **Paid Apps Agreement（付费应用协议）** ⚠️ 关键
- [ ] 登录 [App Store Connect](https://appstoreconnect.apple.com)
- [ ] 进入 **Business** 页面
- [ ] 查看 **Agreements, Tax, and Banking** 部分
- [ ] 确认 **Paid Apps Agreement** 状态为 **Active**
- [ ] 如果状态为 **Pending** 或 **Not Accepted**，点击接受协议

**❗ 重要提示**：只有Account Holder（账户持有人）才能接受此协议

### 2. **银行和税务信息**
- [ ] 在Business页面配置银行信息
- [ ] 填写税务信息
- [ ] 确保所有信息状态为 **Approved**

### 3. **应用内购买产品配置**

#### 检查产品状态：
- [ ] 进入应用的 **Features** → **In-App Purchases**
- [ ] 确认以下产品存在且状态为 **Ready for Sale**：
  - `com.G3RHCPDDQR.aitarotreading.basic_weekly_usd`
  - `com.G3RHCPDDQR.aitarotreading.basic_monthly_usd`
  - `com.G3RHCPDDQR.aitarotreading.basic_yearly_usd`
  - `com.G3RHCPDDQR.aitarotreading.premium_weekly_usd`
  - `com.G3RHCPDDQR.aitarotreading.premium_monthly_usd`
  - `com.G3RHCPDDQR.aitarotreading.premium_yearly_usd`

#### 产品详细配置：
- [ ] 每个产品都有正确的价格设置
- [ ] 产品描述完整且准确
- [ ] 订阅群组配置正确
- [ ] 本地化信息完整

### 4. **沙盒测试账号**
- [ ] 进入 **Users and Access** → **Sandbox Testers**
- [ ] 创建至少一个沙盒测试账号
- [ ] 确认测试账号状态为 **Active**
- [ ] 记录测试账号信息用于测试

### 5. **应用版本配置**
- [ ] 确保当前版本包含应用内购买功能
- [ ] 版本状态为 **Ready for Sale** 或 **Pending Review**
- [ ] 应用内购买在版本中正确关联

## 🧪 测试验证步骤

### 步骤1: 沙盒环境测试
1. **设备准备**：
   - [ ] 在iOS设备上退出所有Apple ID
   - [ ] 不要在设置中登录沙盒账号
   - [ ] 只在购买时登录沙盒账号

2. **购买测试**：
   - [ ] 启动应用
   - [ ] 尝试购买基础会员
   - [ ] 在购买弹窗中登录沙盒账号
   - [ ] 完成购买流程
   - [ ] 验证会员状态更新

3. **验证日志**：
   ```
   ✅ 找到产品: AI塔罗基础会员 - $2.99
   🔍 开始验证购买收据...
   ✅ 收据验证成功 - 环境: sandbox
   ✅ Subscription activated successfully!
   ```

### 步骤2: 恢复购买测试
- [ ] 删除并重新安装应用
- [ ] 点击"恢复购买"按钮
- [ ] 验证订阅状态恢复

### 步骤3: 错误处理测试
- [ ] 测试网络断开时的购买
- [ ] 测试用户取消购买
- [ ] 验证错误消息显示正确

## 🔍 常见问题排查

### 问题1: "找不到产品"
**可能原因**：
- [ ] 产品ID拼写错误
- [ ] 产品状态不是"Ready for Sale"
- [ ] Paid Apps Agreement未接受
- [ ] 银行信息未配置

**解决方案**：
1. 检查上述所有配置项
2. 等待几分钟让配置同步
3. 重新测试

### 问题2: "购买失败"
**可能原因**：
- [ ] 沙盒账号问题
- [ ] 网络连接问题
- [ ] 收据验证失败

**解决方案**：
1. 确认使用正确的沙盒账号
2. 检查网络连接
3. 查看控制台错误日志

### 问题3: "收据验证失败"
**可能原因**：
- [ ] Edge Function未部署
- [ ] Supabase配置问题
- [ ] 收据数据格式错误

**解决方案**：
1. 运行 `./deploy_receipt_verification.sh`
2. 检查Supabase函数状态
3. 验证收据数据获取

## 📞 联系支持

如果遇到无法解决的问题：

1. **Apple Developer Support**：
   - 登录开发者账号
   - 提交技术支持请求
   - 提供详细的错误信息

2. **App Store Connect帮助**：
   - 查看官方文档
   - 搜索已知问题
   - 联系Apple支持团队

## ✅ 最终检查

在提交App Store审核前，确认：
- [ ] 所有配置项都已完成
- [ ] 沙盒测试全部通过
- [ ] 错误处理正常工作
- [ ] 用户体验流畅
- [ ] 收据验证功能正常

**🎉 完成所有检查项后，即可重新提交App Store审核！**
