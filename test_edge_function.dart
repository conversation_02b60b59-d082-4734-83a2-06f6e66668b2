import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 简化测试 Edge Function
class TestEdgeFunction extends StatefulWidget {
  const TestEdgeFunction({super.key});

  @override
  State<TestEdgeFunction> createState() => _TestEdgeFunctionState();
}

class _TestEdgeFunctionState extends State<TestEdgeFunction> {
  String _result = '点击按钮测试 Edge Function';
  bool _isLoading = false;

  Future<void> _testEdgeFunction() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试...';
    });

    try {
      print('🧪 开始测试 Edge Function');
      
      final supabase = Supabase.instance.client;
      
      final response = await supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: {
          'question': '测试问题：我的未来如何？',
          'cards': [
            {
              'id': '1',
              'name': '愚者',
              'description': '新的开始',
              'meaning': '代表新的开始和无限可能',
              'keywords': ['新开始', '冒险', '纯真'],
              'isMajorArcana': true,
              'isReversed': false,
            },
            {
              'id': '2', 
              'name': '魔术师',
              'description': '创造力',
              'meaning': '代表创造力和实现目标的能力',
              'keywords': ['创造', '技能', '意志'],
              'isMajorArcana': true,
              'isReversed': false,
            },
            {
              'id': '3',
              'name': '女祭司',
              'description': '直觉智慧',
              'meaning': '代表直觉、智慧和内在知识',
              'keywords': ['直觉', '智慧', '神秘'],
              'isMajorArcana': true,
              'isReversed': false,
            }
          ],
          'spreadType': '三张牌阵',
          'requestType': 'initial_reading',
          'userLanguage': 'zh',
        },
      );

      print('✅ Edge Function 调用成功');
      print('📊 响应数据: ${response.data}');

      if (response.data != null) {
        final data = response.data as Map<String, dynamic>;
        
        if (data['success'] == true) {
          final reading = data['reading'] as String;
          setState(() {
            _result = '✅ 测试成功！\n\nAI解读内容：\n$reading';
          });
          print('🎉 AI解读获取成功: $reading');
        } else {
          setState(() {
            _result = '❌ 测试失败：${data['error'] ?? '未知错误'}';
          });
          print('❌ Edge Function 返回错误: ${data['error']}');
        }
      } else {
        setState(() {
          _result = '❌ 测试失败：Edge Function 返回空数据';
        });
        print('❌ Edge Function 返回空数据');
      }

    } catch (e) {
      setState(() {
        _result = '❌ 测试失败：$e';
      });
      print('❌ 测试异常: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试 Edge Function'),
        backgroundColor: Colors.purple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testEdgeFunction,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      '测试 Edge Function',
                      style: TextStyle(fontSize: 18, color: Colors.white),
                    ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 主函数 - 用于独立测试
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化 Supabase
  await Supabase.initialize(
    url: 'https://ktqlxbcauxomczubqasp.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0cWx4YmNhdXhvbWN6dWJxYXNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Mjc5OTksImV4cCI6MjA2MTQwMzk5fQ.BKL7B2Mbpl1sOsPI5QsX6EmbmHZu_RWPSh8knHkUEno',
  );
  
  runApp(MaterialApp(
    home: const TestEdgeFunction(),
    debugShowCheckedModeBanner: false,
  ));
}
