#!/bin/bash

# 部署收据验证Edge Function到Supabase

echo "🚀 开始部署收据验证Edge Function..."

# 检查是否安装了Supabase CLI
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI未安装"
    echo "请先安装: npm install -g supabase"
    exit 1
fi

# 检查是否已登录
if ! supabase projects list &> /dev/null; then
    echo "❌ 请先登录Supabase CLI"
    echo "运行: supabase login"
    exit 1
fi

# 部署函数
echo "📦 部署verify-receipt函数..."
supabase functions deploy verify-receipt --project-ref ktqlxbcauxomczubqasp

if [ $? -eq 0 ]; then
    echo "✅ 收据验证函数部署成功!"
    echo ""
    echo "🔗 函数URL: https://ktqlxbcauxomczubqasp.supabase.co/functions/v1/verify-receipt"
    echo ""
    echo "📝 使用方法:"
    echo "POST请求到上述URL，请求体包含:"
    echo '{"receiptData": "base64编码的收据数据"}'
    echo ""
    echo "🧪 测试建议:"
    echo "1. 在iOS模拟器或真机上进行购买测试"
    echo "2. 确保使用沙盒测试账号"
    echo "3. 检查控制台输出的验证结果"
else
    echo "❌ 函数部署失败"
    exit 1
fi
