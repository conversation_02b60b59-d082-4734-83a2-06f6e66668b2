import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/l10n/app_localizations_extension.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'dart:ui';
import 'dart:math' as math;

class ModernTarotSelectionScreen extends StatefulWidget {
  const ModernTarotSelectionScreen({super.key});

  @override
  State<ModernTarotSelectionScreen> createState() => _ModernTarotSelectionScreenState();
}

class _ModernTarotSelectionScreenState extends State<ModernTarotSelectionScreen> {
  String selectedSpread = 'Single Card';
  String selectedTheme = 'Classic';
  String question = '';
  
  final List<Map<String, dynamic>> spreads = [
    {
      'name': 'Single Card',
      'description': 'Quick insight for immediate guidance',
      'cards': 1,
      'price': '0.05',
      'icon': Icons.filter_1,
    },
    {
      'name': 'Three Card',
      'description': 'Past, Present, Future reading',
      'cards': 3,
      'price': '0.15',
      'icon': Icons.filter_3,
    },
    {
      'name': 'Celtic Cross',
      'description': 'Comprehensive life analysis',
      'cards': 10,
      'price': '0.35',
      'icon': Icons.add,
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -1.0),
            end: Alignment(1.0, 1.0),
            colors: [
              Color(0xFF667eea),
              Color(0xFF764ba2),
              Color(0xFFf093fb),
              Color(0xFFf5576c),
              Color(0xFF4facfe),
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 🔙 现代化返回按钮
                _buildModernBackButton(),
                
                const SizedBox(height: 24),
                
                // 📱 双栏布局
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 左侧：配置面板
                    Expanded(
                      flex: 1,
                      child: _buildConfigurationPanel(),
                    ),
                    
                    const SizedBox(width: 20),
                    
                    // 右侧：预览面板
                    Expanded(
                      flex: 1,
                      child: _buildPreviewPanel(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 🔙 现代化返回按钮
  Widget _buildModernBackButton() {
    return GestureDetector(
      onTap: () => Navigator.pop(context),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.2),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.arrow_back_ios,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Text(
              context.l10n.navTarot.toUpperCase(),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                letterSpacing: 1,
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.3, end: 0);
  }

  // ⚙️ 配置面板
  Widget _buildConfigurationPanel() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主标题
          _buildGradientButton(
            text: context.l10n.tarotReading,
            isSelected: true,
            width: double.infinity,
          ),
          
          const SizedBox(height: 32),
          
          // 问题输入
          _buildSectionTitle(context.l10n.enterYourQuestion),
          const SizedBox(height: 12),
          _buildQuestionInput(),
          
          const SizedBox(height: 32),
          
          // 牌阵选择
          _buildSectionTitle(context.l10n.selectSpread),
          const SizedBox(height: 12),
          _buildSpreadSelector(),
          
          const SizedBox(height: 32),
          
          // 主题风格
          _buildSectionTitle('Theme Style'),
          const SizedBox(height: 12),
          _buildThemeSelector(),
          
          const SizedBox(height: 40),
          
          // 开始按钮
          _buildGradientButton(
            text: context.l10n.drawCards.toUpperCase(),
            isSelected: true,
            width: double.infinity,
          ),
        ],
      ),
    ).animate().fadeIn(delay: 200.ms, duration: 800.ms).slideY(begin: 0.3, end: 0);
  }

  // 🎴 预览面板
  Widget _buildPreviewPanel() {
    return Column(
      children: [
        // 上方大卡片
        _buildTarotPreviewCard(
          title: selectedSpread,
          subtitle: '${_getSelectedSpread()['cards']} cards • ${_getSelectedSpread()['description']}',
          price: '${_getSelectedSpread()['price']} ETH',
          isLarge: true,
        ),
        
        const SizedBox(height: 20),
        
        // 下方小卡片
        _buildTarotPreviewCard(
          title: 'Daily Insight',
          subtitle: 'Your daily guidance card',
          price: '0.02 ETH',
          isLarge: false,
        ),
      ],
    );
  }

  // 🃏 塔罗预览卡片
  Widget _buildTarotPreviewCard({
    required String title,
    required String subtitle,
    required String price,
    required bool isLarge,
  }) {
    return Container(
      height: isLarge ? 300 : 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.15),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: Stack(
          children: [
            // 背景渐变
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: isLarge
                        ? [
                            const Color(0xFF667eea),
                            const Color(0xFF764ba2),
                            const Color(0xFFf093fb),
                          ]
                        : [
                            const Color(0xFFffd89b),
                            const Color(0xFF19547b),
                          ],
                  ),
                ),
              ),
            ),
            
            // 毛玻璃效果
            Positioned.fill(
              child: BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 8, sigmaY: 8),
                child: Container(
                  color: Colors.white.withOpacity(0.1),
                ),
              ),
            ),
            
            // 边框
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.2),
                    width: 1,
                  ),
                ),
              ),
            ),
            
            // 内容
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 顶部工具栏
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getSelectedSpread()['icon'],
                          color: Colors.white,
                          size: 18,
                        ),
                      ),
                      const Spacer(),
                      const Icon(
                        Icons.favorite_border,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.more_horiz,
                        color: Colors.white,
                        size: 20,
                      ),
                    ],
                  ),
                  
                  const Spacer(),
                  
                  // 底部信息
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.8),
                      fontSize: 13,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Floor price $price',
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.9),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ).animate(delay: isLarge ? 400.ms : 600.ms)
        .fadeIn(duration: 800.ms)
        .scale(begin: const Offset(0.8, 0.8), end: const Offset(1.0, 1.0));
  }

  // 📝 区块标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        color: Colors.white,
        fontSize: 16,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  // ❓ 问题输入框
  Widget _buildQuestionInput() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: TextField(
        onChanged: (value) => setState(() => question = value),
        style: const TextStyle(color: Colors.white),
        decoration: InputDecoration(
          hintText: context.l10n.questionPlaceholder,
          hintStyle: TextStyle(color: Colors.white.withOpacity(0.6)),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        maxLines: 3,
      ),
    );
  }

  // 🎴 牌阵选择器
  Widget _buildSpreadSelector() {
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: spreads.map((spread) {
          final isSelected = selectedSpread == spread['name'];
          return GestureDetector(
            onTap: () => setState(() => selectedSpread = spread['name']),
            child: Container(
              margin: const EdgeInsets.only(bottom: 4),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected 
                    ? Colors.white.withOpacity(0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    spread['icon'],
                    color: Colors.white,
                    size: 18,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          spread['name'],
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        Text(
                          spread['description'],
                          style: TextStyle(
                            color: Colors.white.withOpacity(0.7),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Text(
                    '${spread['price']} ETH',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // 🎨 主题选择器
  Widget _buildThemeSelector() {
    final themes = ['Classic', 'Modern', 'Mystical'];
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: themes.map((theme) {
          final isSelected = selectedTheme == theme;
          return Expanded(
            child: GestureDetector(
              onTap: () => setState(() => selectedTheme = theme),
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? Colors.white.withOpacity(0.2)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  theme,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 13,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // 🌈 渐变按钮
  Widget _buildGradientButton({
    required String text,
    required bool isSelected,
    double? width,
  }) {
    return Container(
      width: width,
      height: 48,
      decoration: BoxDecoration(
        gradient: isSelected
            ? const LinearGradient(
                colors: [
                  Color(0xFF667eea),
                  Color(0xFF764ba2),
                  Color(0xFFf093fb),
                  Color(0xFFffd89b),
                ],
              )
            : LinearGradient(
                colors: [
                  Colors.white.withOpacity(0.2),
                  Colors.white.withOpacity(0.1),
                ],
              ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: const Color(0xFF667eea).withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 6),
                ),
              ]
            : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(24),
          onTap: () {
            if (isSelected && text.contains('DRAW')) {
              _startTarotReading();
            }
          },
          child: Container(
            alignment: Alignment.center,
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                letterSpacing: isSelected ? 1 : 0.5,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 🎯 获取选中的牌阵
  Map<String, dynamic> _getSelectedSpread() {
    return spreads.firstWhere(
      (spread) => spread['name'] == selectedSpread,
      orElse: () => spreads.first,
    );
  }

  // 🚀 开始塔罗解读
  void _startTarotReading() {
    final appState = Provider.of<AppStateProvider>(context, listen: false);
    
    // 设置问题和牌阵类型
    appState.setCurrentQuestion(question);
    
    // 根据选择的牌阵类型设置相应的SpreadType
    SpreadType spreadType;
    switch (selectedSpread) {
      case 'Three Card':
        spreadType = SpreadType.three;
        break;
      case 'Celtic Cross':
        spreadType = SpreadType.celtic;
        break;
      default:
        spreadType = SpreadType.single;
    }
    
    appState.setSpreadType(spreadType);
    
    // 导航到洗牌页面
    Navigator.pop(context);
    
    // 显示成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('${context.l10n.tarotReading} started with $selectedSpread'),
        backgroundColor: const Color(0xFF667eea),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
} 