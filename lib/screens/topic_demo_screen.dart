import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/widgets/rolling_topic_gallery.dart';
import 'package:ai_tarot_reading/widgets/enhanced_rolling_gallery.dart';

class TopicDemoScreen extends StatefulWidget {
  const TopicDemoScreen({super.key});

  @override
  State<TopicDemoScreen> createState() => _TopicDemoScreenState();
}

class _TopicDemoScreenState extends State<TopicDemoScreen> {
  int _selectedDesign = 0;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: AppBar(
        title: Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Text(
              languageManager.translate('choose_your_topic'),
              style: const TextStyle(
                color: Color(0xFF2D3748),
                fontWeight: FontWeight.bold,
              ),
            );
          },
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
      ),
      body: Column(
        children: [
          // 设计选择器
          Container(
            margin: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(15),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _selectedDesign = 0),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: _selectedDesign == 0
                            ? const Color(0xFF8B5CF6)
                            : Colors.transparent,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(15),
                          bottomLeft: Radius.circular(15),
                        ),
                      ),
                      child: Text(
                        '3D滚动（基础版）',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedDesign == 0
                              ? Colors.white
                              : const Color(0xFF6B7280),
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: GestureDetector(
                    onTap: () => setState(() => _selectedDesign = 1),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        color: _selectedDesign == 1
                            ? const Color(0xFF8B5CF6)
                            : Colors.transparent,
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(15),
                          bottomRight: Radius.circular(15),
                        ),
                      ),
                      child: Text(
                        '3D滚动（增强版）',
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: _selectedDesign == 1
                              ? Colors.white
                              : const Color(0xFF6B7280),
                          fontWeight: FontWeight.w600,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 内容区域
          Expanded(
            child: _selectedDesign == 0
                ? Column(
                    children: [
                      const SizedBox(height: 40),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            '滑动或拖拽查看更多专题',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 20),
                      const Expanded(
                        child: RollingTopicGallery(
                          autoplay: true,
                          pauseOnHover: true,
                        ),
                      ),
                    ],
                  )
                : Column(
                    children: [
                      const SizedBox(height: 40),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            '增强版3D滚动 - 支持悬停缩放和流畅拖拽',
                            style: TextStyle(
                              color: const Color(0xFF6B7280),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 20),
                      const Expanded(
                        child: EnhancedRollingGallery(
                          autoplay: true,
                          pauseOnHover: true,
                          dragFactor: 0.05,
                        ),
                      ),
                    ],
                  ),
          ),

          // 底部说明
          Container(
            margin: const EdgeInsets.all(20),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: const Color(0xFF8B5CF6).withOpacity(0.2),
                width: 1,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.info_outline,
                      color: const Color(0xFF8B5CF6),
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '设计说明',
                      style: TextStyle(
                        color: const Color(0xFF8B5CF6),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  _selectedDesign == 0
                      ? '基础版3D滚动：仿照您提供的React代码实现的3D圆柱滚动效果，支持自动旋转、拖拽交互和悬停暂停功能，每个卡片包含多个细分问题。'
                      : '增强版3D滚动：基于Framer Motion概念的高级3D圆柱滚动效果，增加了悬停缩放、流畅拖拽惯性、更精细的动画控制和视觉反馈。',
                  style: TextStyle(
                    color: const Color(0xFF6B7280),
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
