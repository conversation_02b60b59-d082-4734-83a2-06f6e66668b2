import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/chat_reading_screen.dart';
import 'package:ai_tarot_reading/screens/card_shuffle_screen.dart';
import 'package:ai_tarot_reading/screens/manual_card_selection_screen.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';

class SpreadLayoutScreen extends StatefulWidget {
  final String question;
  final String spreadType;
  final List<TarotCard> selectedCards;

  const SpreadLayoutScreen({
    super.key,
    required this.question,
    required this.spreadType,
    required this.selectedCards,
  });

  @override
  State<SpreadLayoutScreen> createState() => _SpreadLayoutScreenState();
}

class _SpreadLayoutScreenState extends State<SpreadLayoutScreen> {
  TarotSpread? currentSpread;
  List<TarotCard?> layoutCards = [];
  bool hasShuffledOnce = false; // 记录是否已经洗过牌
  List<TarotCard>? shuffledCards; // 保存洗牌后的卡牌顺序

  @override
  void initState() {
    super.initState();
    currentSpread = TarotSpread.getSpreadByName(widget.spreadType);
    if (currentSpread != null) {
      layoutCards = List.filled(currentSpread!.cardCount, null);
      // 填充已选择的卡牌
      for (int i = 0; i < widget.selectedCards.length && i < layoutCards.length; i++) {
        layoutCards[i] = widget.selectedCards[i];
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (currentSpread == null) {
      return Scaffold(
        appBar: AppBar(title: const Text('牌阵布局')),
        body: const Center(child: Text('未找到对应的牌阵')),
      );
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -1.0),
                  end: Alignment(1.0, 1.0),
                  colors: [
                    Color(0xFF87CEEB),
                    Color(0xFFE6E6FA),
                    Color(0xFFF8BBD9),
                    Color(0xFFE6E6FA),
                    Color(0xFF87CEEB),
                  ],
                ),
              ),
            ),
          ),

          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: FigmaTheme.textPrimary,
                          size: 24,
                        ),
                      ),
                      const Spacer(),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            _getLocalizedSpreadName(languageManager, currentSpread!.name),
                            style: const TextStyle(
                              color: FigmaTheme.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                      const Spacer(),
                      // 🎯 手动选牌按钮
                      GestureDetector(
                        onTap: _showManualCardSelection,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: FigmaTheme.textPrimary.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              final manualSelectText = languageManager.translate('manual_select');
                              // 检查是否是西班牙语的"Seleccion Manual"，需要换行
                              final isSpanish = manualSelectText.contains('Seleccion Manual');

                              if (isSpanish) {
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.touch_app,
                                          color: FigmaTheme.textPrimary,
                                          size: 16,
                                        ),
                                        const SizedBox(width: 4),
                                        const Text(
                                          'Seleccion',
                                          style: TextStyle(
                                            color: FigmaTheme.textPrimary,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const Text(
                                      'Manual',
                                      style: TextStyle(
                                        color: FigmaTheme.textPrimary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                );
                              } else {
                                return Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.touch_app,
                                      color: FigmaTheme.textPrimary,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      manualSelectText,
                                      style: const TextStyle(
                                        color: FigmaTheme.textPrimary,
                                        fontSize: 12,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                );
                              }
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 牌阵布局区域
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(20),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // 🎯 添加牌阵标题
                        Padding(
                          padding: const EdgeInsets.only(bottom: 20),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return Column(
                                children: [
                                  Text(
                                    _formatSpreadNameForDisplay(_getLocalizedSpreadName(languageManager, currentSpread!.name)),
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: FigmaTheme.textPrimary,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 2, // 允许两行显示
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    languageManager.translate('silently_think_question'),
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: FigmaTheme.textPrimary.withValues(alpha: 0.7),
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    languageManager.translate('draw_your_cards').replaceAll('{count}', '${currentSpread!.cardCount}'),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Color(0xFF8B5CF6),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                        // 🎯 卡槽区域 - 上下居中
                        Expanded(
                          child: Center(
                            child: SingleChildScrollView(
                              child: _buildSpreadLayout(),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // 底部按钮区域
                Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      // 进度提示
                      if (!_isLayoutComplete())
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Container(
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                              margin: const EdgeInsets.only(bottom: 16),
                              decoration: BoxDecoration(
                                color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  const Icon(
                                    Icons.info_outline,
                                    color: Color(0xFF8B5CF6),
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    languageManager.translate('cards_selected_progress')
                                        .replaceAll('{selected}', '${layoutCards.where((card) => card != null).length}')
                                        .replaceAll('{total}', '${currentSpread!.cardCount}'),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: Color(0xFF8B5CF6),
                                    ),
                                  ),
                                ],
                              ),
                            );
                          },
                        ),

                      // 开始解读按钮
                      if (_isLayoutComplete())
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton(
                                onPressed: _startReading,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF8B5CF6),
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Text(
                                  languageManager.translate('start_reading'),
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getLocalizedSpreadName(LanguageManager languageManager, String spreadName) {
    switch (spreadName) {
      case '单张牌阵':
        return languageManager.translate('single_card_spread');
      case '三张牌':
        return languageManager.translate('three_card_spread');
      case '三张牌阵（经典）':
        return languageManager.translate('three_card_spread_classic');
      case '六芒星牌阵':
        return languageManager.translate('six_point_star_spread');
      case '凯尔特十字':
        return languageManager.translate('celtic_cross_spread');
      default:
        return spreadName;
    }
  }

  // 格式化牌阵名称显示，让括号内容换行
  String _formatSpreadNameForDisplay(String spreadName) {
    print('🔍 格式化牌阵名称: "$spreadName"');

    // 处理各种语言的括号格式，让括号内容换行
    final patterns = [
      RegExp(r'^(.+?)\s*\((.+?)\)$'), // 英文括号: Three Card Spread (Classic), Tirada de Tres Cartas (Clásica)
      RegExp(r'^(.+?)\s*（(.+?)）$'), // 中文括号: 三张牌阵（经典）, 3枚カードスプレッド（クラシック）
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(spreadName);
      if (match != null) {
        final mainPart = match.group(1)?.trim() ?? '';
        final bracketPart = match.group(2)?.trim() ?? '';
        final result = '$mainPart\n($bracketPart)';
        print('✅ 匹配成功: "$mainPart" + "($bracketPart)" = "$result"');
        return result;
      }
    }

    print('❌ 没有匹配到括号格式，返回原文');
    return spreadName; // 如果没有匹配到括号格式，返回原文
  }

  Widget _buildSpreadLayout() {
    switch (currentSpread!.name) {
      case '单张牌阵':
        return _buildSingleCardLayout();
      case '三张牌':
        return _buildThreeCardLayout();
      case '六芒星牌阵':
        return _buildSixPointStarLayout();
      case '凯尔特十字':
        return _buildCelticCrossLayout();
      default:
        return _buildGenericLayout();
    }
  }

  Widget _buildSingleCardLayout() {
    // 单张牌阵：确保上下左右都完全居中，使用较小的尺寸
    return SizedBox(
      height: 400, // 🎯 给一个固定高度，确保有足够空间居中
      child: Center(
        child: _buildCardPosition(0, currentSpread!.positionKeys[0]),
      ),
    );
  }

  Widget _buildThreeCardLayout() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        for (int i = 0; i < 3; i++)
          _buildCardPosition(i, currentSpread!.positionKeys[i]),
      ],
    );
  }

  Widget _buildSixPointStarLayout() {
    return Column(
      children: [
        // 第一行：1张牌
        _buildCardPosition(0, currentSpread!.positionKeys[0]),
        const SizedBox(height: 20),
        // 第二行：2张牌
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCardPosition(1, currentSpread!.positionKeys[1]),
            _buildCardPosition(2, currentSpread!.positionKeys[2]),
          ],
        ),
        const SizedBox(height: 20),
        // 第三行：2张牌
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildCardPosition(3, currentSpread!.positionKeys[3]),
            _buildCardPosition(4, currentSpread!.positionKeys[4]),
          ],
        ),
        const SizedBox(height: 20),
        // 第四行：1张牌
        _buildCardPosition(5, currentSpread!.positionKeys[5]),
      ],
    );
  }

  Widget _buildCelticCrossLayout() {
    // 简化的凯尔特十字布局
    return Column(
      children: [
        for (int row = 0; row < 5; row++)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                for (int col = 0; col < 2; col++)
                  if (row * 2 + col < currentSpread!.cardCount)
                    _buildCardPosition(
                      row * 2 + col,
                      currentSpread!.positionKeys[row * 2 + col],
                    )
                  else
                    const SizedBox(width: 100),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildGenericLayout() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      alignment: WrapAlignment.center,
      children: [
        for (int i = 0; i < currentSpread!.cardCount; i++)
          _buildCardPosition(i, currentSpread!.positionKeys[i]),
      ],
    );
  }

  Widget _buildCardPosition(int index, String positionKey) {
    final card = layoutCards[index];

    return Column(
      children: [
        GestureDetector(
          onTap: card == null ? () => _selectCardForPosition(index) : null,
          child: Container(
            width: 70, // 减小宽度，从80改为70
            height: 105, // 减小高度，从120改为105
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: card != null
                    ? const Color(0xFF8B5CF6)
                    : Colors.grey.withValues(alpha: 0.3),
                width: 2,
              ),
              color: card != null
                  ? Colors.white
                  : Colors.grey.withValues(alpha: 0.1),
            ),
            child: card != null
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Image.asset(
                      card.getCorrectImageUrl(),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            gradient: const LinearGradient(
                              colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
                            ),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.auto_awesome,
                              color: Colors.white,
                              size: 32,
                            ),
                          ),
                        );
                      },
                    ),
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '${index + 1}',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.withValues(alpha: 0.5),
                          ),
                        ),
                        const SizedBox(height: 4),
                        const Icon(
                          Icons.add_circle_outline,
                          color: Color(0xFF8B5CF6),
                          size: 20,
                        ),
                      ],
                    ),
                  ),
          ),
        ).animate().fadeIn(delay: (index * 100).ms),
        const SizedBox(height: 16), // 增加间距，从8改为16
        // 自适应文字容器
        Container(
          constraints: const BoxConstraints(
            minWidth: 70, // 调整最小宽度，与卡牌宽度一致
            maxWidth: 140, // 调整最大宽度
          ),
          child: Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                languageManager.translate(positionKey),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF6B7280),
                ),
                textAlign: TextAlign.center,
                maxLines: 3, // 允许最多3行
                overflow: TextOverflow.ellipsis, // 防止文字溢出
              );
            },
          ),
        ),
      ],
    );
  }

  void _selectCardForPosition(int positionIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardShuffleScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          currentPosition: positionIndex,
          existingCards: layoutCards.where((card) => card != null).cast<TarotCard>().toList(),
          skipShuffle: hasShuffledOnce, // 如果已经洗过牌，跳过洗牌动画
          shuffledCards: shuffledCards, // 传递洗牌后的卡牌顺序
          onCardSelected: (selectedCard) {
            setState(() {
              layoutCards[positionIndex] = selectedCard;
              hasShuffledOnce = true; // 标记已经洗过牌
              // 如果是第一次选牌，保存洗牌后的卡牌顺序
              shuffledCards ??= List<TarotCard>.from(TarotCardsData.allCards)..shuffle();
            });
          },
        ),
      ),
    );
  }

  // 🎯 手动选牌功能 - 直接进入牌库
  void _showManualCardSelection() {
    print('🎯 点击手动选牌按钮');

    // 计算剩余需要选择的牌数
    final selectedCount = layoutCards.where((card) => card != null).length;
    final remainingCount = currentSpread!.cardCount - selectedCount;

    print('🎯 已选择: $selectedCount, 剩余: $remainingCount');

    if (remainingCount <= 0) {
      // 如果已经选择了所有牌，直接开始解读
      _startReading();
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManualCardSelectionScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          cardCount: remainingCount, // 只选择剩余的牌数
          returnToLayout: true, // 设置为返回到牌阵布局页面
        ),
      ),
    ).then((result) {
      // 当从手动选牌页面返回时，检查是否有选择的牌
      if (result != null && result is List<TarotCard>) {
        setState(() {
          // 将选择的牌填入空位
          int emptyIndex = 0;
          for (int i = 0; i < result.length; i++) {
            // 找到下一个空位
            while (emptyIndex < layoutCards.length && layoutCards[emptyIndex] != null) {
              emptyIndex++;
            }
            if (emptyIndex < layoutCards.length) {
              layoutCards[emptyIndex] = result[i];
              emptyIndex++;
            }
          }
        });

        // 如果所有牌都选择完了，自动开始解读
        if (_isLayoutComplete()) {
          _startReading();
        }
      }
    });
  }

  bool _isLayoutComplete() {
    return layoutCards.every((card) => card != null);
  }

  void _startReading() {
    // 获取实际选择的卡牌对象（不是ID）
    final actualSelectedCards = layoutCards
        .where((card) => card != null)
        .cast<TarotCard>()
        .toList();

    print('🚀 开始解读，实际选择的卡牌数量: ${actualSelectedCards.length}');
    for (int i = 0; i < actualSelectedCards.length; i++) {
      print('🃏 卡牌${i + 1}: ${actualSelectedCards[i].name}${actualSelectedCards[i].isReversed ? "(逆位)" : "(正位)"}');
    }

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ChatReadingScreen(
          question: widget.question,
          selectedCards: actualSelectedCards, // 使用实际选择的卡牌对象
          spreadType: widget.spreadType,
        ),
      ),
    );
  }
}
