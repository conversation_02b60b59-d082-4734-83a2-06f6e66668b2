import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/chat_reading_screen.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';

class ManualCardSelectionScreen extends StatefulWidget {
  final String question;
  final String spreadType;
  final int cardCount;
  final bool returnToLayout; // 新增：是否返回到牌阵布局页面

  const ManualCardSelectionScreen({
    super.key,
    required this.question,
    required this.spreadType,
    required this.cardCount,
    this.returnToLayout = false, // 默认不返回，直接进入解读
  });

  @override
  State<ManualCardSelectionScreen> createState() => _ManualCardSelectionScreenState();
}

class _ManualCardSelectionScreenState extends State<ManualCardSelectionScreen> {
  List<String> selectedCards = [];
  TarotSpread? currentSpread;
  String searchQuery = '';
  List<TarotCardOption> filteredCardOptions = []; // 改为卡牌选项列表
  int selectedTabIndex = 0; // 0: 正位, 1: 逆位

  @override
  void initState() {
    super.initState();
    currentSpread = TarotSpread.getSpreadByName(widget.spreadType);
    _updateFilteredCards();
  }

  /// 更新过滤后的卡牌列表
  void _updateFilteredCards() {
    final filteredOptions = <TarotCardOption>[];
    final isReversed = selectedTabIndex == 1; // 0: 正位, 1: 逆位

    // 使用完整的78张塔罗牌数据（TarotCardsData）
    final allCards = TarotCardsData.allCards;

    for (final card in allCards) {
      // 根据搜索条件过滤
      if (searchQuery.isEmpty ||
          card.name.toLowerCase().contains(searchQuery.toLowerCase()) ||
          card.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
          card.keywords.any((keyword) => keyword.toLowerCase().contains(searchQuery.toLowerCase()))) {
        // 只添加当前选中标签对应的版本
        filteredOptions.add(TarotCardOption(card: card, isReversed: isReversed));
      }
    }

    setState(() {
      filteredCardOptions = filteredOptions;
    });
  }

  void _filterCards(String query) {
    setState(() {
      searchQuery = query;
      _updateFilteredCards();
    });
  }

  void _onTabChanged(int index) {
    setState(() {
      selectedTabIndex = index;
      _updateFilteredCards();
    });
  }

  void _selectCardOption(TarotCardOption cardOption) {
    setState(() {
      if (selectedCards.contains(cardOption.id)) {
        selectedCards.remove(cardOption.id);
      } else if (selectedCards.length < widget.cardCount) {
        selectedCards.add(cardOption.id);
      }
    });
  }

  void _confirmSelection() {
    if (selectedCards.length == widget.cardCount) {
      print('🔄 开始转换选中的卡牌...');
      print('📋 选中的卡牌ID: $selectedCards');

      // 将选中的卡牌ID转换为TarotCard对象
      final selectedTarotCards = <TarotCard>[];

      for (final cardId in selectedCards) {
        final card = TarotCardsData.getCardById(cardId);
        if (card != null) {
          selectedTarotCards.add(card);
          print('✅ 成功转换卡牌: ${card.name}${card.isReversed ? "(逆位)" : "(正位)"}');
        } else {
          print('❌ 无法找到卡牌ID: $cardId');
        }
      }

      print('🃏 最终转换的卡牌数量: ${selectedTarotCards.length}/${widget.cardCount}');

      // 确保有足够的卡牌
      if (selectedTarotCards.length != widget.cardCount) {
        print('⚠️ 卡牌数量不匹配，预期: ${widget.cardCount}, 实际: ${selectedTarotCards.length}');
        // 显示错误提示
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('选择的卡牌有误，请重新选择'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      // 根据参数决定是返回还是跳转
      if (widget.returnToLayout) {
        // 返回选择的牌给牌阵布局页面
        print('🔙 返回选择的牌给牌阵布局页面');
        Navigator.pop(context, selectedTarotCards);
      } else {
        // 直接进入解读页面（完整的手动选牌流程）
        print('🚀 直接进入解读页面');
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(
            builder: (context) => ChatReadingScreen(
              question: widget.question,
              selectedCards: selectedTarotCards,
              spreadType: widget.spreadType,
            ),
          ),
        );
      }
    }
  }

  String _getCurrentPositionHint(LanguageManager languageManager) {
    if (currentSpread == null || selectedCards.length >= currentSpread!.positionKeys.length) {
      return '';
    }
    final nextPositionKey = currentSpread!.positionKeys[selectedCards.length];
    final translatedPosition = languageManager.translate(nextPositionKey);
    return '${languageManager.translate('next_card_represents')}：$translatedPosition';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),

          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.9,
                    radius: 0,
                  ),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: FigmaTheme.textPrimary,
                          size: 24,
                        ),
                      ),
                      Expanded(
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            final manualSelectText = languageManager.translate('manual_select');
                            // 检查是否是西班牙语的"Seleccion Manual"，需要换行
                            final isSpanish = manualSelectText.contains('Seleccion Manual');

                            if (isSpanish) {
                              return Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Text(
                                    'Seleccion',
                                    style: TextStyle(
                                      color: FigmaTheme.textPrimary,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const Text(
                                    'Manual',
                                    style: TextStyle(
                                      color: FigmaTheme.textPrimary,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ],
                              );
                            } else {
                              return Text(
                                manualSelectText,
                                style: const TextStyle(
                                  color: FigmaTheme.textPrimary,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              );
                            }
                          },
                        ),
                      ),
                      const SizedBox(width: 48),
                    ],
                  ),
                ),

                // 搜索栏
                Container(
                  margin: const EdgeInsets.all(16),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.8,
                    radius: 16,
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return TextField(
                        onChanged: _filterCards,
                        decoration: InputDecoration(
                          hintText: languageManager.translate('search_tarot_cards'),
                          hintStyle: const TextStyle(color: FigmaTheme.textMuted),
                          prefixIcon: const Icon(Icons.search, color: FigmaTheme.textMuted),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(16),
                            borderSide: BorderSide.none,
                          ),
                          filled: true,
                          fillColor: Colors.transparent,
                          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        ),
                      );
                    },
                  ),
                ),

                // 正位/逆位标签选择器
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.8,
                    radius: 16,
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Row(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: () => _onTabChanged(0),
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  color: selectedTabIndex == 0
                                      ? Colors.green.withOpacity(0.2)
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    bottomLeft: Radius.circular(16),
                                  ),
                                  border: selectedTabIndex == 0
                                      ? Border.all(color: Colors.green, width: 2)
                                      : null,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_upward,
                                      color: selectedTabIndex == 0 ? Colors.green : FigmaTheme.textMuted,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      languageManager.translate('upright_position'),
                                      style: TextStyle(
                                        color: selectedTabIndex == 0 ? Colors.green : FigmaTheme.textMuted,
                                        fontWeight: selectedTabIndex == 0 ? FontWeight.w600 : FontWeight.normal,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          Container(
                            width: 1,
                            height: 40,
                            color: FigmaTheme.textMuted.withOpacity(0.3),
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () => _onTabChanged(1),
                              child: Container(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                decoration: BoxDecoration(
                                  color: selectedTabIndex == 1
                                      ? Colors.red.withOpacity(0.2)
                                      : Colors.transparent,
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(16),
                                    bottomRight: Radius.circular(16),
                                  ),
                                  border: selectedTabIndex == 1
                                      ? Border.all(color: Colors.red, width: 2)
                                      : null,
                                ),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.arrow_downward,
                                      color: selectedTabIndex == 1 ? Colors.red : FigmaTheme.textMuted,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      languageManager.translate('reversed_position'),
                                      style: TextStyle(
                                        color: selectedTabIndex == 1 ? Colors.red : FigmaTheme.textMuted,
                                        fontWeight: selectedTabIndex == 1 ? FontWeight.w600 : FontWeight.normal,
                                        fontSize: 16,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // 选择进度和提示
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(16),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.8,
                    radius: 16,
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                languageManager.translate('cards_selected_progress')
                                    .replaceAll('{selected}', '${selectedCards.length}')
                                    .replaceAll('{total}', '${widget.cardCount}'),
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: FigmaTheme.textPrimary,
                                ),
                              ),
                              if (selectedCards.length == widget.cardCount)
                                ElevatedButton(
                                  onPressed: _confirmSelection,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: FigmaTheme.primaryPink,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                  ),
                                  child: Text(
                                    languageManager.translate('confirm_selection'),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                            ],
                          ),
                          if (_getCurrentPositionHint(languageManager).isNotEmpty) ...[
                            const SizedBox(height: 8),
                            Text(
                              _getCurrentPositionHint(languageManager),
                              style: TextStyle(
                                fontSize: 14,
                                color: FigmaTheme.textPrimary.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ],
                      );
                    },
                  ),
                ),

                // 说明文字
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  padding: const EdgeInsets.all(12),
                  decoration: FigmaTheme.createGlassDecoration(
                    opacity: 0.8,
                    radius: 16,
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('manual_selection_instruction'),
                        style: TextStyle(
                          fontSize: 14,
                          color: FigmaTheme.textPrimary.withOpacity(0.8),
                        ),
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                ),

                const SizedBox(height: 16),

                // 塔罗牌网格
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 16,
                    ),
                    child: GridView.builder(
                      padding: const EdgeInsets.all(16),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        childAspectRatio: 2.0 / 3.0, // 统一使用优雅版的宽高比
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                      ),
                      itemCount: filteredCardOptions.length,
                      itemBuilder: (context, index) {
                        final cardOption = filteredCardOptions[index];
                        final isSelected = selectedCards.contains(cardOption.id);

                        return GestureDetector(
                          onTap: () => _selectCardOption(cardOption),
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: isSelected
                                  ? Border.all(color: FigmaTheme.primaryPink, width: 3)
                                  : Border.all(color: Colors.grey.withValues(alpha: 0.3)),
                              boxShadow: [
                                BoxShadow(
                                  color: isSelected
                                      ? FigmaTheme.primaryPink.withValues(alpha: 0.3)
                                      : Colors.black.withValues(alpha: 0.1),
                                  blurRadius: isSelected ? 12 : 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(12),
                              child: Stack(
                                children: [
                                  // 卡牌图片
                                  Positioned.fill(
                                    child: Transform(
                                      alignment: Alignment.center,
                                      transform: Matrix4.identity()
                                        ..rotateZ(cardOption.isReversed ? 3.14159 : 0), // 逆位时旋转180度
                                      child: Image.asset(
                                        cardOption.card.getCorrectImageUrl(),
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                        errorBuilder: (context, error, stackTrace) {
                                          return Container(
                                            color: Colors.grey[200],
                                            child: Column(
                                              mainAxisAlignment: MainAxisAlignment.center,
                                              children: [
                                                Icon(
                                                  Icons.auto_awesome,
                                                  size: 24,
                                                  color: FigmaTheme.textMuted,
                                                ),
                                                const SizedBox(height: 4),
                                                Text(
                                                  cardOption.card.name,
                                                  style: const TextStyle(
                                                    fontSize: 8,
                                                    color: FigmaTheme.textMuted,
                                                  ),
                                                  textAlign: TextAlign.center,
                                                  maxLines: 2,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ),

                                  // 正位/逆位指示器
                                  Positioned(
                                    top: 4,
                                    right: 4,
                                    child: Consumer<LanguageManager>(
                                      builder: (context, languageManager, child) {
                                        return Container(
                                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                          decoration: BoxDecoration(
                                            color: cardOption.isReversed
                                              ? Colors.red.withOpacity(0.8)
                                              : Colors.green.withOpacity(0.8),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Text(
                                            cardOption.isReversed
                                                ? languageManager.translate('reversed')
                                                : languageManager.translate('upright'),
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 8,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        );
                                      },
                                    ),
                                  ),

                                  // 卡牌名称（底部）
                                  Positioned(
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    child: Container(
                                      padding: const EdgeInsets.all(4),
                                      decoration: BoxDecoration(
                                        color: isSelected
                                            ? FigmaTheme.primaryPink.withOpacity(0.9)
                                            : Colors.black.withOpacity(0.7),
                                        borderRadius: const BorderRadius.only(
                                          bottomLeft: Radius.circular(12),
                                          bottomRight: Radius.circular(12),
                                        ),
                                      ),
                                      child: Consumer<LanguageManager>(
                                        builder: (context, languageManager, child) {
                                          return Text(
                                            cardOption.getDisplayName(languageManager),
                                            style: const TextStyle(
                                              fontSize: 8,
                                              fontWeight: FontWeight.w600,
                                              color: Colors.white,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          );
                                        },
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ).animate().scale(
                            duration: 200.ms,
                            curve: Curves.easeOut,
                          ),
                        );
                      },
                    ),
                  ),
                ),

                const SizedBox(height: 16),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 塔罗牌选项（包含正位和逆位）
class TarotCardOption {
  final TarotCard card;
  final bool isReversed;

  TarotCardOption({
    required this.card,
    required this.isReversed,
  });

  String get id => '${card.id}_${isReversed ? 'reversed' : 'upright'}';
  String get displayName => isReversed ? '${card.name}（逆位）' : '${card.name}（正位）';

  String getDisplayName(LanguageManager languageManager) {
    final positionText = isReversed
        ? languageManager.translate('reversed_position')
        : languageManager.translate('upright_position');
    return '${card.name}（$positionText）';
  }
}
