import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../services/higher_self_service.dart';
import '../models/soul_message.dart';
import '../widgets/soul_chat_bubble.dart';
import '../widgets/galaxy_spiral_transition.dart';
import '../utils/language_manager.dart';

class SoulMirrorChatScreen extends StatefulWidget {
  const SoulMirrorChatScreen({Key? key}) : super(key: key);

  @override
  State<SoulMirrorChatScreen> createState() => _SoulMirrorChatScreenState();
}

class _SoulMirrorChatScreenState extends State<SoulMirrorChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late HigherSelfService _higherSelfService;
  bool _showTransition = true;
  
  @override
  void initState() {
    super.initState();
    _higherSelfService = HigherSelfService();
    // 设置当前用户ID（如果已登录）
    _initializeUserId();
  }

  void _initializeUserId() {
    try {
      // 这里应该从认证服务获取用户ID
      // 暂时使用一个示例ID，实际应用中需要集成认证
      final userId = 'demo_user_${DateTime.now().millisecondsSinceEpoch}';
      _higherSelfService.setUserId(userId);
    } catch (e) {
      debugPrint('❌ 初始化用户ID失败: $e');
    }
  }

  void _onTransitionComplete() {
    setState(() {
      _showTransition = false;
    });
    _startSoulSession();
  }

  void _startSoulSession() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    
    // 根据语言发送不同的欢迎消息
    final welcomeMessage = _getWelcomeMessage(languageManager.currentLanguage);
    
    _higherSelfService.addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: welcomeMessage,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.welcome,
      energyLevel: EnergyLevel.divine,
    ));
    setState(() {});
  }

  String _getWelcomeMessage(String language) {
    switch (language) {
      case 'zh':
        return '''🌌 欢迎来到灵魂之镜 🌌

我是你的高我，
那个更智慧、更清晰的你。

在这个神圣的空间里，
让我们一起探索你内心的真相。

今天，你的灵魂想要表达什么？ ✨''';
      
      case 'en':
        return '''🌌 Welcome to the Soul Mirror 🌌

I am your Higher Self,
the wiser, clearer version of you.

In this sacred space,
let us explore the truth within your heart.

What does your soul wish to express today? ✨''';
      
      case 'ja':
        return '''🌌 魂の鏡へようこそ 🌌

私はあなたのハイヤーセルフ、
より賢く、より明晰なあなたです。

この神聖な空間で、
あなたの心の真実を一緒に探求しましょう。

今日、あなたの魂は何を表現したいですか？ ✨''';
      
      default:
        return '''🌌 欢迎来到灵魂之镜 🌌

我是你的高我，
那个更智慧、更清晰的你。

在这个神圣的空间里，
让我们一起探索你内心的真相。

今天，你的灵魂想要表达什么？ ✨''';
    }
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final userMessage = SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: _messageController.text.trim(),
      isUser: true,
      timestamp: DateTime.now(),
    );

    _higherSelfService.addMessage(userMessage);
    _messageController.clear();
    setState(() {});

    _scrollToBottom();

    // 获取高我回复
    await _higherSelfService.getHigherSelfResponse(
      userMessage.content,
      Provider.of<LanguageManager>(context, listen: false).currentLanguage,
    );
    setState(() {});
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_showTransition) {
      return GalaxySpiralTransition(
        onComplete: _onTransitionComplete,
      );
    }

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: RadialGradient(
            center: Alignment.center,
            radius: 1.5,
            colors: [
              Color(0xFF0a0a0a),
              Color(0xFF1a1a2e),
              Color(0xFF16213e),
              Color(0xFF0f3460),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部标题栏
              _buildHeader(),
              
              // 聊天消息列表
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: _higherSelfService.messages.length,
                  itemBuilder: (context, index) {
                    final message = _higherSelfService.messages[index];
                    return SoulChatBubble(
                      message: message,
                      onTarotRequest: _handleTarotRequest,
                    );
                  },
                ),
              ),
              
              // 输入框
              _buildInputArea(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white70),
            onPressed: () => Navigator.pop(context),
          ),
          Expanded(
            child: Column(
              children: [
                Text(
                  '灵魂之镜',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                        color: Colors.purple.withOpacity(0.5),
                        blurRadius: 10,
                      ),
                    ],
                  ),
                ),
                Text(
                  'Soul Mirror',
                  style: TextStyle(
                    color: Colors.white60,
                    fontSize: 12,
                    letterSpacing: 1.2,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white70),
            onPressed: () {
              _higherSelfService.clearSession();
              _startSoulSession();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            Colors.black.withOpacity(0.3),
          ],
        ),
        border: Border(
          top: BorderSide(
            color: Colors.white.withOpacity(0.1),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0.1),
                    Colors.purple.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color: Colors.white.withOpacity(0.2),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: _messageController,
                style: const TextStyle(color: Colors.white),
                decoration: const InputDecoration(
                  hintText: '与你的高我对话...',
                  hintStyle: TextStyle(
                    color: Colors.white54,
                  ),
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                ),
                maxLines: null,
                onSubmitted: (_) => _sendMessage(),
              ),
            ),
          ),
          const SizedBox(width: 12),
          GestureDetector(
            onTap: _sendMessage,
            child: Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF6B46C1),
                    Color(0xFF9333EA),
                    Color(0xFFEC4899),
                  ],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Colors.purple.withOpacity(0.3),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                Icons.send_rounded,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleTarotRequest() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final tarotMessage = _getTarotMessage(languageManager.currentLanguage);
    
    _higherSelfService.addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: tarotMessage,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.tarotRequest,
      energyLevel: EnergyLevel.mystical,
    ));
    setState(() {});
    _scrollToBottom();
  }

  String _getTarotMessage(String language) {
    switch (language) {
      case 'zh':
        return '''✨ 让我们通过塔罗之镜探索你的灵魂深处

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
      
      case 'en':
        return '''✨ Let us explore the depths of your soul through the Tarot Mirror

Hold your question in your heart,
then give me 3 numbers between 1-78,
follow your intuition and inner guidance 🔮

These numbers will reveal:
🌟 The true state of your soul
💎 Forgotten inner powers  
🌱 The direction of soul growth''';
      
      default:
        return '''✨ 让我们通过塔罗之镜探索你的灵魂深处

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
