import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/manifestation_service.dart';
import 'package:ai_tarot_reading/services/ai_tarot_specialists.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';

class ManifestationScreen extends StatefulWidget {
  const ManifestationScreen({super.key});

  @override
  State<ManifestationScreen> createState() => _ManifestationScreenState();
}

class _ManifestationScreenState extends State<ManifestationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<ManifestationTaskWithProgress> _tasks = [];
  List<JournalEntry> _journalEntries = [];
  ProgressStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final tasks = await ManifestationService.getCurrentTasks();
      final journals = await ManifestationService.getJournalEntries();
      final stats = await ManifestationService.getProgressStats();
      
      if (mounted) {
        setState(() {
          _tasks = tasks;
          _journalEntries = journals;
          _stats = stats;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('❌ 加载显化数据失败: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color(0xFF6B46C1),
                  Color(0xFF8B5CF6),
                  Color(0xFFEC4899),
                ],
              ),
            ),
          ),
          
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                      Expanded(
                        child: Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('manifestation_practice') ?? '显化实践',
                              style: const TextStyle(
                                fontSize: 24,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ),
                      ),
                      const SizedBox(width: 48),
                    ],
                  ),
                ),
                
                // Tab栏
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return TabBar(
                        controller: _tabController,
                        indicator: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25),
                        ),
                        indicatorSize: TabBarIndicatorSize.tab,
                        dividerColor: Colors.transparent,
                        labelColor: const Color(0xFF6B46C1),
                        unselectedLabelColor: Colors.white,
                        labelStyle: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                        tabs: [
                          Tab(text: languageManager.translate('tasks') ?? '任务'),
                          Tab(text: languageManager.translate('journal') ?? '日记'),
                          Tab(text: languageManager.translate('progress') ?? '进度'),
                        ],
                      );
                    },
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // 内容区域
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: _isLoading
                        ? const Center(child: CircularProgressIndicator())
                        : TabBarView(
                            controller: _tabController,
                            children: [
                              _buildTasksTab(),
                              _buildJournalTab(),
                              _buildProgressTab(),
                            ],
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.star, color: Color(0xFF6B46C1)),
              const SizedBox(width: 8),
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    languageManager.translate('active_tasks') ?? '进行中的任务',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF1F2937),
                    ),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: _tasks.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    itemCount: _tasks.length,
                    itemBuilder: (context, index) {
                      final task = _tasks[index];
                      return _buildTaskCard(task);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskCard(ManifestationTaskWithProgress task) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: task.isActive
              ? [const Color(0xFF6B46C1).withOpacity(0.1), const Color(0xFF8B5CF6).withOpacity(0.1)]
              : [Colors.grey.withOpacity(0.1), Colors.grey.withOpacity(0.05)],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: task.isActive ? const Color(0xFF6B46C1).withOpacity(0.3) : Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 任务标题和状态
          Row(
            children: [
              Expanded(
                child: Text(
                  task.task.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: task.isActive ? const Color(0xFF6B46C1) : Colors.grey,
                  ),
                ),
              ),
              if (task.isCompletedToday)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('completed_today') ?? '今日已完成',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      );
                    },
                  ),
                ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 任务描述
          Text(
            task.task.description,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6B7280),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // 进度条和操作按钮
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              '${languageManager.translate('progress') ?? '进度'}: ${task.completedDays.length}/${task.totalDays}',
                              style: const TextStyle(
                                fontSize: 12,
                                color: Color(0xFF6B7280),
                              ),
                            );
                          },
                        ),
                        Text(
                          '${(task.completionPercentage * 100).toInt()}%',
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF6B46C1),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: task.completionPercentage,
                      backgroundColor: Colors.grey.withOpacity(0.3),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        task.isActive ? const Color(0xFF6B46C1) : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              if (task.isActive && !task.isCompletedToday)
                ElevatedButton(
                  onPressed: () => _markTaskCompleted(task),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF6B46C1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('complete') ?? '完成',
                        style: const TextStyle(fontSize: 12),
                      );
                    },
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJournalTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  const Icon(Icons.book, color: Color(0xFF6B46C1)),
                  const SizedBox(width: 8),
                  Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('journal_entries') ?? '日记记录',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF1F2937),
                        ),
                      );
                    },
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: _showNewJournalDialog,
                icon: const Icon(Icons.add, size: 16),
                label: Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      languageManager.translate('new_entry') ?? '新日记',
                      style: const TextStyle(fontSize: 12),
                    );
                  },
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF6B46C1),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Expanded(
            child: _journalEntries.isEmpty
                ? _buildEmptyJournalState()
                : ListView.builder(
                    itemCount: _journalEntries.length,
                    itemBuilder: (context, index) {
                      final entry = _journalEntries[index];
                      return _buildJournalCard(entry);
                    },
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildJournalCard(JournalEntry entry) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF6B46C1).withOpacity(0.05),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: const Color(0xFF6B46C1).withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 日期和字数
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDate(entry.date),
                style: const TextStyle(
                  fontSize: 12,
                  color: Color(0xFF6B46C1),
                  fontWeight: FontWeight.w600,
                ),
              ),
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    '${entry.wordCount} ${languageManager.translate('words') ?? '字'}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9CA3AF),
                    ),
                  );
                },
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // 内容预览
          Text(
            entry.content.length > 100
                ? '${entry.content.substring(0, 100)}...'
                : entry.content,
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF374151),
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressTab() {
    if (_stats == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.trending_up, color: Color(0xFF6B46C1)),
                const SizedBox(width: 8),
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      languageManager.translate('progress_overview') ?? '进度概览',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF1F2937),
                      ),
                    );
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // 统计卡片网格
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 1.2,
              children: [
                _buildStatCard(
                  icon: Icons.assignment,
                  title: '活跃任务',
                  value: '${_stats!.activeTasks}',
                  color: const Color(0xFF6B46C1),
                ),
                _buildStatCard(
                  icon: Icons.check_circle,
                  title: '完成次数',
                  value: '${_stats!.totalTasksCompleted}',
                  color: const Color(0xFF10B981),
                ),
                _buildStatCard(
                  icon: Icons.book,
                  title: '本周日记',
                  value: '${_stats!.journalEntriesThisWeek}',
                  color: const Color(0xFF8B5CF6),
                ),
                _buildStatCard(
                  icon: Icons.local_fire_department,
                  title: '连续天数',
                  value: '${_stats!.streakDays}',
                  color: const Color(0xFFEF4444),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard({
    required IconData icon,
    required String title,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF6B7280),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.star_border,
                size: 64,
                color: Colors.grey.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                languageManager.translate('no_active_tasks') ?? '暂无活跃任务',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                languageManager.translate('get_tasks_from_reading') ?? '通过塔罗解读获取个性化任务',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyJournalState() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.book_outlined,
                size: 64,
                color: Colors.grey.withOpacity(0.5),
              ),
              const SizedBox(height: 16),
              Text(
                languageManager.translate('no_journal_entries') ?? '暂无日记记录',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.grey,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                languageManager.translate('start_journaling_tip') ?? '开始记录你的内心感受和成长历程',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _markTaskCompleted(ManifestationTaskWithProgress task) async {
    try {
      await ManifestationService.markTaskCompleted(task.task.title, task.createdAt);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('任务"${task.task.title}"已完成！'),
          backgroundColor: Colors.green,
        ),
      );
      
      // 刷新数据
      _loadData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('标记任务完成失败，请重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showNewJournalDialog() {
    final TextEditingController contentController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    return Text(
                      languageManager.translate('new_journal_entry') ?? '新日记',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  },
                ),
                
                const SizedBox(height: 20),
                
                Expanded(
                  child: TextField(
                    controller: contentController,
                    maxLines: null,
                    expands: true,
                    decoration: InputDecoration(
                      hintText: '在这里记录你的想法、感受和洞察...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('取消'),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => _saveJournalEntry(contentController.text),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFF6B46C1),
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('保存'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _saveJournalEntry(String content) async {
    if (content.trim().isEmpty) return;
    
    try {
      await ManifestationService.saveJournalEntry(content.trim(), []);
      
      Navigator.of(context).pop();
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('日记已保存！'),
          backgroundColor: Colors.green,
        ),
      );
      
      // 刷新数据
      _loadData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('保存日记失败，请重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    
    if (difference == 0) {
      return '今天';
    } else if (difference == 1) {
      return '昨天';
    } else if (difference < 7) {
      return '$difference天前';
    } else {
      return '${date.month}月${date.day}日';
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }
} 