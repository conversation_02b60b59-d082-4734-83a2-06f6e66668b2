import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:ui';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/providers/daily_tarot_provider.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/widgets/daily_tarot_card.dart';
import 'package:ai_tarot_reading/screens/manifestation_goal_screen.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/screens/card_shuffle_screen.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:intl/intl.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter/rendering.dart';
import 'package:ai_tarot_reading/l10n/app_localizations.dart';

class DailyTarotScreen extends StatefulWidget {
  const DailyTarotScreen({super.key});

  @override
  State<DailyTarotScreen> createState() => _DailyTarotScreenState();
}

class _DailyTarotScreenState extends State<DailyTarotScreen>
    with TickerProviderStateMixin {
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  CalendarFormat _calendarFormat = CalendarFormat.month;
  bool _isCalendarExpanded = true;

  @override
  void initState() {
    super.initState();
    _selectedDay = DateTime.now();
    // 初始化本地化数据
    initializeDateFormatting();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final languageManager = Provider.of<LanguageManager>(context);

    return Consumer2<AppStateProvider, BlurSettingsService>(
      builder: (context, appState, blurSettings, child) {
        return Scaffold(
          backgroundColor: Colors.transparent, // 改为透明背景
          body: SafeArea(
            child: Column(
              children: [
                // 日历区域 - 可折叠（固定在顶部）
                Container(
                  margin: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.15), // 改为半透明毛玻璃
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.white.withOpacity(0.2),
                        blurRadius: 10,
                        offset: const Offset(0, 2),
                      ),
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: blurSettings.getImageFilter(), // 使用动态模糊度
                      child: Column(
                        children: [
                          // 日历头部 - 添加折叠按钮
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.1),
                              borderRadius: BorderRadius.vertical(
                                top: const Radius.circular(16),
                                bottom: Radius.circular(_isCalendarExpanded ? 0 : 16),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  _formatCalendarHeader(_focusedDay, languageManager),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black, // 改为黑色
                                  ),
                                ),
                                Row(
                                  children: [
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          _focusedDay = DateTime(_focusedDay.year, _focusedDay.month - 1);
                                        });
                                      },
                                      icon: const Icon(Icons.chevron_left),
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        setState(() {
                                          _focusedDay = DateTime(_focusedDay.year, _focusedDay.month + 1);
                                        });
                                      },
                                      icon: const Icon(Icons.chevron_right),
                                    ),
                                    // 更明显的折叠按钮
                                    Container(
                                      margin: const EdgeInsets.only(left: 8),
                                      decoration: BoxDecoration(
                                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                                        borderRadius: BorderRadius.circular(8),
                                        border: Border.all(
                                          color: Theme.of(context).primaryColor.withOpacity(0.3),
                                          width: 1,
                                        ),
                                      ),
                                      child: IconButton(
                                        onPressed: () {
                                          setState(() {
                                            _isCalendarExpanded = !_isCalendarExpanded;
                                          });
                                        },
                                        icon: AnimatedRotation(
                                          turns: _isCalendarExpanded ? 0 : 0.5,
                                          duration: const Duration(milliseconds: 300),
                                          child: Icon(
                                            Icons.expand_less,
                                            color: Theme.of(context).primaryColor,
                                            size: 24,
                                          ),
                                        ),
                                        tooltip: _isCalendarExpanded ? '折叠日历' : '展开日历',
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          
                          // 日历主体 - 可折叠
                          if (_isCalendarExpanded)
                            TableCalendar<DailyTarot>(
                              firstDay: DateTime.utc(2020, 1, 1),
                              lastDay: DateTime.utc(2030, 12, 31),
                              focusedDay: _focusedDay,
                              calendarFormat: _calendarFormat,
                              eventLoader: (day) {
                                final dayString = DateFormat('yyyy-MM-dd').format(day);
                                final dailyTarot = appState.dailyTarotReadings[dayString];
                                return dailyTarot != null && dailyTarot.isDrawn ? [dailyTarot] : [];
                              },
                              startingDayOfWeek: StartingDayOfWeek.monday,
                              calendarStyle: CalendarStyle(
                                outsideDaysVisible: false,
                                selectedDecoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor,
                                  shape: BoxShape.circle,
                                ),
                                todayDecoration: BoxDecoration(
                                  color: Theme.of(context).primaryColor.withOpacity(0.6),
                                  shape: BoxShape.circle,
                                ),
                                markerDecoration: const BoxDecoration(
                                  color: Colors.amber,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              headerStyle: const HeaderStyle(
                                formatButtonVisible: false,
                                titleCentered: true,
                              ),
                              onDaySelected: (selectedDay, focusedDay) {
                                setState(() {
                                  _selectedDay = selectedDay;
                                  _focusedDay = focusedDay;
                                });
                                _showDailyTarotEntry(selectedDay, appState);
                              },
                              onFormatChanged: (format) {
                                setState(() {
                                  _calendarFormat = format;
                                });
                              },
                              onPageChanged: (focusedDay) {
                                setState(() {
                                  _focusedDay = focusedDay;
                                });
                              },
                              selectedDayPredicate: (day) {
                                return isSameDay(_selectedDay, day);
                              },
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                
                // 可滚动内容区域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Column(
                      children: [
                        // 显化功能区域 - 液态玻璃毛玻璃UI
                        ClipRRect(
                          borderRadius: BorderRadius.circular(20),
                          child: BackdropFilter(
                            filter: blurSettings.getImageFilter(), // 使用动态模糊度
                            child: Container(
                              padding: const EdgeInsets.all(24), // 增加内边距
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15), // 高透明度毛玻璃
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.4),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 40,
                                    offset: const Offset(0, 12),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.auto_awesome,
                                        color: Colors.black,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        languageManager.translate('manifestation_practice'),
                                        style: const TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.black,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  Text(
                                    languageManager.translate('manifestation_description'),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.black,
                                    ),
                                  ),
                                  const SizedBox(height: 20), // 增加间距
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Container(
                                          height: 44, // 增加按钮高度
                                          decoration: BoxDecoration(
                                            gradient: const LinearGradient(
                                              colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
                                            ),
                                            borderRadius: BorderRadius.circular(12), // 稍微增加圆角
                                            boxShadow: [
                                              BoxShadow(
                                                color: const Color(0xFF8B5CF6).withOpacity(0.3),
                                                blurRadius: 8,
                                                offset: const Offset(0, 4),
                                              ),
                                            ],
                                          ),
                                          child: ElevatedButton(
                                            onPressed: () => _showManifestationGoalSelection(context, appState),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor: Colors.transparent,
                                              foregroundColor: Colors.white,
                                              elevation: 0,
                                              shadowColor: Colors.transparent,
                                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 增加内边距
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                            ),
                                            child: Text(
                                              languageManager.translate('choose_goal'),
                                              style: const TextStyle(
                                                fontSize: 15, // 稍微增加字体
                                                fontWeight: FontWeight.w600,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16), // 增加按钮间距
                                      Expanded(
                                        child: Container(
                                          height: 44, // 增加按钮高度
                                          decoration: BoxDecoration(
                                            color: Colors.white.withOpacity(0.2),
                                            borderRadius: BorderRadius.circular(12), // 稍微增加圆角
                                            border: Border.all(
                                              color: Colors.white.withOpacity(0.5),
                                              width: 1.5,
                                            ),
                                          ),
                                          child: OutlinedButton(
                                            onPressed: () => _startMindfulnessPractice(context, appState),
                                            style: OutlinedButton.styleFrom(
                                              backgroundColor: Colors.transparent,
                                              side: BorderSide.none,
                                              padding: EdgeInsets.zero, // 移除内边距，让Container控制尺寸
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(12),
                                              ),
                                            ),
                                            child: Container(
                                              width: double.infinity,
                                              height: double.infinity,
                                              alignment: Alignment.center, // 确保文字居中
                                              child: Text(
                                                languageManager.translate('mindfulness_practice'),
                                                style: const TextStyle(
                                                  fontSize: 15,
                                                  color: Colors.black,
                                                  fontWeight: FontWeight.w600,
                                                ),
                                                textAlign: TextAlign.center, // 文字居中对齐
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 4), // 在按钮下方增加一点空间
                                ],
                              ),
                            ),
                          ),
                        ),
                        
                        const SizedBox(height: 20),
                        
                        // 今日塔罗卡牌
                        _buildTodaysTarotCard(appState, l10n),
                        
                        const SizedBox(height: 20), // 底部间距
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTodaysTarotCard(AppStateProvider appState, AppLocalizations? l10n) {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    final dailyTarot = appState.dailyTarotReadings[today];

    return DailyTarotCard(
      dailyTarot: dailyTarot,
      selectedDate: DateTime.now(),
      onDrawCard: () => _startDailyTarotShuffle(appState),
      onRedraw: () => _redrawDailyTarot(appState),
      isToday: true,
    );
  }

  // 启动每日塔罗的洗牌抽卡流程
  void _startDailyTarotShuffle(AppStateProvider appState) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardShuffleScreen(
          question: '今日塔罗指引',
          spreadType: '单张牌阵',
          autoMode: false,
          currentPosition: 0,
          onCardSelected: (selectedCard) {
            _saveDailyTarotCard(selectedCard, appState);
          },
        ),
      ),
    );
  }

  // 保存每日塔罗卡牌
  void _saveDailyTarotCard(TarotCard selectedCard, AppStateProvider appState) {
    appState.drawDailyTarotWithSpecificCard(selectedCard);
    if (mounted) {
      setState(() {});
    }
  }

  // 重新抽取每日塔罗
  void _redrawDailyTarot(AppStateProvider appState) {
    _startDailyTarotShuffle(appState);
  }

  void _showDailyTarotEntry(DateTime selectedDay, AppStateProvider appState) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selected = DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    if (selected.isAfter(today)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(languageManager.translate('cannot_view_future_records'))),
      );
      return;
    }

    final dayString = DateFormat('yyyy-MM-dd').format(selectedDay);

    // 先尝试从本地状态获取数据，如果没有则从后端加载
    final localDailyTarot = appState.dailyTarotReadings[dayString];

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white, // 白色底色
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20), // 增强高斯模糊
            child: Container(
              height: MediaQuery.of(context).size.height * 0.8,
              decoration: BoxDecoration(
                // 液态玻璃效果 - 白色底色叠加
                color: Colors.white.withOpacity(0.7), // 调整为70%透明度
                borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                border: Border.all(
                  color: Colors.white.withOpacity(0.6), // 更明显的边框
                  width: 1.5,
                ),
                boxShadow: [
                  // 液态玻璃阴影效果
                  BoxShadow(
                    color: Colors.white.withOpacity(0.3),
                    blurRadius: 25,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 15,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    child: Row(
                      children: [
                        Text(
                          _formatHistoryDate(selectedDay, languageManager),
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.black87,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(
                            Icons.close,
                            color: Colors.black87,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: FutureBuilder<DailyTarot?>(
                      future: _loadDailyTarotData(selectedDay),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                            ),
                          );
                        }

                        // 使用后端数据或本地数据
                        final dailyTarot = snapshot.data ?? localDailyTarot;

                        return DailyTarotCard(
                          dailyTarot: dailyTarot,
                          selectedDate: selectedDay,
                          onDrawCard: selected.isAtSameMomentAs(today)
                              ? () {
                                  Navigator.pop(context);
                                  _startDailyTarotShuffle(appState);
                                }
                              : null,
                          onRedraw: selected.isAtSameMomentAs(today)
                              ? () {
                                  Navigator.pop(context);
                                  _redrawDailyTarot(appState);
                                }
                              : null,
                          isToday: selected.isAtSameMomentAs(today),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 从后端加载每日塔罗数据
  Future<DailyTarot?> _loadDailyTarotData(DateTime selectedDay) async {
    try {
      final dataService = SupabaseDataService();
      final dayString = DateFormat('yyyy-MM-dd').format(selectedDay);

      // 获取每日塔罗数据
      final dailyTarotData = await dataService.getDailyTarot(dayString);

      // 获取显化日记数据
      final manifestationData = await dataService.getManifestationJournal(dayString);

      // 转换为 DailyTarot 对象
      if (dailyTarotData != null) {
        print('📊 加载的每日塔罗数据: $dailyTarotData'); // 调试日志

        // 查找对应的塔罗牌
        TarotCard? card;
        if (dailyTarotData['card'] != null) {
          final cardData = dailyTarotData['card'] as Map<String, dynamic>;
          final cardId = cardData['id'] as String?;
          if (cardId != null) {
            card = TarotCardsData.allCards.firstWhere(
              (c) => c.id == cardId,
              orElse: () => TarotCardsData.allCards.first,
            );
          }
        }

        // 解析显化目标
        ManifestationGoal? manifestationGoal;
        if (dailyTarotData['manifestation_goal'] != null) {
          final goalStr = dailyTarotData['manifestation_goal'] as String;
          try {
            manifestationGoal = ManifestationGoal.values.firstWhere(
              (g) => g.name == goalStr,
            );
          } catch (e) {
            print('⚠️ 无法解析显化目标: $goalStr');
            manifestationGoal = ManifestationGoal.wealth;
          }
        }

        return DailyTarot(
          date: selectedDay,
          card: card,
          fortune: dailyTarotData['fortune'] as String?,
          advice: dailyTarotData['advice'] as String?,
          isDrawn: dailyTarotData['is_drawn'] as bool? ?? false,
          manifestationGoal: manifestationGoal,
          affirmation: dailyTarotData['affirmation'] as String?,
          manifestationJournal: dailyTarotData['manifestation_journal'] as String? ?? manifestationData?['journal'] as String?,
        );
      }

      return null;
    } catch (e) {
      print('❌ 加载每日塔罗数据失败: $e');
      return null;
    }
  }

  void _showManifestationGoalSelection(BuildContext context, AppStateProvider appState) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationGoalScreen(
          onGoalSelected: (goal) {
            appState.setManifestationGoal(goal);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('已设定显化目标：${goal.displayName}'),
                backgroundColor: Colors.purple[600],
              ),
            );
          },
        ),
      ),
    );
  }

  void _startMindfulnessPractice(BuildContext context, AppStateProvider appState) {
    final currentGoal = appState.currentManifestationGoal ?? ManifestationGoal.wealth;
    final affirmation = _getAffirmationForGoal(currentGoal);
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationAnimationScreen(
          goal: currentGoal,
          affirmation: affirmation,
        ),
      ),
    );
  }

  String _getAffirmationForGoal(ManifestationGoal goal) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '我值得拥有丰盛的财富，宇宙的富足源源不断地流向我';
      case ManifestationGoal.career:
        return '我的事业蒸蒸日上，我在工作中发挥着独特的才能';
      case ManifestationGoal.beauty:
        return '我由内而外散发着美丽的光芒，我爱我的独特之美';
      case ManifestationGoal.fame:
        return '我的才华被世界看见，我在自己的领域里发光发热';
      case ManifestationGoal.love:
        return '我吸引着真挚的爱情，我值得被深深地爱着';
      default:
        return '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活';
    }
  }

  // 格式化日历头部日期
  String _formatCalendarHeader(DateTime date, LanguageManager languageManager) {
    switch (languageManager.currentLanguage) {
      case 'en-US':
        return DateFormat('MMMM yyyy', 'en').format(date);
      case 'es-ES':
        return DateFormat('MMMM yyyy', 'es').format(date);
      case 'ja-JP':
        return DateFormat('yyyy年MM月', 'ja').format(date);
      case 'ko-KR':
        return DateFormat('yyyy년 MM월', 'ko').format(date);
      case 'zh-TW':
        return DateFormat('yyyy年MM月').format(date);
      case 'zh-CN':
      default:
        return DateFormat('yyyy年MM月').format(date);
    }
  }

  // 格式化历史页面日期
  String _formatHistoryDate(DateTime date, LanguageManager languageManager) {
    switch (languageManager.currentLanguage) {
      case 'en-US':
        return DateFormat('MMMM dd, yyyy', 'en').format(date);
      case 'es-ES':
        return DateFormat('dd \'de\' MMMM \'de\' yyyy', 'es').format(date);
      case 'ja-JP':
        return DateFormat('yyyy年MM月dd日', 'ja').format(date);
      case 'ko-KR':
        return DateFormat('yyyy년 MM월 dd일', 'ko').format(date);
      case 'zh-TW':
        return DateFormat('yyyy年MM月dd日').format(date);
      case 'zh-CN':
      default:
        return DateFormat('yyyy年MM月dd日').format(date);
    }
  }
}
