import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/screens/spread_preview_screen.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'dart:ui';

class QuestionInputScreen extends StatefulWidget {
  const QuestionInputScreen({super.key});

  @override
  State<QuestionInputScreen> createState() => _QuestionInputScreenState();
}

class _QuestionInputScreenState extends State<QuestionInputScreen> {
  final TextEditingController _questionController = TextEditingController();
  String? _selectedSpread;
  bool _canProceed = false;

  // 获取常用牌阵配置
  List<Map<String, dynamic>> get _tarotSpreads {
    final commonSpreads = [
      TarotSpread.getSpreadByName('单张牌阵')!,
      TarotSpread.getSpreadByName('三张牌阵（经典）')!,
      TarotSpread.getSpreadByName('爱情关系牌阵')!,
      TarotSpread.getSpreadByName('事业成长牌阵')!,
      TarotSpread.getSpreadByName('六芒星牌阵')!,
      TarotSpread.getSpreadByName('凯尔特十字牌阵')!,
    ];

    return commonSpreads.map((spread) => {
      'name': spread.name,
      'translatedName': _getTranslatedSpreadName(spread.name), // 添加翻译名称
      'description': _getShortDescription(spread.name), // 使用简化描述
      'cards': spread.cardCount,
      'icon': _getIconForSpread(spread.name),
      'color': _getColorForSpread(spread.name),
    }).toList();
  }

  // 获取简化的描述文字，适合手机界面
  String _getShortDescription(String spreadName) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (spreadName) {
      case '单张牌阵':
        return languageManager.translate('quick_daily_guidance');
      case '三张牌阵（经典）':
        return languageManager.translate('past_present_future');
      case '爱情关系牌阵':
        return languageManager.translate('analyze_emotional_state');
      case '事业成长牌阵':
        return languageManager.translate('workplace_problem_analysis');
      case '六芒星牌阵':
        return languageManager.translate('comprehensive_problem_analysis');
      case '凯尔特十字牌阵':
        return languageManager.translate('deep_problem_analysis');
      default:
        return languageManager.translate('professional_tarot_guidance');
    }
  }

  // 获取翻译的牌阵名称
  String _getTranslatedSpreadName(String spreadName) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (spreadName) {
      case '单张牌阵':
        return languageManager.translate('single_card_spread');
      case '三张牌阵（经典）':
        return languageManager.translate('three_card_spread_classic');
      case '爱情关系牌阵':
        return languageManager.translate('love_relationship_spread');
      case '事业成长牌阵':
        return languageManager.translate('career_growth_spread');
      case '六芒星牌阵':
        return languageManager.translate('hexagram_spread');
      case '凯尔特十字牌阵':
        return languageManager.translate('celtic_cross_spread');
      default:
        return spreadName;
    }
  }

  IconData _getIconForSpread(String spreadName) {
    switch (spreadName) {
      case '单张牌阵':
        return Icons.crop_portrait;
      case '三张牌阵（经典）':
        return Icons.view_column;
      case '爱情关系牌阵':
        return Icons.favorite;
      case '事业成长牌阵':
        return Icons.work;
      case '六芒星牌阵':
        return Icons.hexagon;
      case '凯尔特十字牌阵':
        return Icons.add;
      default:
        return Icons.auto_awesome;
    }
  }

  Color _getColorForSpread(String spreadName) {
    switch (spreadName) {
      case '单张牌阵':
        return const Color(0xFF8B5CF6); // purple
      case '三张牌阵（经典）':
        return const Color(0xFF3B82F6); // blue
      case '爱情关系牌阵':
        return const Color(0xFFEC4899); // pink
      case '事业成长牌阵':
        return const Color(0xFF10B981); // emerald
      case '六芒星牌阵':
        return const Color(0xFFF59E0B); // amber
      case '凯尔特十字牌阵':
        return const Color(0xFF8B5A2B); // brown
      default:
        return const Color(0xFF6B7280); // gray
    }
  }

  void _updateCanProceed() {
    setState(() {
      _canProceed = _questionController.text.trim().isNotEmpty && _selectedSpread != null;
    });
  }

  void _handleSpreadSelection(String spreadName) {
    final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
    
    print('🔍 _handleSpreadSelection: spreadName=$spreadName');
    print('🔍 SubscriptionService: isSubscribed=${subscriptionService.isSubscribed}, tier=${subscriptionService.currentTier}');
    
    // 检查是否为免费会员且选择了非单张牌阵
    if (!subscriptionService.isSubscribed && spreadName != '单张牌阵') {
      print('❌ 免费会员尝试选择付费牌阵，显示升级弹窗');
      _showFreeLimitationDialog();
      return;
    }

    print('✅ 权限检查通过，允许选择牌阵');
    setState(() {
      _selectedSpread = spreadName;
    });
    _updateCanProceed();
  }

  void _showFreeLimitationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.lock,
              color: Colors.orange,
              size: 24,
            ),
            const SizedBox(width: 8),
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('membership_feature'));
              },
            ),
          ],
        ),
        content: Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('free_member_single_spread_only'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  languageManager.translate('themed_spreads_require_upgrade'),
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  '• ${languageManager.translate('unlock_all_themed_spreads')}\n• ${languageManager.translate('daily_multiple_ai_readings')}\n• ${languageManager.translate('detailed_divination_guidance')}\n• ${languageManager.translate('professional_tarot_advice')}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(
                  languageManager.translate('back'),
                  style: const TextStyle(color: Colors.grey),
                );
              },
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6B46C1),
              foregroundColor: Colors.white,
            ),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('upgrade_now'));
              },
            ),
          ),
        ],
      ),
    );
  }

  void _startDivination() {
    if (_canProceed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SpreadPreviewScreen(
            question: _questionController.text.trim(),
            spreadType: _selectedSpread!,
          ),
        ),
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _questionController.addListener(_updateCanProceed);
  }

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      resizeToAvoidBottomInset: true, // 确保键盘弹出时页面正确调整
      body: Stack(
        children: [
          // 统一的彩色渐变背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -1.0), // 135deg
                  end: Alignment(1.0, 1.0),
                  stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                  colors: [
                    Color(0xFF87CEEB), // #87CEEB 0%
                    Color(0xFFE6E6FA), // #E6E6FA 25%
                    Color(0xFFF8BBD9), // #F8BBD9 50%
                    Color(0xFFE6E6FA), // #E6E6FA 75%
                    Color(0xFF87CEEB), // #87CEEB 100%
                  ],
                ),
              ),
            ),
          ),
          
          // 云朵装饰
          Positioned.fill(
            child: Stack(
              children: [
                Positioned(
                  top: 60,
                  left: 40,
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.6),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.4),
                          blurRadius: 8,
                          spreadRadius: 4,
                        ),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  top: 150,
                  right: 60,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 6,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          // 主要内容
          SafeArea(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // 返回按钮
                  Row(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.8),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 4),
                            ),
                          ],
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                            child: IconButton(
                              onPressed: () => Navigator.pop(context),
                              icon: const Icon(Icons.arrow_back_ios, color: Color(0xFF374151)),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.8),
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.1),
                                blurRadius: 15,
                                offset: const Offset(0, 5),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: BackdropFilter(
                              filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                              child: Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    languageManager.translate('start_tarot_journey'),
                                    style: const TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: 18,
                                      color: Color(0xFF374151),
                                    ),
                                    textAlign: TextAlign.center,
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 40),
                  
                  // 问题输入区域 - ChatGPT风格
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.8),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 25,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0), // 向右移动8像素
                              child: Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    languageManager.translate('enter_your_question'),
                                    style: const TextStyle(
                                      fontFamily: 'Inter',
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                      color: Color(0xFF374151),
                                    ),
                                  );
                                },
                              ),
                            ),
                            const SizedBox(height: 12),
                            Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return TextField(
                                  controller: _questionController,
                                  maxLines: 3,
                                  decoration: InputDecoration(
                                    hintText: languageManager.translate('describe_question_detail'),
                                    hintStyle: TextStyle(
                                      color: Colors.grey.withValues(alpha: 0.6),
                                      fontFamily: 'Inter',
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                      borderSide: BorderSide.none,
                                    ),
                                    filled: true,
                                    fillColor: Colors.white.withValues(alpha: 0.5),
                                    contentPadding: const EdgeInsets.all(16),
                                  ),
                                  style: const TextStyle(
                                    fontFamily: 'Inter',
                                    fontSize: 16,
                                    color: Color(0xFF374151),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                    ),
                  ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),
                  
                  const SizedBox(height: 30),
                  
                  // 牌阵选择
                  SizedBox(
                    height: 400, // 设置固定高度，确保可以滚动
                    child: Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.8),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 25,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(left: 8.0), // 向右移动8像素
                                child: Consumer<LanguageManager>(
                                  builder: (context, languageManager, child) {
                                    return Text(
                                      languageManager.translate('choose_spread'),
                                      style: const TextStyle(
                                        fontFamily: 'Inter',
                                        fontWeight: FontWeight.w600,
                                        fontSize: 16,
                                        color: Color(0xFF374151),
                                      ),
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(height: 16),
                              SizedBox(
                                height: 320, // 为GridView设置固定高度
                                child: GridView.builder(
                                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: 2,
                                    crossAxisSpacing: 12,
                                    mainAxisSpacing: 12,
                                    childAspectRatio: 0.85, // 调整比例，让卡片更高一些
                                  ),
                                  itemCount: _tarotSpreads.length,
                                  itemBuilder: (context, index) {
                                    final spread = _tarotSpreads[index];
                                    final isSelected = _selectedSpread == spread['name'];

                                    return GestureDetector(
                                      onTap: () {
                                        _handleSpreadSelection(spread['name']);
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.all(12), // 减少内边距
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? spread['color'].withValues(alpha: 0.2)
                                              : Colors.white.withValues(alpha: 0.6),
                                          borderRadius: BorderRadius.circular(16),
                                          border: Border.all(
                                            color: isSelected
                                                ? spread['color']
                                                : Colors.transparent,
                                            width: 2,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withValues(alpha: 0.05),
                                              blurRadius: 10,
                                              offset: const Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center, // 整体垂直居中
                                          crossAxisAlignment: CrossAxisAlignment.center, // 水平居中
                                          children: [
                                            // 使用Flexible让内容能够自适应，但保持居中
                                            Flexible(
                                              child: Column(
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    spread['icon'],
                                                    size: 32, // 稍微增大图标
                                                    color: spread['color'],
                                                  ),
                                                  const SizedBox(height: 8),
                                                  Text(
                                                    spread['translatedName'],
                                                    style: const TextStyle(
                                                      fontFamily: 'Inter',
                                                      fontWeight: FontWeight.w600,
                                                      fontSize: 14, // 稍微增大字体
                                                      color: Color(0xFF374151),
                                                    ),
                                                    textAlign: TextAlign.center,
                                                    maxLines: 2, // 限制最多2行
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                  const SizedBox(height: 6),
                                                  Text(
                                                    spread['description'],
                                                    style: TextStyle(
                                                      fontFamily: 'Inter',
                                                      fontSize: 11, // 稍微增大描述文字
                                                      color: Colors.grey.withValues(alpha: 0.8),
                                                      height: 1.3, // 调整行高
                                                    ),
                                                    textAlign: TextAlign.center,
                                                    maxLines: 3, // 限制最多3行
                                                    overflow: TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ).animate().fadeIn(duration: 800.ms, delay: 200.ms).slideY(begin: 0.3, end: 0),
                  ),
                  
                  const SizedBox(height: 20),
                  
                  // 开始占卜按钮
                  Container(
                    width: double.infinity,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: _canProceed 
                          ? const LinearGradient(
                              colors: [Color(0xFF8B5CF6), Color(0xFF3B82F6)],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.grey.withValues(alpha: 0.4),
                                Colors.grey.withValues(alpha: 0.6),
                              ],
                            ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: _canProceed ? [
                        BoxShadow(
                          color: const Color(0xFF8B5CF6).withValues(alpha: 0.25),
                          blurRadius: 20,
                          offset: const Offset(0, 8),
                        ),
                      ] : [],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: _canProceed ? _startDivination : null,
                        child: Center(
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return Text(
                                languageManager.translate('start_divination'),
                                style: const TextStyle(
                                  fontFamily: 'Inter',
                                  fontWeight: FontWeight.w600,
                                  fontSize: 18,
                                  color: Colors.white,
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    ),
                  ).animate().fadeIn(duration: 1000.ms, delay: 400.ms).slideY(begin: 0.3, end: 0),

                  // 底部间距，确保键盘弹出时可以滚动到底部
                  const SizedBox(height: 40),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
