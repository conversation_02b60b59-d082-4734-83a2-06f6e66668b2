import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/services/psychological_guidance_service.dart';
import 'package:ai_tarot_reading/services/ai_tarot_service.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/models/psychological_tarot_reading.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

class PsychologicalGuidanceDemoScreen extends StatefulWidget {
  const PsychologicalGuidanceDemoScreen({super.key});

  @override
  State<PsychologicalGuidanceDemoScreen> createState() => _PsychologicalGuidanceDemoScreenState();
}

class _PsychologicalGuidanceDemoScreenState extends State<PsychologicalGuidanceDemoScreen> {
  final TextEditingController _questionController = TextEditingController();
  final TextEditingController _responseController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  PsychologicalTarotReading? _currentReading;
  bool _isLoading = false;
  List<ChatMessage> _messages = [];

  @override
  void initState() {
    super.initState();
    // 延迟添加欢迎消息，以便LanguageManager已经初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      _addSystemMessage(languageManager.translate('psychological_guidance_welcome'));
    });
  }

  void _addSystemMessage(String message) {
    setState(() {
      _messages.add(ChatMessage(
        text: message,
        isUser: false,
        timestamp: DateTime.now(),
        messageType: MessageType.system,
      ));
    });
    _scrollToBottom();
  }

  void _addUserMessage(String message) {
    setState(() {
      _messages.add(ChatMessage(
        text: message,
        isUser: true,
        timestamp: DateTime.now(),
        messageType: MessageType.user,
      ));
    });
    _scrollToBottom();
  }

  void _addAIMessage(String message, {MessageType type = MessageType.ai}) {
    setState(() {
      _messages.add(ChatMessage(
        text: message,
        isUser: false,
        timestamp: DateTime.now(),
        messageType: type,
      ));
    });
    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  Future<void> _startReading() async {
    final question = _questionController.text.trim();
    if (question.isEmpty) return;

    _addUserMessage(question);
    _questionController.clear();

    setState(() {
      _isLoading = true;
    });

    try {
      // 模拟抽取3张牌
      final cards = TarotCardsData.getRandomCards(3);
      
      // 生成心理引导式解读
      final reading = await AITarotService.generatePsychologicalReading(
        question: question,
        cards: cards,
        spreadType: '三张牌阵',
      );

      setState(() {
        _currentReading = reading;
      });

      // 显示基础解读
      _addAIMessage('🔮 **塔罗解读**\n\n${reading.basicReading}', type: MessageType.tarotReading);
      
      // 显示问题类型分析
      _addAIMessage('📊 **问题分析**\n\n我识别出这是一个${reading.questionTypeDescription}类的问题。', type: MessageType.analysis);
      
      // 显示引导问题
      if (reading.guidingQuestions.isNotEmpty) {
        final firstCard = cards.first;
        final questions = reading.guidingQuestions[firstCard.id] ?? [];
        if (questions.isNotEmpty) {
          _addAIMessage('🤔 **深度引导**\n\n${questions.first}', type: MessageType.guidance);
        }
      }

    } catch (e) {
      _addAIMessage('抱歉，生成解读时出现了错误。请稍后重试。');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _continueConversation() async {
    final response = _responseController.text.trim();
    if (response.isEmpty || _currentReading == null) return;

    _addUserMessage(response);
    _responseController.clear();

    setState(() {
      _isLoading = true;
    });

    try {
      // 继续对话
      final cards = TarotCardsData.getRandomCards(1); // 使用一张牌作为焦点
      final updatedReading = await AITarotService.continueGuidedConversation(
        previousReading: _currentReading!,
        userResponse: response,
        focusCard: cards.first,
      );

      setState(() {
        _currentReading = updatedReading;
      });

      // 显示心理模式识别
      if (updatedReading.hasIdentifiedPattern) {
        _addAIMessage('🧠 **模式识别**\n\n${updatedReading.patternDescription}', type: MessageType.pattern);
      }

      // 显示下一轮引导问题
      if (updatedReading.canContinueConversation) {
        final questions = updatedReading.guidingQuestions[cards.first.id] ?? [];
        if (questions.isNotEmpty) {
          _addAIMessage('💭 **继续探索**\n\n${questions.first}', type: MessageType.guidance);
        }
      } else {
        // 显示疗愈建议
        _showHealingGuidance();
      }

    } catch (e) {
      _addAIMessage('继续对话时出现了错误。请稍后重试。');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showHealingGuidance() {
    if (_currentReading == null) return;

    // 显示疗愈技术
    if (_currentReading!.healingTechniques.isNotEmpty) {
      final techniques = _currentReading!.healingTechniques
          .map((t) => '**${t.name}**\n${t.description}\n\n*练习方法：*\n${t.instruction}')
          .join('\n\n---\n\n');
      
      _addAIMessage('🌟 **疗愈引导**\n\n$techniques', type: MessageType.healing);
    }

    // 显示显化指引
    _addAIMessage('✨ **显化指引**\n\n${_currentReading!.manifestationGuidance}', type: MessageType.manifestation);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // 毛玻璃背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),
          
          SafeArea(
            child: Column(
              children: [
                // 顶部标题 - 使用动态模糊设置
                Consumer<BlurSettingsService>(
                  builder: (context, blurSettings, child) {
                    return ClipRRect(
                      borderRadius: BorderRadius.circular(0),
                      child: BackdropFilter(
                        filter: blurSettings.getImageFilter(),
                        child: Container(
                          padding: const EdgeInsets.all(20),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.15),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: const Icon(Icons.arrow_back_ios),
                              ),
                              Expanded(
                                child: Consumer<LanguageManager>(
                                  builder: (context, languageManager, child) {
                                    return Text(
                                      languageManager.translate('psychological_guidance_tarot'),
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      textAlign: TextAlign.center,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 48),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                // 对话区域 - 使用动态模糊设置
                Expanded(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    child: Consumer<BlurSettingsService>(
                      builder: (context, blurSettings, child) {
                        return ClipRRect(
                          borderRadius: BorderRadius.circular(16),
                          child: BackdropFilter(
                            filter: blurSettings.getImageFilter(),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.15),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.3),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.2),
                                    blurRadius: 20,
                                    offset: const Offset(0, 8),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 40,
                                    offset: const Offset(0, 12),
                                  ),
                                ],
                              ),
                              child: Column(
                                children: [
                                  // 消息列表
                                  Expanded(
                                    child: ListView.builder(
                                      controller: _scrollController,
                                      padding: const EdgeInsets.all(16),
                                      itemCount: _messages.length,
                                      itemBuilder: (context, index) {
                                        return _buildMessageBubble(_messages[index]);
                                      },
                                    ),
                                  ),

                                  // 输入区域
                                  _buildInputArea(),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(ChatMessage message) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment: message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            _buildMessageIcon(message.messageType),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isUser 
                  ? FigmaTheme.primaryPink.withOpacity(0.8)
                  : _getMessageColor(message.messageType),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                message.text,
                style: TextStyle(
                  color: message.isUser ? Colors.white : Colors.black87,
                  fontSize: 14,
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildMessageIcon(MessageType type) {
    IconData icon;
    Color color;
    
    switch (type) {
      case MessageType.tarotReading:
        icon = Icons.auto_awesome;
        color = Colors.purple;
        break;
      case MessageType.analysis:
        icon = Icons.analytics;
        color = Colors.blue;
        break;
      case MessageType.guidance:
        icon = Icons.psychology;
        color = Colors.orange;
        break;
      case MessageType.pattern:
        icon = Icons.pattern;
        color = Colors.red;
        break;
      case MessageType.healing:
        icon = Icons.healing;
        color = Colors.green;
        break;
      case MessageType.manifestation:
        icon = Icons.star;
        color = Colors.amber;
        break;
      default:
        icon = Icons.smart_toy;
        color = Colors.grey;
    }
    
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        shape: BoxShape.circle,
      ),
      child: Icon(icon, size: 16, color: color),
    );
  }

  Color _getMessageColor(MessageType type) {
    switch (type) {
      case MessageType.tarotReading:
        return Colors.purple.withOpacity(0.1);
      case MessageType.analysis:
        return Colors.blue.withOpacity(0.1);
      case MessageType.guidance:
        return Colors.orange.withOpacity(0.1);
      case MessageType.pattern:
        return Colors.red.withOpacity(0.1);
      case MessageType.healing:
        return Colors.green.withOpacity(0.1);
      case MessageType.manifestation:
        return Colors.amber.withOpacity(0.1);
      default:
        return Colors.grey.withOpacity(0.1);
    }
  }

  Widget _buildInputArea() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: Colors.grey.withOpacity(0.3)),
        ),
      ),
      child: Column(
        children: [
          if (_currentReading == null) ...[
            // 初始问题输入
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return TextField(
                  controller: _questionController,
                  decoration: InputDecoration(
                    hintText: languageManager.translate('question_input_hint'),
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 2,
                );
              },
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return ElevatedButton(
                    onPressed: _isLoading ? null : _startReading,
                    child: _isLoading
                      ? const CircularProgressIndicator()
                      : Text(languageManager.translate('start_reading')),
                  );
                },
              ),
            ),
          ] else if (_currentReading!.canContinueConversation) ...[
            // 继续对话输入
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return TextField(
                  controller: _responseController,
                  decoration: InputDecoration(
                    hintText: languageManager.translate('please_answer_above_question'),
                    border: const OutlineInputBorder(),
                  ),
                  maxLines: 2,
                );
              },
            ),
            const SizedBox(height: 8),
            SizedBox(
              width: double.infinity,
              child: Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return ElevatedButton(
                    onPressed: _isLoading ? null : _continueConversation,
                    child: _isLoading
                      ? const CircularProgressIndicator()
                      : Text(languageManager.translate('continue_conversation')),
                  );
                },
              ),
            ),
          ] else ...[
            // 对话结束
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    languageManager.translate('psychological_guidance_complete'),
                    style: const TextStyle(
                      color: Colors.green,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;
  final MessageType messageType;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
    required this.messageType,
  });
}

enum MessageType {
  system,
  user,
  ai,
  tarotReading,
  analysis,
  guidance,
  pattern,
  healing,
  manifestation,
}
