import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/screens/card_shuffle_screen.dart';
import 'package:ai_tarot_reading/screens/spread_layout_screen.dart';
import 'package:ai_tarot_reading/screens/chat_reading_screen.dart';
import 'package:ai_tarot_reading/screens/circular_card_selection_screen.dart';
import 'package:ai_tarot_reading/screens/manual_card_selection_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';

class SpreadPreviewScreen extends StatefulWidget {
  final String question;
  final String spreadType;

  const SpreadPreviewScreen({
    super.key,
    required this.question,
    required this.spreadType,
  });

  @override
  State<SpreadPreviewScreen> createState() => _SpreadPreviewScreenState();
}

class _SpreadPreviewScreenState extends State<SpreadPreviewScreen> {
  TarotSpread? currentSpread;

  @override
  void initState() {
    super.initState();
    currentSpread = TarotSpread.getSpreadByName(widget.spreadType);
  }

  // 获取卡牌数量
  int get cardCount => currentSpread?.cardCount ?? 1;

  // 获取牌阵描述
  String get spreadDescription => currentSpread?.description ?? '专业的塔罗指引，为你答疑解惑';

  // 翻译牌阵描述
  String _getTranslatedSpreadDescription(LanguageManager languageManager) {
    final descriptionTranslationMap = {
      '聚焦当前能量或宇宙提示，适合快速日常指引。': 'single_card_description',
      '过去、现在、未来，揭示事件的时间脉络。': 'three_card_classic_description',
      '当前状况、挑战、建议行动，适合日常问题分析。': 'advice_spread_description',
      '两个选项各用三张牌，分别代表现状、影响、结果，帮助做选择。': 'choice_spread_description',
      '你、对方、你怎么看关系、对方怎么看、现状、未来，分析感情状态。': 'love_relationship_description',
      '问题本质、阻碍、机会、建议、外部环境、最终结果，适合问题全景分析。': 'hexagram_spread_description',
      '深度问题剖析，分析现状、阻碍、潜意识、过去未来走向、周边环境和结局等10个面向。': 'celtic_cross_description',
      '内在创伤、当下情绪、内在需求、疗愈路径、转化结果，适合情绪/心理咨询类场景。': 'soul_healing_description',
      '目前状态、内在潜能、阻碍、发展建议、最终结果，适合职场问题。': 'career_growth_description',
    };

    final translationKey = descriptionTranslationMap[spreadDescription];
    if (translationKey != null) {
      return languageManager.translate(translationKey);
    }
    return spreadDescription; // 如果没有找到翻译，返回原文
  }

  // 获取牌位含义
  List<String> get cardPositions => currentSpread?.positions ?? ['当前能量/宇宙提示'];

  // 翻译牌位含义
  String _translatePosition(String position) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (position) {
      case '你':
        return languageManager.translate('you');
      case '对方':
        return languageManager.translate('other_person');
      case '你怎么看关系':
        return languageManager.translate('how_you_see');
      case '对方怎么看':
        return languageManager.translate('how_other_sees');
      case '关系':
        return languageManager.translate('relationship');
      case '现状':
        return languageManager.translate('current_status');
      case '未来':
        return languageManager.translate('future');
      case '过去':
        return languageManager.translate('past');
      case '现在':
        return languageManager.translate('present');
      case '当前能量':
        return languageManager.translate('current_energy');
      case '当前能量/宇宙提示':
        return languageManager.translate('position_current_energy');
      default:
        return position; // 如果没有翻译，返回原文
    }
  }

  // 翻译牌阵名称
  String _getTranslatedSpreadName(LanguageManager languageManager) {
    final spreadTranslationMap = {
      '单张牌阵': 'single_card_spread',
      '三张牌阵（经典）': 'three_card_spread_classic',
      '爱情关系牌阵': 'love_relationship_spread',
      '事业成长牌阵': 'career_growth_spread',
      '六芒星牌阵': 'hexagram_spread',
      '凯尔特十字牌阵': 'celtic_cross_spread',
    };

    final translationKey = spreadTranslationMap[widget.spreadType];
    if (translationKey != null) {
      return languageManager.translate(translationKey);
    }
    return widget.spreadType; // 如果没有找到翻译，返回原文
  }

  void _startAutoShuffle() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => CardShuffleScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          autoMode: true, // 自动模式：洗牌后直接机器抽牌进入解读
        ),
      ),
    );
  }

  void _startSpreadLayout() {
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => SpreadLayoutScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          selectedCards: const [], // 空的卡牌列表，用户将逐张选择
        ),
      ),
    );
  }

  void _showManualCardSelection() {
    // 🎯 直接显示完整牌库选择界面
    _showFullCardLibrary();
  }

  void _showCircularCardSelection() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CircularCardSelectionScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          onCardSelected: (selectedCard) {
            // 根据牌阵类型处理选中的卡牌
            if (cardCount == 1) {
              // 单张牌阵：直接进入解读
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ChatReadingScreen(
                    question: widget.question,
                    selectedCards: [selectedCard],
                    spreadType: widget.spreadType,
                  ),
                ),
              );
            } else {
              // 多张牌阵：进入牌阵布局页面
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => SpreadLayoutScreen(
                    question: widget.question,
                    spreadType: widget.spreadType,
                    selectedCards: [selectedCard],
                  ),
                ),
              );
            }
          },
        ),
      ),
    );
  }

  void _showFullCardLibrary() {
    print('🎯 显示完整牌库选择界面');
    // 直接进入完整的手动选牌页面，支持选择所需数量的牌
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ManualCardSelectionScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          cardCount: cardCount,
        ),
      ),
    );
  }

  // 根据牌数量计算容器高度
  double _getContainerHeight() {
    switch (cardCount) {
      case 1:
        return 350; // 增加单张牌阵的高度，确保显示完整
      case 3:
        return 250;
      case 4:
      case 5:
        return 300;
      case 6:
        return 400; // 6张牌需要更高的容器
      case 7:
        return 350;
      case 10:
        return 450; // 凯尔特十字需要更高的容器
      case 12:
        return 500; // 年运势需要最高的容器
      default:
        return 300;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -1.0),
                  end: Alignment(1.0, 1.0),
                  colors: [
                    Color(0xFF87CEEB),
                    Color(0xFFE6E6FA),
                    Color(0xFFF8BBD9),
                    Color(0xFFE6E6FA),
                    Color(0xFF87CEEB),
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: FigmaTheme.textPrimary,
                          size: 24,
                        ),
                      ),
                      const Spacer(),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            _getTranslatedSpreadName(languageManager),
                            style: const TextStyle(
                              color: FigmaTheme.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                      const Spacer(),
                      // 手动选牌按钮
                      GestureDetector(
                        onTap: _showManualCardSelection,
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(
                                Icons.touch_app,
                                color: FigmaTheme.textPrimary,
                                size: 16,
                              ),
                              const SizedBox(width: 4),
                              Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    languageManager.translate('manual_card_selection'),
                                    style: const TextStyle(
                                      color: FigmaTheme.textPrimary,
                                      fontSize: 12,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  );
                                },
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 内容区域 - 改为可滚动
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // 标题区域
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              _getTranslatedSpreadName(languageManager),
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.w700,
                                color: Color(0xFF2D3748),
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ).animate().fadeIn(duration: 600.ms),

                        const SizedBox(height: 16),

                        // 提示文字
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('silently_think_question'),
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF2D3748),
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ).animate().fadeIn(delay: 200.ms, duration: 600.ms),

                        const SizedBox(height: 8),

                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              '${languageManager.translate('draw_your_card')}${cardCount == 1 ? '' : '1-$cardCount'}${languageManager.translate('card_number')}',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF8B5CF6),
                              ),
                              textAlign: TextAlign.center,
                            );
                          },
                        ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

                        const SizedBox(height: 40),

                        // 牌阵预览区域 - 改为固定高度，内部可滚动
                        Container(
                          width: double.infinity,
                          height: _getContainerHeight(),
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(24),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.08),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Column(
                            children: [
                              // 牌阵布局 - 可滚动
                              Expanded(
                                child: SingleChildScrollView(
                                  child: _buildSpreadLayout(),
                                ),
                              ),

                              const SizedBox(height: 24),

                              // 牌阵描述
                              Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    _getTranslatedSpreadDescription(languageManager),
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[600],
                                      height: 1.4,
                                    ),
                                    textAlign: TextAlign.center,
                                  );
                                },
                              ),
                            ],
                          ),
                        ).animate().fadeIn(delay: 600.ms, duration: 800.ms).scale(
                          begin: const Offset(0.9, 0.9),
                          end: const Offset(1.0, 1.0),
                        ),

                        const SizedBox(height: 32),

                        // 按钮区域
                        Column(
                          children: [
                            // 逐张选择按钮（推荐）
                            SizedBox(
                              width: double.infinity,
                              height: 56,
                              child: ElevatedButton(
                                onPressed: _startSpreadLayout,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF8B5CF6),
                                  foregroundColor: Colors.white,
                                  elevation: 8,
                                  shadowColor: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Consumer<LanguageManager>(
                                  builder: (context, languageManager, child) {
                                    return Text(
                                      languageManager.translate('select_one_by_one_recommended'),
                                      style: const TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.w600,
                                      ),
                                      textAlign: TextAlign.center, // 居中对齐
                                      maxLines: 2, // 允许两行显示
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  },
                                ),
                              ),
                            ),

                            const SizedBox(height: 12),

                            // 自动抽牌按钮 - 增加高度和调整字体
                            SizedBox(
                              width: double.infinity,
                              height: 56, // 增加高度从48到56
                              child: OutlinedButton(
                                onPressed: _startAutoShuffle,
                                style: OutlinedButton.styleFrom(
                                  side: const BorderSide(color: Color(0xFF8B5CF6), width: 2),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), // 增加内边距
                                ),
                                child: Consumer<LanguageManager>(
                                  builder: (context, languageManager, child) {
                                    return Text(
                                      languageManager.translate('auto_draw_cards'),
                                      style: const TextStyle(
                                        fontSize: 18, // 增加字体大小从16到18
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF8B5CF6),
                                        letterSpacing: 0.5, // 增加字符间距
                                      ),
                                      maxLines: 1, // 确保单行显示
                                      overflow: TextOverflow.ellipsis, // 防止溢出
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],
                        ).animate().fadeIn(delay: 1000.ms, duration: 600.ms),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpreadLayout() {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: _buildCardLayout(constraints),
          ),
        );
      },
    );
  }

  Widget _buildCardLayout(BoxConstraints constraints) {
    // 自适应卡牌大小
    final availableWidth = constraints.maxWidth - 32; // 减去padding
    final availableHeight = constraints.maxHeight - 100; // 减去其他元素高度

    // 根据可用空间动态调整卡牌大小
    double cardWidth = 60.0;
    double cardHeight = 90.0;
    double spacing = 16.0;

    // 根据卡牌数量和可用空间调整大小
    if (cardCount >= 10) {
      cardWidth = 45.0;
      cardHeight = 67.5;
      spacing = 12.0;
    } else if (cardCount >= 6) {
      cardWidth = 50.0;
      cardHeight = 75.0;
      spacing = 14.0;
    }

    // 确保卡牌不会超出容器
    final maxCardWidth = availableWidth / (cardCount > 6 ? 4 : cardCount.clamp(1, 3)) - spacing;
    if (cardWidth > maxCardWidth) {
      cardWidth = maxCardWidth;
      cardHeight = cardWidth * 1.5; // 保持比例
    }

    switch (cardCount) {
      case 1:
        return _buildSingleCard(cardWidth, cardHeight);
      case 3:
        return _buildThreeCardLayout(cardWidth, cardHeight, spacing);
      case 4:
        return _buildFourCardLayout(cardWidth, cardHeight, spacing);
      case 5:
        return _buildFiveCardLayout(cardWidth, cardHeight, spacing);
      case 6:
        return _buildSixCardLayout(cardWidth, cardHeight, spacing);
      case 7:
        return _buildSevenCardLayout(cardWidth, cardHeight, spacing);
      case 10:
        return _buildCelticCrossLayout(cardWidth, cardHeight, spacing);
      case 12:
        return _buildTwelveCardLayout(cardWidth, cardHeight, spacing);
      default:
        return _buildSingleCard(cardWidth, cardHeight);
    }
  }

  Widget _buildSingleCard(double width, double height) {
    // 单张牌阵：确保上下左右都完全居中，使用自适应尺寸
    return Center( // 确保内容在容器中完全居中（包括上下居中）
      child: _buildCardPlaceholder(width, height, '1', true),
    );
  }

  Widget _buildThreeCardLayout(double width, double height, double spacing) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildCardPlaceholder(width, height, '1', false),
        SizedBox(width: spacing),
        _buildCardPlaceholder(width, height, '2', false),
        SizedBox(width: spacing),
        _buildCardPlaceholder(width, height, '3', false),
      ],
    );
  }

  Widget _buildFourCardLayout(double width, double height, double spacing) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '1', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '2', false),
          ],
        ),
        SizedBox(height: spacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '3', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '4', false),
          ],
        ),
      ],
    );
  }

  Widget _buildSixCardLayout(double width, double height, double spacing) {
    // 对于手机屏幕，使用2张3行的布局更适合
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '1', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '2', false),
          ],
        ),
        SizedBox(height: spacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '3', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '4', false),
          ],
        ),
        SizedBox(height: spacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '5', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '6', false),
          ],
        ),
      ],
    );
  }

  Widget _buildFiveCardLayout(double width, double height, double spacing) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildCardPlaceholder(width, height, '1', false),
        SizedBox(height: spacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '2', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '3', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '4', false),
          ],
        ),
        SizedBox(height: spacing),
        _buildCardPlaceholder(width, height, '5', false),
      ],
    );
  }

  Widget _buildSevenCardLayout(double width, double height, double spacing) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '1', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '2', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '3', false),
          ],
        ),
        SizedBox(height: spacing),
        _buildCardPlaceholder(width, height, '4', false),
        SizedBox(height: spacing),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(width, height, '5', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '6', false),
            SizedBox(width: spacing),
            _buildCardPlaceholder(width, height, '7', false),
          ],
        ),
      ],
    );
  }

  Widget _buildTwelveCardLayout(double width, double height, double spacing) {
    final smallWidth = width * 0.8;
    final smallHeight = height * 0.8;
    final smallSpacing = spacing * 0.8;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 第一行：4张牌
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(smallWidth, smallHeight, '1', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '2', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '3', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '4', false),
          ],
        ),
        SizedBox(height: smallSpacing),
        // 第二行：4张牌
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(smallWidth, smallHeight, '5', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '6', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '7', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '8', false),
          ],
        ),
        SizedBox(height: smallSpacing),
        // 第三行：4张牌
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(smallWidth, smallHeight, '9', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '10', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '11', false),
            SizedBox(width: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '12', false),
          ],
        ),
      ],
    );
  }

  Widget _buildCelticCrossLayout(double width, double height, double spacing) {
    final smallWidth = width * 0.8;
    final smallHeight = height * 0.8;
    final smallSpacing = spacing * 0.8;

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // 左侧十字
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(smallWidth, smallHeight, '1', false),
            SizedBox(height: smallSpacing),
            Row(
              children: [
                _buildCardPlaceholder(smallWidth, smallHeight, '4', false),
                SizedBox(width: smallSpacing),
                _buildCardPlaceholder(smallWidth, smallHeight, '2', false),
                SizedBox(width: smallSpacing),
                _buildCardPlaceholder(smallWidth, smallHeight, '3', false),
              ],
            ),
            SizedBox(height: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '5', false),
          ],
        ),
        SizedBox(width: spacing * 2),
        // 右侧竖列
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCardPlaceholder(smallWidth, smallHeight, '10', false),
            SizedBox(height: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '9', false),
            SizedBox(height: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '8', false),
            SizedBox(height: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '7', false),
            SizedBox(height: smallSpacing),
            _buildCardPlaceholder(smallWidth, smallHeight, '6', false),
          ],
        ),
      ],
    );
  }

  Widget _buildCardPlaceholder(double width, double height, String number, bool isSelected) {
    final cardIndex = int.parse(number) - 1;
    final originalPosition = cardIndex < cardPositions.length ? cardPositions[cardIndex] : '位置$number';
    final positionName = _translatePosition(originalPosition);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center, // 确保整个列上下居中
      children: [
        Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFF8B5CF6).withValues(alpha: 0.1)
                : Colors.grey.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF8B5CF6)
                  : Colors.grey.withValues(alpha: 0.3),
              width: isSelected ? 3 : 2, // 增加边框厚度：单张牌阵时更粗
              style: BorderStyle.solid,
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center, // 确保内容上下居中
              children: [
                Text(
                  number,
                  style: TextStyle(
                    fontSize: isSelected ? 20 : 16, // 单张牌阵时字体更大
                    fontWeight: FontWeight.w600,
                    color: isSelected
                        ? const Color(0xFF8B5CF6)
                        : Colors.grey[600],
                  ),
                ),
                if (isSelected) ...[
                  const SizedBox(height: 4),
                  const Icon(
                    Icons.add_circle_outline,
                    color: Color(0xFF8B5CF6),
                    size: 20,
                  ),
                ],
              ],
            ),
          ),
        ),
        const SizedBox(height: 8), // 增加间距
        SizedBox(
          width: width + 20,
          child: Text(
            positionName,
            style: TextStyle(
              fontSize: isSelected ? 12 : 10, // 单张牌阵时字体稍大
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    ).animate().fadeIn(
      delay: Duration(milliseconds: 800 + int.parse(number) * 100),
      duration: 400.ms,
    ).scale(
      begin: const Offset(0.8, 0.8),
      end: const Offset(1.0, 1.0),
    );
  }
}
