import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/services/invitation_service.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

class InvitationCodeScreen extends StatefulWidget {
  const InvitationCodeScreen({super.key});

  @override
  State<InvitationCodeScreen> createState() => _InvitationCodeScreenState();
}

class _InvitationCodeScreenState extends State<InvitationCodeScreen> {
  final TextEditingController _codeController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;

  Future<void> _submitCode() async {
    final code = _codeController.text.trim().toUpperCase();
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    if (code.isEmpty) {
      setState(() {
        _errorMessage = languageManager.translate('please_enter_code');
      });
      return;
    }

    if (code.length != 6) {
      setState(() {
        _errorMessage = languageManager.translate('code_should_be_6_chars');
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final invitationService = Provider.of<InvitationService>(context, listen: false);
      final result = await invitationService.useInvitationCode(code);

      if (result.success) {
        _showSuccessDialog(result.message);
      } else {
        setState(() {
          _errorMessage = result.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = languageManager.translate('network_error_retry');
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(String message) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.green.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 50,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              languageManager.translate('redemption_successful'),
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: FigmaTheme.textPrimary,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: const TextStyle(
                fontSize: 16,
                color: FigmaTheme.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pop();
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: FigmaTheme.primaryPink,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: Text(
                  languageManager.translate('awesome'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // 毛玻璃背景
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment(-1.0, -1.0),
                end: Alignment(1.0, 1.0),
                stops: [0.0, 0.25, 0.5, 0.75, 1.0],
                colors: [
                  Color(0xFF87CEEB),
                  Color(0xFFE6E6FA),
                  Color(0xFFF8BBD9),
                  Color(0xFFE6E6FA),
                  Color(0xFF87CEEB),
                ],
              ),
            ),
          ),

          // 顶部导航栏
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                decoration: FigmaTheme.createGlassDecoration(
                  opacity: 0.9,
                  radius: 0,
                ),
                child: Row(
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: () {
                          print('返回按钮被点击'); // 调试用
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: 48,
                          height: 48,
                          alignment: Alignment.center,
                          child: const Icon(
                            Icons.arrow_back_ios,
                            color: FigmaTheme.textPrimary,
                            size: 24,
                          ),
                        ),
                      ),
                    ),
                  Expanded(
                    child: Text(
                      languageManager.translate('invitation_code'),
                      style: const TextStyle(
                        color: FigmaTheme.textPrimary,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                    const SizedBox(width: 48),
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          Positioned.fill(
            child: SafeArea(
              child: SingleChildScrollView(
                padding: const EdgeInsets.fromLTRB(24, 80, 24, 24),
              child: Column(
                children: [
                  const SizedBox(height: 20),

                  // 标题和说明
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 20,
                    ),
                    child: Column(
                      children: [
                        Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                FigmaTheme.primaryPink.withOpacity(0.3),
                                FigmaTheme.primaryPink.withOpacity(0.1),
                              ],
                            ),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: FigmaTheme.primaryPink.withOpacity(0.2),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.card_giftcard,
                            size: 50,
                            color: FigmaTheme.primaryPink,
                          ),
                        ),

                        const SizedBox(height: 24),

                        Text(
                          languageManager.translate('enter_invitation_code'),
                          style: const TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: FigmaTheme.textPrimary,
                          ),
                        ),

                        const SizedBox(height: 12),

                        Text(
                          languageManager.translate('invitation_code_description'),
                          style: const TextStyle(
                            fontSize: 16,
                            color: FigmaTheme.textSecondary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 40),

                  // 邀请码输入框
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.8,
                      radius: 16,
                    ),
                    child: Column(
                      children: [
                        Text(
                          languageManager.translate('invitation_code'),
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: FigmaTheme.textPrimary,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: _errorMessage != null
                                ? Colors.red
                                : FigmaTheme.primaryPink.withOpacity(0.3),
                              width: 2,
                            ),
                            color: Colors.white.withOpacity(0.9),
                          ),
                          child: TextField(
                            controller: _codeController,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 8,
                              color: FigmaTheme.textPrimary,
                            ),
                            decoration: InputDecoration(
                              hintText: languageManager.translate('invitation_code_placeholder'),
                              hintStyle: const TextStyle(
                                fontSize: 16,
                                color: FigmaTheme.textMuted,
                                letterSpacing: 2,
                              ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Colors.transparent,
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 20,
                                vertical: 20,
                              ),
                            ),
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(6),
                              FilteringTextInputFormatter.allow(RegExp(r'[A-Za-z0-9]')),
                              UpperCaseTextFormatter(),
                            ],
                            onSubmitted: (_) => _submitCode(),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 错误信息
                  if (_errorMessage != null) ...[
                    const SizedBox(height: 12),
                    Text(
                      _errorMessage!,
                      style: const TextStyle(
                        color: Colors.red,
                        fontSize: 14,
                      ),
                    ),
                  ],

                  const SizedBox(height: 40),

                  // 提交按钮
                  Container(
                    width: double.infinity,
                    height: 56,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          FigmaTheme.primaryPink,
                          FigmaTheme.primaryPink.withOpacity(0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: FigmaTheme.primaryPink.withOpacity(0.3),
                          blurRadius: 15,
                          offset: const Offset(0, 8),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(16),
                        onTap: _isLoading ? null : _submitCode,
                        child: Container(
                          alignment: Alignment.center,
                          child: _isLoading
                            ? const SizedBox(
                                width: 24,
                                height: 24,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                languageManager.translate('redeem_invitation_code'),
                                style: const TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 40),

                  // 底部提示
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: FigmaTheme.createGlassDecoration(
                      opacity: 0.7,
                      radius: 16,
                    ),
                    child: Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: FigmaTheme.primaryPink,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              languageManager.translate('usage_instructions'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: FigmaTheme.textPrimary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        Text(
                          languageManager.translate('invitation_code_rules'),
                          style: const TextStyle(
                            fontSize: 14,
                            color: FigmaTheme.textSecondary,
                            height: 1.5,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          ),
        ],
      ),
    );
  }
}

/// 自动转换为大写的输入格式化器
class UpperCaseTextFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    return TextEditingValue(
      text: newValue.text.toUpperCase(),
      selection: newValue.selection,
    );
  }
}