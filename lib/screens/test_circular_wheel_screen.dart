import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/widgets/circular_card_wheel.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';

/// 测试圆轮选牌功能的页面
class TestCircularWheelScreen extends StatefulWidget {
  const TestCircularWheelScreen({super.key});

  @override
  State<TestCircularWheelScreen> createState() => _TestCircularWheelScreenState();
}

class _TestCircularWheelScreenState extends State<TestCircularWheelScreen> {
  TarotCard? selectedCard;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F3FF),
      appBar: AppBar(
        title: const Text('圆轮选牌测试'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Column(
        children: [
          // 显示选中的卡牌
          if (selectedCard != null) ...[
            Container(
              margin: const EdgeInsets.all(20),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.9),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                children: [
                  const Text(
                    '已选择卡牌',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: FigmaTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    selectedCard!.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: FigmaTheme.primaryPink,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    selectedCard!.description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: FigmaTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
          
          // 打开圆轮选牌的按钮
          Expanded(
            child: Center(
              child: ElevatedButton(
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => CircularCardWheel(
                        title: '滑动\n浏览全部卡片',
                        subtitle: '在心中默念牌意\n双指放大牌轮\n点击选牌',
                        onCardSelected: (card) {
                          setState(() {
                            selectedCard = card;
                          });
                          Navigator.pop(context);
                          
                          // 显示选牌成功提示
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('已选择：${card.name}'),
                              backgroundColor: FigmaTheme.primaryPink,
                              duration: const Duration(seconds: 2),
                            ),
                          );
                        },
                      ),
                    ),
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: FigmaTheme.primaryPink,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: 8,
                ),
                child: const Text(
                  '打开圆轮选牌',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
