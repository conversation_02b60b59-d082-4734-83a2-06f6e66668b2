import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/screens/spread_preview_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/subscription_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/subscription_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

class TopicQuestionScreen extends StatefulWidget {
  final String topicTitle;
  final String topicSubtitle;
  final String spreadType;

  const TopicQuestionScreen({
    super.key,
    required this.topicTitle,
    required this.topicSubtitle,
    required this.spreadType,
  });

  @override
  State<TopicQuestionScreen> createState() => _TopicQuestionScreenState();
}

class _TopicQuestionScreenState extends State<TopicQuestionScreen> {
  final TextEditingController _questionController = TextEditingController();
  bool _canProceed = false;

  @override
  void initState() {
    super.initState();
    _checkSubscriptionAndShowDialog();
    _questionController.addListener(_updateCanProceed);
  }

  // 检查订阅状态，如果是免费用户且选择了专题占卜，显示限制提示
  void _checkSubscriptionAndShowDialog() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final subscriptionService = Provider.of<SubscriptionService>(context, listen: false);
      
      // 如果是免费用户且选择的不是单张牌阵，显示限制提示
      if (!subscriptionService.isSubscribed && !widget.spreadType.contains('单张牌')) {
        _showFreeLimitationDialog();
      }
    });
  }

  // 显示免费会员限制对话框
  void _showFreeLimitationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.lock,
              color: Colors.orange,
              size: 24,
            ),
            const SizedBox(width: 8),
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('membership_feature'));
              },
            ),
          ],
        ),
        content: Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('free_member_single_spread_only'),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 12),
                Text(
                  languageManager.translate('themed_spreads_require_upgrade'),
                  style: const TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 8),
                Text(
                  '• ${languageManager.translate('unlock_all_themed_spreads')}\n• ${languageManager.translate('daily_multiple_ai_readings')}\n• ${languageManager.translate('detailed_divination_guidance')}\n• ${languageManager.translate('professional_tarot_advice')}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.pop(context); // 返回上一页
            },
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(
                  languageManager.translate('back'),
                  style: const TextStyle(color: Colors.grey),
                );
              },
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF6B46C1),
              foregroundColor: Colors.white,
            ),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('upgrade_now'));
              },
            ),
          ),
        ],
      ),
    );
  }

  void _updateCanProceed() {
    setState(() {
      _canProceed = _questionController.text.trim().isNotEmpty;
    });
  }

  void _startDivination() {
    if (_canProceed) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => SpreadPreviewScreen(
            question: _questionController.text.trim(),
            spreadType: widget.spreadType,
          ),
        ),
      );
    }
  }

  void _selectSuggestedQuestion(String question) {
    _questionController.text = question;
    _updateCanProceed();
  }

  @override
  void dispose() {
    _questionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 背景
          Positioned.fill(
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment(-1.0, -1.0),
                  end: Alignment(1.0, 1.0),
                  colors: [
                    Color(0xFF87CEEB),
                    Color(0xFFE6E6FA),
                    Color(0xFFF8BBD9),
                    Color(0xFFE6E6FA),
                    Color(0xFF87CEEB),
                  ],
                ),
              ),
            ),
          ),

          // 主要内容
          SafeArea(
            child: Column(
              children: [
                // 顶部导航栏
                Container(
                  height: 60,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      IconButton(
                        onPressed: () => Navigator.pop(context),
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          color: FigmaTheme.textPrimary,
                          size: 24,
                        ),
                      ),
                      const Spacer(),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            _getTranslatedTopicTitle(languageManager),
                            style: const TextStyle(
                              color: FigmaTheme.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                      const Spacer(),
                      const SizedBox(width: 48),
                    ],
                  ),
                ),

                // 内容区域
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 专题介绍卡片
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.08),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Consumer<LanguageManager>(
                                builder: (context, languageManager, child) {
                                  return Text(
                                    _getTranslatedTopicTitle(languageManager),
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.w700,
                                      color: Color(0xFF2D3748),
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(height: 12),
                              Text(
                                widget.topicSubtitle,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey[600],
                                  height: 1.5,
                                ),
                              ),
                              const SizedBox(height: 16),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: const Color(0xFF8B5CF6).withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                                  ),
                                ),
                                child: Consumer<LanguageManager>(
                                  builder: (context, languageManager, child) {
                                    return Text(
                                      '${languageManager.translate('spread')}: ${_getTranslatedSpreadType(languageManager)}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: Color(0xFF8B5CF6),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ).animate().fadeIn(duration: 600.ms).slideY(begin: 0.3, end: 0),

                        const SizedBox(height: 32),

                        // 问题输入标题
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('enter_specific_question'),
                              style: const TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF2D3748),
                              ),
                            );
                          },
                        ).animate().fadeIn(delay: 200.ms, duration: 600.ms),

                        const SizedBox(height: 16),

                        // 问题输入框
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.white.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(16),
                            border: Border.all(
                              color: Colors.white.withValues(alpha: 0.3),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withValues(alpha: 0.05),
                                blurRadius: 10,
                                offset: const Offset(0, 4),
                              ),
                            ],
                          ),
                          child: Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return TextField(
                                controller: _questionController,
                                maxLines: 4,
                                decoration: InputDecoration(
                                  hintText: languageManager.translate('question_input_hint'),
                                  border: InputBorder.none,
                                  contentPadding: const EdgeInsets.all(20),
                                  hintStyle: const TextStyle(
                                    color: Colors.grey,
                                    fontSize: 16,
                                  ),
                                ),
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Color(0xFF2D3748),
                                  height: 1.5,
                                ),
                              );
                            },
                          ),
                        ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

                        const SizedBox(height: 24),

                        // 建议问题
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            return Text(
                              languageManager.translate('or_choose_suggested_questions'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Color(0xFF2D3748),
                              ),
                            );
                          },
                        ).animate().fadeIn(delay: 600.ms, duration: 600.ms),

                        const SizedBox(height: 16),

                        // 建议问题列表
                        Consumer<LanguageManager>(
                          builder: (context, languageManager, child) {
                            final suggestedQuestions = _getSuggestedQuestions(languageManager);
                            return Column(
                              children: List.generate(suggestedQuestions.length, (index) {
                                return Container(
                                  margin: const EdgeInsets.only(bottom: 12),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      onTap: () => _selectSuggestedQuestion(suggestedQuestions[index]),
                                      borderRadius: BorderRadius.circular(12),
                                      child: Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: Colors.white.withValues(alpha: 0.7),
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.white.withValues(alpha: 0.3),
                                            width: 1,
                                          ),
                                        ),
                                        child: Text(
                                          suggestedQuestions[index],
                                          style: const TextStyle(
                                            fontSize: 15,
                                            color: Color(0xFF2D3748),
                                            height: 1.4,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ).animate().fadeIn(
                                  delay: Duration(milliseconds: 800 + index * 100),
                                  duration: 600.ms,
                                ).slideX(begin: 0.3, end: 0);
                              }),
                            );
                          },
                        ),

                        const SizedBox(height: 32),

                        // 开始占卜按钮
                        SizedBox(
                          width: double.infinity,
                          height: 56,
                          child: ElevatedButton(
                            onPressed: _canProceed ? _startDivination : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _canProceed 
                                  ? const Color(0xFF8B5CF6) 
                                  : Colors.grey[300],
                              foregroundColor: Colors.white,
                              elevation: _canProceed ? 8 : 0,
                              shadowColor: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Text(
                                  languageManager.translate('start_divination'),
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                );
                              },
                            ),
                          ),
                        ).animate().fadeIn(delay: 1200.ms, duration: 600.ms),

                        const SizedBox(height: 32),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 获取翻译后的专题标题
  String _getTranslatedTopicTitle(LanguageManager languageManager) {
    final topicTranslationMap = {
      // 快速决策类
      'Yes or No': 'yes_or_no',
      '二选一抉择': 'two_choice_decision',
      '三选一抉择': 'three_choice_decision',

      // 情感关系类
      '真爱时机': 'true_love_timing',
      '分手复合': 'breakup_reconciliation',
      '暗恋心意': 'secret_crush',
      '恋爱关系': 'topic_love_relationship',

      // 事业学业类
      '事业发展': 'career_development',
      '跳槽转职': 'job_change',
      '考试运势': 'exam_fortune',
      '学业考试': 'topic_academic_exams',

      // 内在探索类
      '自我认知': 'self_awareness',

      // 疗愈建议类
      '情绪疗愈': 'emotional_healing',

      // 宠物专题类
      '宠物情绪': 'pet_emotions',
      '宠物缘分': 'pet_compatibility',

      // 其他专题
      '财富丰盛': 'topic_wealth_abundance',
      '健康养生': 'topic_health_wellness',
      '人际关系': 'topic_interpersonal_relationships',
      '家庭和谐': 'topic_family_harmony',
      '创业投资': 'topic_entrepreneurship_investment',
    };

    final translationKey = topicTranslationMap[widget.topicTitle];
    if (translationKey != null) {
      return languageManager.translate(translationKey);
    }
    return widget.topicTitle; // 如果没有找到翻译，返回原文
  }

  // 获取翻译后的牌阵类型
  String _getTranslatedSpreadType(LanguageManager languageManager) {
    final spreadTranslationMap = {
      '单张牌阵': 'single_card_spread',
      '三张牌阵（经典）': 'three_card_spread_classic',
      '建议牌阵': 'advice_spread',
      '二选一牌阵': 'choice_spread',
      '爱情关系牌阵': 'love_relationship_spread',
      '事业成长牌阵': 'career_growth_spread',
      '六芒星牌阵': 'hexagram_spread',
      '凯尔特十字牌阵': 'celtic_cross_spread',
      '心灵疗愈牌阵': 'soul_healing_spread',
      '每月运势牌阵': 'monthly_fortune_spread',
      '七日运势牌阵': 'weekly_fortune_spread',
      '新月许愿牌阵': 'new_moon_wish_spread',
      '全年运势牌阵': 'yearly_fortune_spread',
    };

    final translationKey = spreadTranslationMap[widget.spreadType];
    if (translationKey != null) {
      return languageManager.translate(translationKey);
    }
    return widget.spreadType; // 如果没有找到翻译，返回原文
  }

  // 根据专题提供预设问题建议
  List<String> _getSuggestedQuestions(LanguageManager languageManager) {
    switch (widget.topicTitle) {
      case '自我认知':
        return [
          languageManager.translate('self_awareness_q1'),
          languageManager.translate('self_awareness_q2'),
          languageManager.translate('self_awareness_q3'),
          languageManager.translate('self_awareness_q4'),
        ];
      case '情绪疗愈':
        return [
          languageManager.translate('emotional_healing_q1'),
          languageManager.translate('emotional_healing_q2'),
          languageManager.translate('emotional_healing_q3'),
          languageManager.translate('emotional_healing_q4'),
        ];
      default:
        return [
          languageManager.translate('enter_specific_question'),
          languageManager.translate('what_guidance_needed'),
          languageManager.translate('what_confusion_needs_wisdom'),
        ];
    }
  }
}
