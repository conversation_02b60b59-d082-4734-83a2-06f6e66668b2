import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/widgets/circular_card_wheel.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/screens/spread_layout_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';

/// 圆轮选牌屏幕 - 1:1模仿参考设计
class CircularCardSelectionScreen extends StatefulWidget {
  final String question;
  final String spreadType;
  final int? currentPosition;
  final List<TarotCard> existingCards;
  final Function(TarotCard)? onCardSelected;

  const CircularCardSelectionScreen({
    super.key,
    required this.question,
    required this.spreadType,
    this.currentPosition,
    this.existingCards = const [],
    this.onCardSelected,
  });

  @override
  State<CircularCardSelectionScreen> createState() => _CircularCardSelectionScreenState();
}

class _CircularCardSelectionScreenState extends State<CircularCardSelectionScreen> {
  TarotCard? selectedCard;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Consumer<LanguageManager>(
        builder: (context, languageManager, child) {
          return CircularCardWheel(
            title: languageManager.translate('swipe_browse_all_cards'),
            subtitle: languageManager.translate('meditate_card_meaning_instructions'),
            onCardSelected: _handleCardSelection,
          );
        },
      ),
    );
  }

  void _handleCardSelection(TarotCard card) {
    setState(() {
      selectedCard = card;
    });

    // 显示选中的卡牌确认对话框
    _showCardConfirmationDialog(card);
  }

  void _showCardConfirmationDialog(TarotCard card) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: FigmaTheme.createGlassDecoration(
            opacity: 0.95,
            radius: 20,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 卡牌预览
              Container(
                width: 120,
                height: 180,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF8B5CF6),
                      Color(0xFF7C3AED),
                      Color(0xFF6D28D9),
                    ],
                  ),
                  border: Border.all(
                    color: Colors.white.withValues(alpha: 0.3),
                    width: 2,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        card.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            card.isReversed
                                ? languageManager.translate('reversed')
                                : languageManager.translate('upright'),
                            style: TextStyle(
                              color: card.isReversed ? Colors.red[200] : Colors.green[200],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          );
                        },
                      ),
                      const SizedBox(height: 8),
                      Text(
                        card.keywords.join(' • '),
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 12,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // 卡牌信息
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    languageManager.translate('you_selected'),
                    style: const TextStyle(
                      fontSize: 16,
                      color: FigmaTheme.textSecondary,
                    ),
                  );
                },
              ),
              const SizedBox(height: 8),
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  final positionText = card.isReversed
                      ? '（${languageManager.translate('reversed')}）'
                      : '（${languageManager.translate('upright')}）';
                  return Text(
                    '${card.name}$positionText',
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: FigmaTheme.textPrimary,
                    ),
                  );
                },
              ),
              const SizedBox(height: 16),
              Text(
                card.description,
                style: const TextStyle(
                  fontSize: 14,
                  color: FigmaTheme.textSecondary,
                  height: 1.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 24),

              // 按钮组
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(
                            color: FigmaTheme.textSecondary.withValues(alpha: 0.3),
                          ),
                        ),
                      ),
                      child: Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            languageManager.translate('reselect'),
                            style: const TextStyle(
                              color: FigmaTheme.textSecondary,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _confirmSelection(card),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: FigmaTheme.primaryPink,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: Consumer<LanguageManager>(
                        builder: (context, languageManager, child) {
                          return Text(
                            languageManager.translate('confirm_selection'),
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _confirmSelection(TarotCard card) {
    Navigator.pop(context); // 关闭对话框

    if (widget.onCardSelected != null) {
      // 如果有回调函数，调用它并返回
      widget.onCardSelected!(card);
      Navigator.pop(context, card);
    } else {
      // 否则导航到牌阵布局页面
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => SpreadLayoutScreen(
            question: widget.question,
            spreadType: widget.spreadType,
            selectedCards: [card],
          ),
        ),
      );
    }
  }
}
