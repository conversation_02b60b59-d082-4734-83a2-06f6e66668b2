import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/services/affirmation_service.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:ui'; // 添加液态玻璃效果支持

class ManifestationGoalScreen extends StatefulWidget {
  final Function(ManifestationGoal) onGoalSelected;
  final ManifestationGoal? currentGoal;

  const ManifestationGoalScreen({
    super.key,
    required this.onGoalSelected,
    this.currentGoal,
  });

  @override
  State<ManifestationGoalScreen> createState() => _ManifestationGoalScreenState();
}

class _ManifestationGoalScreenState extends State<ManifestationGoalScreen> {
  ManifestationGoal? _selectedGoal;

  @override
  void initState() {
    super.initState();
    _selectedGoal = widget.currentGoal;
  }

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return Consumer<BlurSettingsService>(
      builder: (context, blurSettings, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFF87CEEB),
                      Color(0xFFE6E6FA),
                      Color(0xFFF8BBD9),
                    ],
                  ),
                ),
              ),
              SafeArea(
                child: Column(
                  children: [
                    // 顶部导航栏
                    Container(
                      height: 60,
                      padding: const EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(
                              Icons.arrow_back_ios,
                              color: FigmaTheme.textPrimary,
                              size: 24,
                            ),
                          ),
                          const Spacer(),
                          Text(
                            languageManager.translate('set_manifestation_goal'),
                            style: const TextStyle(
                              color: FigmaTheme.textPrimary,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const Spacer(),
                          const SizedBox(width: 48), // 平衡布局
                        ],
                      ),
                    ),

                    // 内容区域
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            // 标题区域
                            Text(
                              languageManager.translate('choose_manifestation_direction'),
                              style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.w700,
                                color: Color(0xFF2D3748),
                              ),
                              textAlign: TextAlign.center,
                            ).animate().fadeIn(duration: 600.ms),

                            const SizedBox(height: 16),

                            const SizedBox(height: 32),

                            // 目标选择卡片
                            ...ManifestationGoal.values.asMap().entries.map((entry) {
                              final index = entry.key;
                              final goal = entry.value;
                              final isSelected = _selectedGoal == goal;
                              
                              return Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      _selectedGoal = goal;
                                    });
                                  },
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: BackdropFilter(
                                      filter: blurSettings.getImageFilter(), // 使用动态模糊度
                                      child: Container(
                                        width: double.infinity,
                                        padding: const EdgeInsets.all(20),
                                        decoration: BoxDecoration(
                                          color: isSelected 
                                              ? Colors.white.withOpacity(0.15) // 半透明毛玻璃
                                              : Colors.white.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected 
                                                ? Colors.white.withOpacity(0.6)
                                                : Colors.white.withOpacity(0.3),
                                            width: isSelected ? 2 : 1,
                                          ),
                                          boxShadow: isSelected ? [
                                            BoxShadow(
                                              color: Colors.white.withOpacity(0.3),
                                              blurRadius: 20,
                                              offset: const Offset(0, 8),
                                            ),
                                          ] : [
                                            BoxShadow(
                                              color: Colors.black.withOpacity(0.08),
                                              blurRadius: 10,
                                              offset: const Offset(0, 4),
                                            ),
                                          ],
                                        ),
                                        child: Row(
                                          children: [
                                            // 图标
                                            Container(
                                              width: 60,
                                              height: 60,
                                              decoration: BoxDecoration(
                                                color: isSelected 
                                                    ? const Color(0xFF8B5CF6)
                                                    : Colors.grey[100],
                                                borderRadius: BorderRadius.circular(16),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  goal.emoji,
                                                  style: const TextStyle(fontSize: 28),
                                                ),
                                              ),
                                            ),
                                            
                                            const SizedBox(width: 16),
                                            
                                            // 内容
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    _getGoalName(goal),
                                                    style: TextStyle(
                                                      fontSize: 20,
                                                      fontWeight: FontWeight.w700,
                                                      color: isSelected
                                                          ? const Color(0xFF8B5CF6)
                                                          : const Color(0xFF2D3748),
                                                    ),
                                                  ),
                                                  const SizedBox(height: 4),
                                                  Text(
                                                    _getGoalDescription(goal),
                                                    style: TextStyle(
                                                      fontSize: 14,
                                                      color: Colors.grey[600],
                                                      height: 1.3,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            
                                            // 选中指示器
                                            if (isSelected)
                                              const Icon(
                                                Icons.check_circle,
                                                color: Color(0xFF8B5CF6),
                                                size: 24,
                                              ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ).animate().fadeIn(
                                delay: Duration(milliseconds: 400 + index * 100),
                                duration: 600.ms,
                              ).slideX(
                                begin: 0.3,
                                end: 0,
                              );
                            }),

                            const SizedBox(height: 32),

                            // 确认按钮
                            if (_selectedGoal != null)
                              SizedBox(
                                width: double.infinity,
                                height: 56,
                                child: ElevatedButton(
                                  onPressed: () {
                                    _showManifestationTrainingDialog();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF8B5CF6),
                                    foregroundColor: Colors.white,
                                    elevation: 8,
                                    shadowColor: const Color(0xFF8B5CF6).withValues(alpha: 0.3),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(16),
                                    ),
                                  ),
                                  child: Text(
                                    languageManager.translate('confirm_selection'),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ).animate().fadeIn(delay: 1000.ms, duration: 600.ms),

                            const SizedBox(height: 32),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _showManifestationTrainingDialog() {
    // 如果选择的是财富目标，显示版本选择对话框
    if (_selectedGoal == ManifestationGoal.wealth) {
      _showWealthVersionDialog();
      return;
    }

    // 其他目标直接显示训练对话框
    _showTrainingDialog();
  }

  void _showWealthVersionDialog() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Text(_selectedGoal!.emoji, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Text(
              languageManager.translate('choose_wealth_version'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              languageManager.translate('choose_wealth_style'),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // 东方版选项
            GestureDetector(
              onTap: () => _navigateToManifestation(ManifestationVersion.eastern),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Column(
                  children: [
                    const Text(
                      '🏮',
                      style: TextStyle(fontSize: 32),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageManager.translate('eastern_version'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.red,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      languageManager.translate('eastern_elements'),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // 西方版选项
            GestureDetector(
              onTap: () => _navigateToManifestation(ManifestationVersion.western),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  children: [
                    const Text(
                      '💎💵',
                      style: TextStyle(fontSize: 28),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      languageManager.translate('western_version'),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.green,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      languageManager.translate('western_elements'),
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.grey,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context); // 关闭对话框
            },
            child: Text(languageManager.translate('cancel')),
          ),
        ],
      ),
    );
  }

  void _navigateToManifestation(ManifestationVersion version) {
    widget.onGoalSelected(_selectedGoal!);

    // 进入对应版本的显化页面
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final initialAffirmation = languageManager.currentLanguage.startsWith('en')
        ? 'I believe the universe will bring me the best arrangements, and I deserve to have a beautiful life.'
        : '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活。';

    // 先关闭对话框，然后导航
    Navigator.pop(context); // 关闭版本选择对话框

    // 使用pushReplacement替代多次pop + push
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => ManifestationAnimationScreen(
          goal: _selectedGoal!,
          version: version,
          affirmation: initialAffirmation,
        ),
      ),
    );
  }

  void _showTrainingDialog() {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Text(_selectedGoal!.emoji, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            Text(
              languageManager.translate('manifestation_goal_set'),
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${languageManager.translate('selected_manifestation')}${_getGoalName(_selectedGoal!)}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              languageManager.translate('enter_training_question'),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // 只设定目标，不进入训练
              widget.onGoalSelected(_selectedGoal!);
              Navigator.pop(context); // 关闭对话框
              Navigator.pop(context); // 返回上一页
            },
            child: Text(languageManager.translate('later')),
          ),
          ElevatedButton(
            onPressed: () {
              // 设定目标并进入训练
              widget.onGoalSelected(_selectedGoal!);

              // 进入肯定语显化页面（其他目标不需要版本参数）
              final initialAffirmation = languageManager.currentLanguage.startsWith('en')
                  ? 'I believe the universe will bring me the best arrangements, and I deserve to have a beautiful life.'
                  : '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活。';

              // 先关闭对话框，然后导航
              Navigator.pop(context); // 关闭对话框

              // 使用pushReplacement替代多次pop + push
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => ManifestationAnimationScreen(
                    goal: _selectedGoal!,
                    affirmation: initialAffirmation,
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF8B5CF6),
              foregroundColor: Colors.white,
            ),
            child: Text(languageManager.translate('confirm')),
          ),
        ],
      ),
    );
  }

  String _getGoalName(ManifestationGoal goal) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (goal) {
      case ManifestationGoal.wealth:
        return languageManager.translate('wealth_goal');
      case ManifestationGoal.career:
        return languageManager.translate('career_goal');
      case ManifestationGoal.beauty:
        return languageManager.translate('beauty_goal');
      case ManifestationGoal.fame:
        return languageManager.translate('fame_goal');
      case ManifestationGoal.love:
        return languageManager.translate('love_goal');
    }
  }

  String _getGoalDescription(ManifestationGoal goal) {
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    switch (goal) {
      case ManifestationGoal.wealth:
        return languageManager.translate('wealth_description');
      case ManifestationGoal.career:
        return languageManager.translate('career_description');
      case ManifestationGoal.beauty:
        return languageManager.translate('beauty_description');
      case ManifestationGoal.fame:
        return languageManager.translate('fame_description');
      case ManifestationGoal.love:
        return languageManager.translate('love_description');
    }
  }
}
