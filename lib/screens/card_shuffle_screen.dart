import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/chat_reading_screen.dart';
import 'package:ai_tarot_reading/screens/card_confirmation_screen.dart';
import 'package:ai_tarot_reading/screens/spread_layout_screen.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'dart:math' as math;
import 'package:ai_tarot_reading/widgets/app_background.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/data/tarot_spreads.dart';
import 'package:ai_tarot_reading/services/card_preference_service.dart';

class CardShuffleScreen extends StatefulWidget {
  final String question;
  final String spreadType;
  final int? currentPosition; // 当前要选择的位置索引
  final List<TarotCard>? existingCards; // 已经选择的卡牌
  final Function(TarotCard)? onCardSelected; // 选择卡牌的回调
  final bool autoMode; // 自动模式：洗牌后直接机器抽牌进入解读
  final bool skipShuffle; // 跳过洗牌动画，直接显示半圆形卡牌
  final List<TarotCard>? shuffledCards; // 预洗好的卡牌顺序

  const CardShuffleScreen({
    super.key,
    required this.question,
    required this.spreadType,
    this.currentPosition,
    this.existingCards,
    this.onCardSelected,
    this.autoMode = false,
    this.skipShuffle = false,
    this.shuffledCards, // 可选的预洗好的卡牌
  });

  @override
  State<CardShuffleScreen> createState() => _CardShuffleScreenState();
}

class _CardShuffleScreenState extends State<CardShuffleScreen>
    with TickerProviderStateMixin {
  late AnimationController _shuffleController;
  late AnimationController _cardController;

  List<CardData> cards = [];
  bool isShuffling = true;
  int? selectedCardId;
  bool showCardSelection = false;
  List<String> selectedCards = []; // 存储已选择的塔罗牌ID
  List<TarotCard> availableCards = []; // 可选择的塔罗牌
  TarotSpread? currentSpread; // 当前牌阵信息

  // 圆形轮子的状态变量
  double _wheelRotation = 0.0; // 轮子旋转角度
  double _wheelScale = 1.0; // 轮子缩放比例
  double _initialScale = 1.0; // 缩放开始时的初始比例

  // 根据牌阵类型确定需要选择的卡牌数量
  int get requiredCardCount {
    final spread = TarotSpread.getSpreadByName(widget.spreadType);
    return spread?.cardCount ?? 1;
  }

  @override
  void initState() {
    super.initState();

    _shuffleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _cardController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // 初始化塔罗牌数据
    _initializeTarotCards();
    // 创建多张卡牌用于洗牌动画
    _initializeCards();

    // 如果是自动模式，直接自动选牌
    if (widget.autoMode) {
      // 延迟一点时间让界面完成初始化
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _autoSelectCards();
        }
      });
    } else {
      // 非自动模式：直接跳过洗牌，进入选牌模式
      isShuffling = false;
      showCardSelection = false; // 确保显示圆轮
    }
  }

  void _initializeTarotCards() {
    // 如果有预洗好的卡牌，使用它们；否则重新洗牌
    if (widget.shuffledCards != null) {
      availableCards = List<TarotCard>.from(widget.shuffledCards!);
    } else {
      // 获取所有塔罗牌并打乱顺序，同时随机分配正逆位
      final random = math.Random();
      // 使用完整的78张塔罗牌数据（TarotCardsData）
      final allCards = TarotCardsData.allCards;
      availableCards = allCards.map((card) {
        final isReversed = random.nextBool(); // 50%概率逆位
        return card.copyWith(isReversed: isReversed);
      }).toList()..shuffle();
    }
    // 获取当前牌阵信息
    currentSpread = TarotSpread.getSpreadByName(widget.spreadType);
  }

  String _getCurrentPositionHint() {
    if (widget.currentPosition != null) {
      // 单张选择模式
      if (currentSpread != null && widget.currentPosition! < currentSpread!.positionKeys.length) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        final positionKey = currentSpread!.positionKeys[widget.currentPosition!];
        final translatedPosition = languageManager.translate(positionKey);
        return '${languageManager.translate('this_card_represents')}：$translatedPosition';
      }
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      return '${languageManager.translate('card_number_prefix')}${widget.currentPosition! + 1}${languageManager.translate('card_number_suffix')}';
    }

    // 多张选择模式
    if (currentSpread == null || selectedCards.length >= currentSpread!.positionKeys.length) {
      return '';
    }
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    final nextPositionKey = currentSpread!.positionKeys[selectedCards.length];
    final translatedPosition = languageManager.translate(nextPositionKey);
    return '${languageManager.translate('next_card_represents')}：$translatedPosition';
  }

  String _getSelectionHint(LanguageManager languageManager) {
    if (widget.currentPosition != null) {
      // 单张选择模式
      return languageManager.translate('please_select_card');
    }

    // 多张选择模式
    if (requiredCardCount == 1) {
      return languageManager.translate('please_select_card');
    } else {
      final cardsText = languageManager.translate('please_select_cards').replaceAll('{count}', requiredCardCount.toString());
      final selectedText = languageManager.translate('cards_selected').replaceAll('{selected}', selectedCards.length.toString());
      return '$cardsText ($selectedText)';
    }
  }

  bool _shouldShowPositionHint() {
    if (widget.currentPosition != null) {
      // 单张选择模式，总是显示位置提示
      return true;
    }

    // 多张选择模式
    return currentSpread != null && selectedCards.length < requiredCardCount;
  }

  void _initializeCards() {
    final random = math.Random();
    cards.clear();

    // 🎯 创建像空气一样铺满整个区域的散乱卡牌效果
    for (int i = 0; i < 72; i++) {
      // 🎯 确保卡牌真正铺满整个洗牌区域，包括边角
      // 使用更激进的分布策略，确保覆盖每个角落

      // 🌟 生成完全随机的位置，覆盖整个洗牌区域
      // 使用 0 到 1 的范围，这样更容易控制分布
      final x = random.nextDouble(); // 0 到 1，覆盖整个宽度
      final y = random.nextDouble(); // 0 到 1，覆盖整个高度

      // 🎯 增加一些卡牌专门放在边角，确保边角也有卡牌
      double finalX = x;
      double finalY = y;

      // 每8张卡牌中有一张专门放在边角
      if (i % 8 == 0) {
        // 强制放在四个角落之一
        final corner = i % 4;
        switch (corner) {
          case 0: // 左上角
            finalX = random.nextDouble() * 0.2; // 0-20%区域
            finalY = random.nextDouble() * 0.2;
            break;
          case 1: // 右上角
            finalX = 0.8 + random.nextDouble() * 0.2; // 80-100%区域
            finalY = random.nextDouble() * 0.2;
            break;
          case 2: // 左下角
            finalX = random.nextDouble() * 0.2;
            finalY = 0.8 + random.nextDouble() * 0.2;
            break;
          case 3: // 右下角
            finalX = 0.8 + random.nextDouble() * 0.2;
            finalY = 0.8 + random.nextDouble() * 0.2;
            break;
        }
      }

      // 随机旋转角度
      final rotation = (random.nextDouble() - 0.5) * 1.0; // 更大的旋转角度

      cards.add(CardData(
        id: i,
        x: finalX, // 0到1的相对位置
        y: finalY, // 0到1的相对位置
        rotation: rotation,
        scale: 1.0,
      ));
    }
  }

  void _startShuffling() {
    // 开始有节奏的洗牌动画：运动2秒，停止1秒，运动2秒
    _startRhythmicShuffle();
  }

  void _startRhythmicShuffle() {
    // 第一阶段：运动2秒
    _shuffleController.repeat();

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // 停止1秒
        _shuffleController.stop();

        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) {
            // 第二阶段：再运动2秒
            _shuffleController.repeat();

            Future.delayed(const Duration(seconds: 2), () {
              if (mounted) {
                setState(() {
                  isShuffling = false;
                });
                _shuffleController.stop();

                // 如果是自动模式，直接机器抽牌进入解读
                if (widget.autoMode) {
                  _autoSelectCards();
                } else {
                  _arrangeCardsInLine();
                }
              }
            });
          }
        });
      }
    });
  }

  void _arrangeCardsInLine() {
    // 将卡牌排列成圆形扇形，供用户选择
    // 确保在widget构建完成后再调用
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _arrangeCardsInCircle();
      }
    });
  }

  void _arrangeCardsInCircle() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // 🎯 自适应靠左计算
    // 玻璃框的实际可用区域
    final containerWidth = screenWidth - 40; // 减去左右padding
    final containerHeight = screenHeight - 299; // 减去顶部和底部空间，减少1/15 (320-21=299)

    // 半圆的中心点 - 居中放置，让半圆凸的朝下
    final centerX = containerWidth / 2; // 居中放置
    final centerY = containerHeight * 0.3; // 靠上放置，让半圆凸的朝下

    // 🔧 自适应半径计算 - 确保半圆完全适应容器且不被遮挡
    // 考虑半圆的实际占用空间和卡牌大小
    final maxRadiusForWidth = (containerWidth * 0.8) / 2; // 使用80%宽度空间
    final maxRadiusForHeight = (containerHeight - 100) / 2; // 留出更多垂直空间给卡牌
    final radius = math.min(maxRadiusForWidth, maxRadiusForHeight);

    // ✅ 修正半圆布局：从左到右向下弯曲，凸的朝下
    const startAngle = 0; // 0° (右边)
    const sweepAngle = math.pi;  // 180°，从右到左向下弯曲，凸的朝下

    // 使用所有100张卡牌来形成半圆
    const totalCards = 100;

    // 🎯 自适应卡牌缩放 - 根据容器和半径大小动态调整，确保不被遮挡
    // 基于容器大小和半径计算最佳卡牌尺寸
    final baseCardScale = math.min(1.5, radius / 60); // 提高基础缩放
    final containerBasedScale = math.min(
      containerWidth / 800, // 基于容器宽度
      containerHeight / 600, // 基于容器高度
    );
    final cardScale = math.min(baseCardScale, containerBasedScale * 2); // 综合考虑
    final cardWidth = 60 * cardScale; // 动态宽度
    final cardHeight = 90 * cardScale; // 动态高度

    for (int i = 0; i < totalCards && i < cards.length; i++) {
      // ✅ 正确的半圆角度计算：从左到右向下弯曲
      final angle = startAngle + (sweepAngle / (totalCards - 1)) * i;

      // ✅ 正确的位置计算
      final x = centerX + radius * math.cos(angle);
      final y = centerY + radius * math.sin(angle);

      // 🎯 自适应卡牌位置计算（减去卡牌尺寸的一半以居中）
      cards[i].targetX = x - cardWidth / 2;
      cards[i].targetY = y - cardHeight / 2;

      // ✅ 卡牌朝向调整：让卡牌稍微朝向圆心，保持静止
      cards[i].targetRotation = angle + math.pi / 2;
      cards[i].targetScale = cardScale; // 自适应缩放
    }

    // 静止显示，不使用动画控制器
    setState(() {
      // 直接设置卡牌位置，不使用动画
      for (int i = 0; i < cards.length; i++) {
        cards[i].x = cards[i].targetX;
        cards[i].y = cards[i].targetY;
        cards[i].rotation = cards[i].targetRotation;
        cards[i].scale = cards[i].targetScale;
      }
    });
  }

  @override
  void dispose() {
    _shuffleController.dispose();
    _cardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      extendBody: true,
      body: AppBackground(
        child: Stack(
          children: [

          // ☁️ 苹果风格云朵装饰效果 - 与主页一致
          Positioned.fill(
            child: Stack(
              children: [
                // 云朵1
                Positioned(
                  top: 80,
                  left: 30,
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.4),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.3),
                          blurRadius: 8,
                          spreadRadius: 4,
                        ),
                      ],
                    ),
                  ),
                ),
                // 云朵2
                Positioned(
                  top: 200,
                  right: 40,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.3),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.2),
                          blurRadius: 6,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                  ),
                ),
                // 云朵3
                Positioned(
                  bottom: 250,
                  left: 60,
                  child: Container(
                    width: 90,
                    height: 90,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.35),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.white.withValues(alpha: 0.25),
                          blurRadius: 7,
                          spreadRadius: 3,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 主要内容
          SafeArea(
            child: Stack(
              children: [
            // 🍎 简洁的顶部导航栏
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                height: 60,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    // 🍎 苹果风格返回按钮
                    IconButton(
                      onPressed: () => Navigator.pop(context),
                      icon: const Icon(
                        Icons.arrow_back_ios,
                        color: FigmaTheme.textPrimary,
                        size: 24,
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),
            ),

            // 🎯 左上角标题文字 - 按照参考图位置
            Positioned(
              top: 80,
              left: 30,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 主标题 - 左对齐显示，放大30%
                  Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('meditate_and_ask'),
                        style: const TextStyle(
                          fontSize: 28.6, // 22 * 1.3 = 28.6，放大30%
                          fontWeight: FontWeight.w700,
                          color: Colors.black,
                          fontFamily: 'Inter',
                          shadows: [
                            Shadow(
                              color: Colors.white54,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2, // 允许最多2行
                        overflow: TextOverflow.ellipsis, // 超出时显示省略号
                        softWrap: true, // 启用自动换行
                      );
                    },
                  ),
                  const SizedBox(height: 6),
                  // 副标题，放大30%
                  Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        _getSelectionHint(languageManager),
                        style: const TextStyle(
                          fontSize: 19.5, // 15 * 1.3 = 19.5，放大30%
                          fontWeight: FontWeight.w500,
                          color: Colors.black,
                          fontFamily: 'Inter',
                          shadows: [
                            Shadow(
                              color: Colors.white54,
                              offset: Offset(0, 1),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),

            // 🎯 移除左下角操作提示

            // 🎴 选牌状态的圆形卡牌轮子 - 占满整个屏幕，无边界限制
            // 🎯 直接显示选牌圆轮，不再有洗牌动画
            if (!showCardSelection)
              Positioned.fill(
                child: _buildCircularCardWheel(),
              ),

            // 选中卡牌信息显示（如果需要）
            if (selectedCards.isNotEmpty && !isShuffling)
              Positioned(
                left: 20,
                right: 20,
                top: 20,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: _buildSelectedCardsInfo(),
                ),
              ),

            // 🎯 不再需要洗牌按钮
              ],
            ),
          ),
          ],
        ),
      ),
    );
  }

  Widget _buildShufflingCard(CardData card) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return AnimatedBuilder(
          animation: _shuffleController,
          builder: (context, child) {
            final animationValue = _shuffleController.value;

            // 🎯 洗牌区域尺寸 - 现在是精确的指定区域
            // Positioned 已经限制了区域：left: 100, top: 200, right: 100, bottom: 350
            final shuffleAreaWidth = constraints.maxWidth;  // 屏幕宽度 - 200 (左右各100)
            final shuffleAreaHeight = constraints.maxHeight; // 屏幕高度 - 550 (top200 + bottom350)

            // 时间偏移，让卡牌有不同的动画节奏
            final timeOffset = card.id * 0.15;
            final animTime = (animationValue + timeOffset) % 1.0;

            // 🎯 卡牌尺寸
            const cardWidth = 75.0;
            const cardHeight = 110.0;

            // 🎯 让卡牌像空气一样铺满整个区域
            // 🌟 不减去卡牌尺寸，让卡牌可以延伸到边界甚至稍微超出
            final extendedWidth = shuffleAreaWidth + cardWidth * 0.5; // 允许卡牌延伸
            final extendedHeight = shuffleAreaHeight + cardHeight * 0.5; // 允许卡牌延伸

            // 🎯 将 card.x 和 card.y（0到1）直接转换为实际像素位置
            // 让卡牌真正铺满整个区域，包括边角
            final baseX = card.x * extendedWidth - cardWidth * 0.25; // 可以稍微超出左边界
            final baseY = card.y * extendedHeight - cardHeight * 0.25; // 可以稍微超出上边界

            // 🎯 调试信息 - 打印前几张卡牌的位置
            if (card.id < 3) {
              print('🎴 卡牌${card.id}: 原始位置(${card.x.toStringAsFixed(2)}, ${card.y.toStringAsFixed(2)}) -> 像素位置(${baseX.toStringAsFixed(1)}, ${baseY.toStringAsFixed(1)}) 区域大小(${extendedWidth.toStringAsFixed(1)}, ${extendedHeight.toStringAsFixed(1)})');
            }

            // 🌊 轻微的浮动动画
            final floatX = math.sin(animTime * 2 * math.pi + card.id * 0.7) * 8.0; // 增加浮动范围
            final floatY = math.cos(animTime * 1.8 * math.pi + card.id * 0.5) * 6.0; // 增加浮动范围

            final shuffleX = baseX + floatX;
            final shuffleY = baseY + floatY;

            // 🔄 自然的旋转 - 使用初始化时的旋转加上浮动
            final floatRotation = math.sin(animTime * 1.5 * math.pi + card.id * 0.6) * 0.15; // 增加旋转幅度
            final shuffleRotation = card.rotation + floatRotation;

            // 🎯 不再限制卡牌位置，让它们自由分布，像空气一样铺满
            // 移除clamp限制，让卡牌可以延伸到边界外

            return Positioned(
              left: shuffleX,
              top: shuffleY,
              child: Transform.rotate(
                angle: shuffleRotation,
                child: Container(
                  width: cardWidth,
                  height: cardHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                      ),
                      BoxShadow(
                        color: Colors.white.withValues(alpha: 0.15),
                        blurRadius: 3,
                        offset: const Offset(0, -2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(14),
                    child: Consumer<CardPreferenceService>(
                      builder: (context, cardService, child) {
                        return Image.asset(
                          'assets/images/${cardService.selectedCardBack}',
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Image.asset(
                              'assets/images/IMG_0992 42.png',
                              fit: BoxFit.cover,
                            );
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 🎯 180度半圆卡牌轮子 - 一打开就是180度，支持旋转查看其他牌
  Widget _buildCircularCardWheel() {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 🎯 正确布局：圆心在右边缘，直径范围从top200到bottom140
        final centerX = constraints.maxWidth; // 圆心在右边缘

        // 计算可用的垂直空间：屏幕高度 - top(200) - bottom(150)
        // 🎯 修改底部边界为150px
        final screenHeight = MediaQuery.of(context).size.height;
        final availableHeight = screenHeight - 200 - 150; // 可用高度，底部改为150px
        final centerY = 200 + availableHeight / 2; // 圆心在可用范围的中间

        // 半径为可用高度的一半，这样圆的直径正好填满从top200到bottom150的空间
        final baseRadius = availableHeight / 2;

        return GestureDetector(
          // 🎯 只使用scale手势，避免冲突
          onScaleStart: (details) {
            _initialScale = _wheelScale;
          },
          onScaleUpdate: (details) {
            setState(() {
              // 🎯 双指缩放功能
              if (details.scale != 1.0) {
                _wheelScale = (_initialScale * details.scale).clamp(0.5, 3.0);
              }

              // 🎯 单指滑动旋转功能（当scale接近1时）
              if ((details.scale - 1.0).abs() < 0.1) {
                _wheelRotation += details.focalPointDelta.dy * 0.008;
              }
            });
          },
          // 🎯 设置手势行为，避免与子组件冲突
          behavior: HitTestBehavior.deferToChild,
          child: Stack(
            children: List.generate(78, (index) {
              // 🎯 计算每张卡牌在完整圆形上的角度
              final normalizedIndex = index / 78; // 0到1
              final fullCircleAngle = normalizedIndex * 2 * math.pi + _wheelRotation;

              // 🎯 使用完整圆形的角度，不做范围限制
              // 让自然的屏幕边界来"切掉"右半部分
              final displayAngle = fullCircleAngle % (2 * math.pi);

              // 应用缩放的半径
              final radius = baseRadius * _wheelScale;

              // 计算卡牌位置 - 放大33%
              final cardWidth = 60.0 * 1.33 * _wheelScale; // 放大33%
              final cardHeight = 90.0 * 1.33 * _wheelScale; // 放大33%
              final x = centerX + math.cos(displayAngle) * radius - cardWidth / 2;
              final y = centerY + math.sin(displayAngle) * radius - cardHeight / 2;

              // 🎯 不做边界限制，让屏幕自然切掉右半部分
              // 只有左半部分的卡牌会显示在屏幕内

              // 计算卡牌旋转角度，让卡牌朝向圆心
              final cardRotation = displayAngle + math.pi / 2;

              // 判断是否显示编号（只有放大时显示）
              final showNumber = _wheelScale > 1.5;

              return Positioned(
                left: x,
                top: y,
                child: Transform.rotate(
                  angle: cardRotation,
                  child: GestureDetector(
                    onTap: () => _selectCard(index),
                    child: Container(
                      width: cardWidth,
                      height: cardHeight,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12 * _wheelScale),
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 1 * _wheelScale,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 8 * _wheelScale,
                            offset: Offset(0, 4 * _wheelScale),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12 * _wheelScale),
                        child: Stack(
                          children: [
                            // 卡牌背面图片
                            Consumer<CardPreferenceService>(
                              builder: (context, cardService, child) {
                                return Image.asset(
                                  'assets/images/${cardService.selectedCardBack}',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                  errorBuilder: (context, error, stackTrace) {
                                    // 如果用户选择的图片加载失败，使用默认图片
                                    return Image.asset(
                                      'assets/images/IMG_0992 42.png',
                                      fit: BoxFit.cover,
                                      width: double.infinity,
                                      height: double.infinity,
                                    );
                                  },
                                );
                              },
                            ),
                            // 卡牌编号（只有放大时显示）
                            if (showNumber)
                              Positioned(
                                top: 4 * _wheelScale,
                                left: 4 * _wheelScale,
                                child: Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 6 * _wheelScale,
                                    vertical: 2 * _wheelScale,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.black.withValues(alpha: 0.7),
                                    borderRadius: BorderRadius.circular(8 * _wheelScale),
                                  ),
                                  child: Text(
                                    '${index + 1}',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 12 * _wheelScale,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }

  Widget _buildSelectableCard(CardData card) {
    final isSelected = selectedCards.contains(card.id);

    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        final currentX = card.x + (card.targetX - card.x) * _cardController.value;
        final currentY = card.y + (card.targetY - card.y) * _cardController.value;
        final currentRotation = card.rotation + (card.targetRotation - card.rotation) * _cardController.value;
        final currentScale = card.scale + (card.targetScale - card.scale) * _cardController.value;

        return Positioned(
          left: currentX,
          top: currentY,
          child: Transform.rotate(
            angle: currentRotation,
            child: Transform.scale(
              scale: currentScale,
              child: GestureDetector(
                onTap: () => _selectCard(card.id),
                child: Container(
                  width: 65,
                  height: 95,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(14),
                    border: isSelected
                        ? Border.all(
                            color: const Color(0xFFE91E63),
                            width: 2.5,
                          )
                        : Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                    boxShadow: [
                      BoxShadow(
                        color: isSelected
                            ? const Color(0xFFE91E63).withValues(alpha: 0.4)
                            : Colors.black.withValues(alpha: 0.15),
                        blurRadius: isSelected ? 18 : 10,
                        offset: Offset(0, isSelected ? 8 : 5),
                      ),
                      if (isSelected)
                        BoxShadow(
                          color: const Color(0xFFE91E63).withValues(alpha: 0.2),
                          blurRadius: 25,
                          offset: const Offset(0, 12),
                        ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(14),
                    child: Stack(
                      children: [
                        Consumer<CardPreferenceService>(
                          builder: (context, cardService, child) {
                            return Image.asset(
                              'assets/images/${cardService.selectedCardBack}',
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              errorBuilder: (context, error, stackTrace) {
                                return Image.asset(
                                  'assets/images/IMG_0992 42.png',
                                  fit: BoxFit.cover,
                                  width: double.infinity,
                                  height: double.infinity,
                                );
                              },
                            );
                          },
                        ),
                        if (isSelected)
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(14),
                              border: Border.all(
                                color: const Color(0xFFE91E63).withValues(alpha: 0.8),
                                width: 2,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              ).animate().scale(
                duration: 250.ms,
                curve: Curves.easeOutBack,
              ),
            ),
          ),
        );
      },
    );
  }

  void _selectCard(int cardId) {
    // 获取对应的塔罗牌
    final tarotCard = availableCards[cardId % availableCards.length];

    print('🎴 选择卡牌: ${tarotCard.name}');
    print('🎯 当前位置: ${widget.currentPosition}');
    print('🔄 回调函数: ${widget.onCardSelected != null}');

    // 检查是否为单张选择模式（从牌阵页面或每日塔罗来的）
    if (widget.currentPosition != null && widget.onCardSelected != null) {
      print('✅ 进入单张选择模式');

      // 检查是否为每日塔罗（问题为"今日塔罗指引"）
      if (widget.question == '今日塔罗指引') {
        print('✅ 每日塔罗模式：直接返回');
        // 每日塔罗模式：直接返回，不需要确认页面
        Navigator.pop(context); // 关闭洗牌页面，返回到每日塔罗页面
        widget.onCardSelected!(tarotCard); // 调用回调
        return;
      }

      // 普通牌阵模式：跳转到确认页面
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      String positionName;
      if (currentSpread != null && widget.currentPosition! < currentSpread!.positionKeys.length) {
        final positionKey = currentSpread!.positionKeys[widget.currentPosition!];
        positionName = languageManager.translate(positionKey);
      } else {
        positionName = '${languageManager.translate('card_number_prefix')}${widget.currentPosition! + 1}${languageManager.translate('card_number_suffix')}';
      }

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CardConfirmationScreen(
            selectedCard: tarotCard,
            position: positionName,
            onConfirm: () {
              print('✅ 确认选择卡牌: ${tarotCard.name}');
              // 确认选择这张牌，返回到牌阵页面
              Navigator.pop(context); // 关闭确认页面
              Navigator.pop(context); // 关闭洗牌页面，返回到牌阵页面
              widget.onCardSelected!(tarotCard); // 调用回调
            },
            onReselect: () {
              print('🔄 重新选择卡牌');
              // 重新选择
              Navigator.pop(context);
            },
          ),
        ),
      );
      return;
    }

    // 原有的多张选择模式
    if (selectedCards.contains(tarotCard.id)) {
      // 如果已经选择了这张牌，则取消选择
      setState(() {
        selectedCards.remove(tarotCard.id);
        if (selectedCards.isEmpty) {
          selectedCardId = null;
          showCardSelection = false;
        }
      });
    } else if (selectedCards.length < requiredCardCount) {
      // 新流程：跳转到单张牌确认页面
      final currentPosition = selectedCards.length + 1;
      final languageManager = Provider.of<LanguageManager>(context, listen: false);
      String positionName;
      if (currentSpread != null && selectedCards.length < currentSpread!.positionKeys.length) {
        final positionKey = currentSpread!.positionKeys[selectedCards.length];
        positionName = languageManager.translate(positionKey);
      } else {
        positionName = '${languageManager.translate('card_number_prefix')}$currentPosition${languageManager.translate('card_number_suffix')}';
      }

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => CardConfirmationScreen(
            selectedCard: tarotCard,
            position: positionName,
            onConfirm: () {
              // 确认选择这张牌
              Navigator.pop(context);
              setState(() {
                selectedCards.add(tarotCard.id);
              });

              // 如果还需要更多牌，继续选择；否则进入牌阵布局页面
              if (selectedCards.length >= requiredCardCount) {
                _showSpreadLayout();
              }
            },
            onReselect: () {
              // 重新选择
              Navigator.pop(context);
            },
          ),
        ),
      );
    }
  }

  void _autoSelectCards() {
    // 自动选择所需数量的卡牌
    final random = math.Random();
    final autoSelectedCards = <String>[];

    for (int i = 0; i < requiredCardCount; i++) {
      String cardId;
      do {
        cardId = availableCards[random.nextInt(availableCards.length)].id;
      } while (autoSelectedCards.contains(cardId));
      autoSelectedCards.add(cardId);
    }

    // 检查是否为每日塔罗模式（有回调函数）
    if (widget.onCardSelected != null && autoSelectedCards.isNotEmpty) {
      // 每日塔罗模式：获取选中的卡牌并调用回调
      final selectedTarotCard = availableCards.firstWhere(
        (card) => card.id == autoSelectedCards.first,
      );

      // 返回到每日塔罗页面并显示反转的卡牌
      Navigator.pop(context);
      widget.onCardSelected!(selectedTarotCard);
    } else {
      // 普通解读模式：直接进入聊天解读页面
      // 获取完整的卡牌对象
      final selectedTarotCards = autoSelectedCards
          .map((cardId) => availableCards.firstWhere((card) => card.id == cardId))
          .toList();

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => ChatReadingScreen(
            question: widget.question,
            selectedCards: selectedTarotCards,
            spreadType: widget.spreadType,
          ),
        ),
      );
    }
  }

  void _showSpreadLayout() {
    // 获取选中的塔罗牌对象
    final selectedTarotCards = selectedCards
        .map((cardId) => availableCards.firstWhere((card) => card.id == cardId))
        .toList();

    Navigator.pushReplacement(
      context,
      MaterialPageRoute(
        builder: (context) => SpreadLayoutScreen(
          question: widget.question,
          spreadType: widget.spreadType,
          selectedCards: selectedTarotCards,
        ),
      ),
    );
  }

  void _confirmCardSelection() {
    // 🎯 导航到聊天解读页面
    // 获取完整的卡牌对象
    final selectedTarotCards = selectedCards
        .map((cardId) => availableCards.firstWhere((card) => card.id == cardId))
        .toList();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatReadingScreen(
          question: widget.question,
          selectedCards: selectedTarotCards,
          spreadType: widget.spreadType,
        ),
      ),
    );
  }

  void _changeCard() {
    // 重新选择
    setState(() {
      selectedCards.clear();
      selectedCardId = null;
      showCardSelection = false;
    });
  }

  Widget _buildSelectedCardsInfo() {
    return Column(
      mainAxisSize: MainAxisSize.min, // 🔧 防止溢出
      children: [
        // 📊 选择进度显示
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              '已选择卡牌',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: FigmaTheme.textPrimary,
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: FigmaTheme.primaryPink.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: FigmaTheme.primaryPink.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                '${selectedCards.length}/$requiredCardCount',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: FigmaTheme.primaryPink,
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 16),

        // 🎴 已选择的卡牌缩略图
        if (selectedCards.isNotEmpty)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: selectedCards.map((cardId) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              width: 40,
              height: 60,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: FigmaTheme.primaryPink.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Consumer<CardPreferenceService>(
                  builder: (context, cardService, child) {
                    return Image.asset(
                      'assets/images/${cardService.selectedCardBack}',
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Image.asset(
                          'assets/images/IMG_0992 42.png',
                          fit: BoxFit.cover,
                        );
                      },
                    );
                  },
                ),
              ),
            )).toList(),
          ),

        const SizedBox(height: 16),

        // 🔘 按钮区域
        if (selectedCards.length == requiredCardCount)
          // 已选择足够数量 - 显示确认按钮
          SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: _confirmCardSelection,
              style: ElevatedButton.styleFrom(
                backgroundColor: FigmaTheme.primaryPink,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
              child: const Text(
                '确认选择，开始解读',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                ),
              ),
            ),
          )
        else
          // 还需要选择更多卡牌 - 显示提示和重新选择按钮
          Column(
            children: [
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    requiredCardCount == 1
                        ? languageManager.translate('please_select_card')
                        : '还需要选择 ${requiredCardCount - selectedCards.length} 张牌',
                    style: const TextStyle(
                      fontSize: 14,
                      color: FigmaTheme.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2, // 允许最多2行
                    overflow: TextOverflow.ellipsis, // 超出时显示省略号
                    softWrap: true, // 启用自动换行
                  );
                },
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                height: 40,
                child: OutlinedButton(
                  onPressed: _changeCard,
                  style: OutlinedButton.styleFrom(
                    side: BorderSide(color: FigmaTheme.primaryPink.withValues(alpha: 0.5)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: Consumer<LanguageManager>(
                    builder: (context, languageManager, child) {
                      return Text(
                        languageManager.translate('reselect'),
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: FigmaTheme.primaryPink,
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
      ],
    );
  }
}

class CardData {
  final int id;
  double x;
  double y;
  double rotation;
  double scale;
  
  double targetX = 0;
  double targetY = 0;
  double targetRotation = 0;
  double targetScale = 1;

  CardData({
    required this.id,
    required this.x,
    required this.y,
    required this.rotation,
    required this.scale,
  });
}
