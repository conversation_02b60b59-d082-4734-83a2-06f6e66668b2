
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import '../services/subscription_service.dart';
import '../utils/language_manager.dart';

class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen> {
  SubscriptionPeriod _selectedPeriod = SubscriptionPeriod.weekly;
  SubscriptionTier _selectedTier = SubscriptionTier.basic;

  @override
  Widget build(BuildContext context) {
    print('🔍 SubscriptionScreen build() called');
    return Scaffold(
      backgroundColor: const Color(0xFF6B46C1),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFF8B5CF6),
              Color(0xFF6B46C1),
              Color(0xFF7C3AED),
              Color(0xFF5B21B6),
            ],
            stops: [0.0, 0.3, 0.7, 1.0],
          ),
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              // 顶部关闭按钮
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.close, color: Colors.white, size: 28),
                      onPressed: () {
                        print('🔍 Close button pressed');
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              ),

              // 主要内容
              Expanded(
                child: Consumer<SubscriptionService>(
                  builder: (context, subscriptionService, child) {
                    print('🔍 SubscriptionService Consumer builder called');
                    print('🔍 isSubscribed: ${subscriptionService.isSubscribed}');
                    if (subscriptionService.isSubscribed) {
                      print('🔍 Building subscribed view');
                      return _buildSubscribedView(subscriptionService);
                    }
                    print('🔍 Building subscription view');
                    return _buildSubscriptionView(context, subscriptionService);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubscribedView(SubscriptionService subscriptionService) {
    return const Padding(
      padding: EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.white,
            size: 80,
          ),
          SizedBox(height: 20),
          Text(
            'You are subscribed!',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 10),
          Text(
            'Enjoy unlimited AI tarot readings',
            style: TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionView(BuildContext context, SubscriptionService subscriptionService) {
    print('🔍 _buildSubscriptionView called');
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                const SizedBox(height: 20),

                // 标题区域
                _buildHeaderSection(),

                const SizedBox(height: 32),

                // 周期选择器
                _buildPeriodSelector(),

                const SizedBox(height: 32),

                // 会员卡片
                _buildMembershipCards(subscriptionService),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),

        // 底部购买按钮
        _buildBottomSection(subscriptionService),
      ],
    );
  }

  Widget _buildHeaderSection() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Column(
          children: [
            // 图标
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 48,
              ),
            ),

            const SizedBox(height: 16),

            // 主标题
            Text(
              languageManager.translate('unlock_ai_tarot_reading'),
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 8),

            // 副标题
            Text(
              languageManager.translate('get_professional_insights'),
              style: TextStyle(
                fontSize: 16,
                color: Colors.white.withOpacity(0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }

  Widget _buildPeriodSelector() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.2),
            borderRadius: BorderRadius.circular(25),
          ),
          padding: const EdgeInsets.all(4),
          child: Row(
            children: [
              _buildPeriodTab(languageManager.translate('weekly'), SubscriptionPeriod.weekly),
              _buildPeriodTab(languageManager.translate('monthly'), SubscriptionPeriod.monthly),
              _buildPeriodTab(languageManager.translate('annually'), SubscriptionPeriod.yearly),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPeriodTab(String title, SubscriptionPeriod period) {
    final isSelected = _selectedPeriod == period;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedPeriod = period;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isSelected ? Colors.white : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            title,
            textAlign: TextAlign.center,
            style: TextStyle(
              color: isSelected ? const Color(0xFF6B46C1) : Colors.white,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              fontSize: 16,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMembershipCards(SubscriptionService subscriptionService) {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        print('🔍 Building membership cards');
        final freeFeatures = languageManager.translate('free_features');
        final basicFeatures = languageManager.translate('basic_features');
        final proFeatures = languageManager.translate('pro_features');
        print('🔍 Free features: $freeFeatures');
        print('🔍 Basic features: $basicFeatures');
        print('🔍 Pro features: $proFeatures');

        // 如果翻译为空，使用硬编码的功能描述
        final actualFreeFeatures = freeFeatures.isNotEmpty ? freeFeatures : '每周1次AI解读\n每日抽卡\n有限牌阵';
        final actualBasicFeatures = basicFeatures.isNotEmpty ? basicFeatures : '每天1次AI解读\n所有塔罗牌阵\n自定义背景\n自定义卡牌背面';
        final actualProFeatures = proFeatures.isNotEmpty ? proFeatures : '每天5次AI解读\n所有塔罗牌阵\n自定义背景\n自定义卡牌背面\n高级显化功能\n优先客服支持';

        return Column(
          children: [
            // Free Member Card
            _buildMembershipCard(
              subscriptionService,
              SubscriptionTier.free,
              languageManager.translate('free_member'),
              actualFreeFeatures,
              languageManager.translate('free_price'),
              '',
              isSelected: false,
              isPopular: false,
              isFree: true,
            ),

            const SizedBox(height: 16),

            // Basic Member Card
            _buildMembershipCard(
              subscriptionService,
              SubscriptionTier.basic,
              languageManager.translate('basic_member'),
              actualBasicFeatures,
              subscriptionService.getPrice(SubscriptionTier.basic, _selectedPeriod),
              _calculateDailyPrice(SubscriptionTier.basic),
              isSelected: _selectedTier == SubscriptionTier.basic,
              isPopular: false,
            ),

            const SizedBox(height: 16),

            // Pro Member Card
            _buildMembershipCard(
              subscriptionService,
              SubscriptionTier.premium,
              languageManager.translate('pro_member'),
              actualProFeatures,
              subscriptionService.getPrice(SubscriptionTier.premium, _selectedPeriod),
              _calculateDailyPrice(SubscriptionTier.premium),
              isSelected: _selectedTier == SubscriptionTier.premium,
              isPopular: true,
            ),
          ],
        );
      },
    );
  }

  Widget _buildMembershipCard(
    SubscriptionService subscriptionService,
    SubscriptionTier tier,
    String title,
    String description,
    String price,
    String dailyPrice,
    {
    required bool isSelected,
    required bool isPopular,
    bool isFree = false,
  }) {
    return GestureDetector(
      onTap: isFree ? null : () {
        setState(() {
          _selectedTier = tier;
        });
      },
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? const Color(0xFF6B46C1) : Colors.transparent,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 选择圆圈 - 免费会员不显示
                if (!isFree)
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: isSelected ? const Color(0xFF6B46C1) : Colors.grey.shade300,
                        width: 2,
                      ),
                      color: isSelected ? const Color(0xFF6B46C1) : Colors.transparent,
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, color: Colors.white, size: 16)
                        : null,
                  ),

                if (!isFree) const SizedBox(width: 12),

                // 标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Colors.black,
                        ),
                      ),
                      if (isPopular)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: const Color(0xFF6B46C1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Consumer<LanguageManager>(
                              builder: (context, languageManager, child) {
                                return Text(
                                  languageManager.translate('most_popular'),
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 价格
                Text(
                  price,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isFree ? Colors.green : Colors.black,
                  ),
                ),

                if (!isFree)
                  Text(
                    ' /${_getPeriodText()}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
              ],
            ),

            const SizedBox(height: 12),

            // 功能列表
            _buildFeatureList(description),

            const SizedBox(height: 8),

            // 每日价格 - 仅付费会员显示
            if (!isFree && dailyPrice.isNotEmpty)
              Text(
                dailyPrice,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureList(String features) {
    final featureList = features.split('\n');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: featureList.map((feature) => Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Row(
          children: [
            const Icon(
              Icons.check_circle,
              color: Color(0xFF6B46C1),
              size: 16,
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                feature,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black87,
                ),
              ),
            ),
          ],
        ),
      )).toList(),
    );
  }

  String _getPeriodText() {
    switch (_selectedPeriod) {
      case SubscriptionPeriod.weekly:
        return 'week';
      case SubscriptionPeriod.monthly:
        return 'month';
      case SubscriptionPeriod.yearly:
        return 'year';
    }
  }

  String _calculateDailyPrice(SubscriptionTier tier) {
    // 简单的每日价格计算
    switch (tier) {
      case SubscriptionTier.basic:
        switch (_selectedPeriod) {
          case SubscriptionPeriod.weekly:
            return 'About \$0.43/day';
          case SubscriptionPeriod.monthly:
            return 'About \$0.33/day';
          case SubscriptionPeriod.yearly:
            return 'About \$0.25/day';
        }
      case SubscriptionTier.premium:
        switch (_selectedPeriod) {
          case SubscriptionPeriod.weekly:
            return 'About \$0.71/day';
          case SubscriptionPeriod.monthly:
            return 'About \$0.60/day';
          case SubscriptionPeriod.yearly:
            return 'About \$0.41/day';
        }
      case SubscriptionTier.free:
        return '';
    }
  }

  Widget _buildBottomSection(SubscriptionService subscriptionService) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 购买按钮
          Container(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () async {
                print('🔍 Purchase button pressed for tier: $_selectedTier, period: $_selectedPeriod');
                if (_selectedTier != SubscriptionTier.free) {
                  try {
                    // 显示加载状态
                    await subscriptionService.purchaseSubscription(_selectedTier, _selectedPeriod);
                    
                    // 检查购买是否成功
                    if (subscriptionService.isSubscribed) {
                      // 购买成功，关闭页面
                      Navigator.pop(context);
                    } else if (subscriptionService.errorMessage != null) {
                      // 显示错误信息
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(subscriptionService.errorMessage!),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  } catch (e) {
                    // 处理异常
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text('购买失败: $e'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                } else {
                  // 选择了免费版本，直接关闭
                  Navigator.pop(context);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: const Color(0xFF6B46C1),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    _selectedTier == SubscriptionTier.free
                        ? languageManager.translate('continue_with_free')
                        : languageManager.translate('start_free_trial'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 12),

          // 恢复购买按钮
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return TextButton(
                onPressed: () async {
                  await subscriptionService.restorePurchases();
                },
                child: Text(
                  languageManager.translate('restore_purchases'),
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 8),

          // 法律链接
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  TextButton(
                    onPressed: () => _launchURL('https://www.apple.com/legal/privacy/'),
                    child: Text(
                      languageManager.translate('privacy_policy'),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                  const Text(
                    ' • ',
                    style: TextStyle(color: Colors.white70, fontSize: 12),
                  ),
                  TextButton(
                    onPressed: () => _launchURL('https://www.apple.com/legal/internet-services/itunes/dev/stdeula/'),
                    child: Text(
                      languageManager.translate('terms_of_service'),
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 12,
                        decoration: TextDecoration.underline,
                      ),
                    ),
                  ),
                ],
              );
            },
          ),

          const SizedBox(height: 8),

          // 订阅说明
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                languageManager.translate('subscription_auto_renew_notice'),
                textAlign: TextAlign.center,
                style: const TextStyle(
                  color: Colors.white60,
                  fontSize: 11,
                  height: 1.3,
                ),
              );
            },
          ),

        ],
      ),
    );
  }

  // 🔗 启动URL
  void _launchURL(String url) async {
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        debugPrint('无法启动URL: $url');
      }
    } catch (e) {
      debugPrint('启动URL时出错: $e');
    }
  }
}
