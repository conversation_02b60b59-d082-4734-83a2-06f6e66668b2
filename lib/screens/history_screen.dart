import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/widgets/reading_history_item.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';

import 'package:intl/intl.dart';
import 'package:flutter_animate/flutter_animate.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  String _searchQuery = '';
  SpreadType? _filterSpreadType;
  bool _isRefreshing = false;







  // 下拉刷新
  Future<void> _onRefresh() async {
    if (_isRefreshing) return;
    
    setState(() {
      _isRefreshing = true;
    });
    
    try {
      final appState = Provider.of<AppStateProvider>(context, listen: false);
      await appState.refreshReadingHistory();
      
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('history_refreshed')),
              ],
            ),
            backgroundColor: const Color(0xFF4CAF50),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final languageManager = Provider.of<LanguageManager>(context, listen: false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error, color: Colors.white),
                const SizedBox(width: 8),
                Text(languageManager.translate('refresh_failed')),
              ],
            ),
            backgroundColor: const Color(0xFFE53E3E),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final appState = Provider.of<AppStateProvider>(context);
    final languageManager = Provider.of<LanguageManager>(context);
    final readings = appState.readingHistory;

    // Filter readings based on search query and spread type
    final filteredReadings = readings.where((reading) {
      final query = _searchQuery.toLowerCase();
      final matchesSearch = reading.question.toLowerCase().contains(query) ||
             reading.interpretation.toLowerCase().contains(query);

      final matchesSpread = _filterSpreadType == null ||
                           reading.spreadType == _filterSpreadType;

      return matchesSearch && matchesSpread;
    }).toList();

    // Sort by date (newest first)
    filteredReadings.sort((a, b) => b.date.compareTo(a.date));

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent, // 透明背景，使用HomeScreen的背景
      body: SafeArea(
        child: Column(
          children: [
            // 🍎 现代化顶部导航栏
            Container(
              height: 60,
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.15),
                borderRadius: BorderRadius.circular(23),
                border: Border.all(
                  color: Colors.white.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.white.withOpacity(0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  const SizedBox(width: 16),
                  Icon(
                    Icons.history,
                    color: Color(0xFF2A404E),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      languageManager.translate('reading_retrospective'),
                      style: const TextStyle(
                        fontFamily: 'Circular Std',
                        fontSize: 22,
                        fontWeight: FontWeight.w700,
                        color: Color(0xFF2A404E),
                        height: 1.36,
                      ),
                    ),
                  ),
                  if (_isRefreshing)
                    Container(
                      margin: const EdgeInsets.only(right: 16),
                      width: 24,
                      height: 24,
                      child: const CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2A404E)),
                      ),
                    )
                  else
                    IconButton(
                      onPressed: _onRefresh,
                      icon: const Icon(Icons.refresh, color: Color(0xFF2A404E)),
                      tooltip: languageManager.translate('refresh'),
                    ),


                  IconButton(
                    onPressed: () => _showFilterDialog(),
                    icon: const Icon(Icons.tune, color: Color(0xFF2A404E)),
                    tooltip: languageManager.translate('filter'),
                  ),
                ],
              ),
            ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.5, end: 0),
            
            // 主要内容区域 - 可滚动
            Expanded(
              child: RefreshIndicator(
                onRefresh: _onRefresh,
                backgroundColor: Colors.white,
                color: const Color(0xFF667eea),
                child: SingleChildScrollView(
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.fromLTRB(20, 0, 20, 10), // 进一步减少底部内边距
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [


                      // 统计信息卡片
                      _buildStatsCards(readings, languageManager),

                      const SizedBox(height: 24),

                      // 搜索栏
                      _buildSearchBar(languageManager),
                      
                      const SizedBox(height: 24),
                      
                      // 历史记录列表
                      if (filteredReadings.isEmpty)
                        _buildEmptyState(languageManager)
                      else
                        _buildReadingsList(filteredReadings),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 🎯 精美的空状态页面 - 倒数第三层毛玻璃样式
  Widget _buildEmptyState(LanguageManager languageManager) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(23),
        border: Border.all(
          color: Colors.white.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 移除书本图标
            
            const SizedBox(height: 24),
            
            Text(
              _searchQuery.isNotEmpty || _filterSpreadType != null
                  ? languageManager.translate('no_matching_records')
                  : languageManager.translate('start_first_reading'),
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: Colors.black,
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 400.ms, duration: 600.ms),

            const SizedBox(height: 12),

            Text(
              _searchQuery.isNotEmpty || _filterSpreadType != null
                  ? languageManager.translate('adjust_search_filter')
                  : languageManager.translate('reading_history_display'),
              style: TextStyle(
                fontSize: 16,
                color: Colors.black.withOpacity(0.8),
              ),
              textAlign: TextAlign.center,
            ).animate().fadeIn(delay: 600.ms, duration: 600.ms),
            
            const SizedBox(height: 32),
            
            if (_searchQuery.isNotEmpty || _filterSpreadType != null)
              Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _searchQuery = '';
                      _filterSpreadType = null;
                    });
                  },
                  icon: const Icon(Icons.clear_all, color: Colors.black),
                  label: Text(
                    languageManager.translate('clear_filter'),
                    style: const TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
                  ),
                ),
              ).animate().fadeIn(delay: 800.ms, duration: 600.ms),
            
            const SizedBox(height: 16),
            
            Container(
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: TextButton.icon(
                onPressed: _onRefresh,
                icon: const Icon(Icons.refresh, color: Colors.black),
                label: Text(
                  languageManager.translate('refresh_records'),
                  style: const TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
                ),
              ),
            ).animate().fadeIn(delay: 1000.ms, duration: 600.ms),
          ],
        ),
      ),
    );
  }

  // 📊 统计信息卡片 - 倒数第三层毛玻璃样式
  Widget _buildStatsCards(List readings, LanguageManager languageManager) {
    return Row(
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              // 倒数第三层样式：更透明的毛玻璃效果
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(23),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('total_readings_count'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${readings.length}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              // 倒数第三层样式：更透明的毛玻璃效果
              color: Colors.white.withOpacity(0.15),
              borderRadius: BorderRadius.circular(23),
              border: Border.all(
                color: Colors.white.withOpacity(0.3),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  languageManager.translate('monthly_count'),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black.withOpacity(0.8),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${_getMonthlyCount(readings)}',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: Colors.black,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  // 🔍 搜索栏 - 倒数第三层毛玻璃样式
  Widget _buildSearchBar(LanguageManager languageManager) {
    return Container(
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.white.withOpacity(0.15),
        borderRadius: BorderRadius.circular(28), // 增加圆角弧度，更接近半圆
        border: Border.all(
          color: Colors.white.withOpacity(0.4), // 提高边框透明度，更明显
          width: 2.0, // 增加边框宽度
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.white.withOpacity(0.15),
            blurRadius: 25,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(28), // 确保内容遵循圆角
        child: TextField(
          onChanged: (value) => setState(() => _searchQuery = value),
          decoration: InputDecoration(
            hintText: languageManager.translate('search_reading_records'),
            hintStyle: TextStyle(
              color: Colors.black.withOpacity(0.6),
              fontSize: 16,
            ),
            prefixIcon: Container(
              padding: const EdgeInsets.all(12),
              child: Icon(
                Icons.search,
                color: Colors.black.withOpacity(0.7),
                size: 22,
              ),
            ),
            suffixIcon: _searchQuery.isNotEmpty
                ? Container(
                    padding: const EdgeInsets.all(12),
                    child: GestureDetector(
                      onTap: () => setState(() => _searchQuery = ''),
                      child: Icon(
                        Icons.clear,
                        color: Colors.black.withOpacity(0.5),
                        size: 20,
                      ),
                    ),
                  )
                : null,
            border: InputBorder.none, // 移除默认边框
            enabledBorder: InputBorder.none,
            focusedBorder: InputBorder.none,
            contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            filled: false, // 不使用填充色，使用外层容器的背景
          ),
          style: const TextStyle(
            color: Colors.black,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  // 📜 历史记录列表 - 倒数第三层毛玻璃样式
  Widget _buildReadingsList(List filteredReadings) {
    return Column(
      children: filteredReadings.map((reading) {
        return Container(
          margin: const EdgeInsets.only(bottom: 16),
          decoration: BoxDecoration(
            // 倒数第三层样式：更透明的毛玻璃效果
            color: Colors.white.withOpacity(0.15),
            borderRadius: BorderRadius.circular(23),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: ReadingHistoryItem(
            reading: reading,
          ),
        );
      }).toList(),
    );
  }

  void _showReadingDetails(dynamic reading) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                reading.question,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2D3748),
                ),
              ),
              const SizedBox(height: 16),
              Text(
                DateFormat('yyyy-MM-dd HH:mm').format(reading.date),
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF666666),
                ),
              ),
              const SizedBox(height: 16),
              Consumer<LanguageManager>(
                builder: (context, languageManager, child) {
                  return Text(
                    languageManager.translate('interpretation_content'),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF4A5568),
                    ),
                  );
                },
              ),
              const SizedBox(height: 8),
              Text(
                reading.interpretation,
                style: const TextStyle(
                  fontSize: 14,
                  color: Color(0xFF2D3748),
                  height: 1.5,
                ),
              ),
              const SizedBox(height: 24),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('关闭'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int _getMonthlyCount(List readings) {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    int count = 0;

    for (var reading in readings) {
      if (reading.date.year == currentMonth.year &&
          reading.date.month == currentMonth.month) {
        count++;
      }
    }

    return count;
  }

  String _getAverageRating(List readings) {
    if (readings.isEmpty) return '0.0';

    double totalRating = 0;
    int ratedCount = 0;

    for (var reading in readings) {
      if (reading.accuracy != null && reading.usefulness != null && reading.satisfaction != null) {
        totalRating += (reading.accuracy! + reading.usefulness! + reading.satisfaction!) / 3;
        ratedCount++;
      }
    }

    if (ratedCount == 0) return '0.0';
    return (totalRating / ratedCount).toStringAsFixed(1);
  }

  String _getSpreadTypeName(SpreadType spreadType) {
    switch (spreadType) {
      case SpreadType.single:
        return '单张牌';
      case SpreadType.three:
        return '三张牌';
      case SpreadType.celtic:
        return '凯尔特十字';
      default:
        return '未知';
    }
  }

  bool _hasRating(dynamic reading) {
    return reading.accuracy != null || reading.usefulness != null || reading.satisfaction != null;
  }

  String _getReadingRating(dynamic reading) {
    if (!_hasRating(reading)) return '0.0';
    
    double total = 0;
    int count = 0;
    
    if (reading.accuracy != null) {
      total += reading.accuracy!;
      count++;
    }
    if (reading.usefulness != null) {
      total += reading.usefulness!;
      count++;
    }
    if (reading.satisfaction != null) {
      total += reading.satisfaction!;
      count++;
    }
    
    if (count == 0) return '0.0';
    return (total / count).toStringAsFixed(1);
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          margin: const EdgeInsets.all(20),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
          mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
          children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: const Color(0xFF667eea).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.tune,
                      color: Color(0xFF667eea),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    '筛选条件',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF2D3748),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                '牌阵类型',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF4A5568),
                ),
              ),
              const SizedBox(height: 12),
              ...SpreadType.values.map((type) {
                if (type == SpreadType.none) return const SizedBox.shrink();
                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () {
                  setState(() {
                          _filterSpreadType = _filterSpreadType == type ? null : type;
                  });
                  Navigator.pop(context);
                },
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: _filterSpreadType == type
                              ? const Color(0xFF667eea).withOpacity(0.1)
                              : Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: _filterSpreadType == type
                                ? const Color(0xFF667eea)
                                : Colors.grey[300]!,
                            width: 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              _filterSpreadType == type
                                  ? Icons.radio_button_checked
                                  : Icons.radio_button_unchecked,
                              color: _filterSpreadType == type
                                  ? const Color(0xFF667eea)
                                  : Colors.grey[500],
                              size: 20,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              _getSpreadTypeName(type),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: _filterSpreadType == type
                                    ? FontWeight.w600
                                    : FontWeight.normal,
                                color: _filterSpreadType == type
                                    ? const Color(0xFF667eea)
                                    : const Color(0xFF4A5568),
              ),
            ),
          ],
        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
            onPressed: () {
              setState(() {
                _filterSpreadType = null;
              });
              Navigator.pop(context);
            },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        '清除筛选',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF6B7280),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
            onPressed: () => Navigator.pop(context),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF667eea),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '确定',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
