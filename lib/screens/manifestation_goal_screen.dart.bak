import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/services/blur_settings_service.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:ui'; // 添加液态玻璃效果支持

class ManifestationGoalScreen extends StatefulWidget {
  final Function(ManifestationGoal) onGoalSelected;
  final ManifestationGoal? currentGoal;

  const ManifestationGoalScreen({
    super.key,
    required this.onGoalSelected,
    this.currentGoal,
  });

  @override
  State<ManifestationGoalScreen> createState() => _ManifestationGoalScreenState();
}

class _ManifestationGoalScreenState extends State<ManifestationGoalScreen> {
  ManifestationGoal? _selectedGoal;
  ManifestationVersion? _selectedVersion;

  @override
  void initState() {
    super.initState();
    _selectedGoal = widget.currentGoal;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BlurSettingsService>(
      builder: (context, blurSettings, child) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              // 🌈 梦幻渐变背景 (塔罗阅读页面配色)
              Positioned.fill(
                child: Container(
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Color(0xFFE8F4FD), // 浅蓝色
                        Color(0xFFF8E8FF), // 浅紫色  
                        Color(0xFFFFE8F8), // 浅粉色
                      ],
                      stops: [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),

              // ✨ 动态装饰元素
              Positioned.fill(
                child: Stack(
                  children: [
                    // 浮动爱心效果
                    Positioned(
                      top: 80,
                      right: 30,
                      child: Container(
                        width: 12,
                        height: 12,
                        child: const Icon(
                          Icons.favorite,
                          color: Colors.white,
                          size: 12,
                        ),
                      ).animate(
                        onPlay: (controller) => controller.repeat(reverse: true),
                      ).fade(duration: 3000.ms).moveY(begin: 0, end: -20),
                    ),
                    Positioned(
                      top: 150,
                      left: 40,
                      child: Container(
                        width: 8,
                        height: 8,
                        child: const Icon(
                          Icons.star,
                          color: Colors.white,
                          size: 8,
                        ),
                      ).animate(
                        onPlay: (controller) => controller.repeat(reverse: true),
                      ).fade(duration: 2500.ms, delay: 1000.ms),
                    ),
                    // 更多装饰元素
                    Positioned(
                      top: 250,
                      right: 80,
                      child: Container(
                        width: 6,
                        height: 6,
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                      ).animate(
                        onPlay: (controller) => controller.repeat(reverse: true),
                      ).scale(
                        begin: const Offset(0.5, 0.5),
                        end: const Offset(1.5, 1.5),
                        duration: 2000.ms,
                      ),
                    ),
                  ],
                ),
              ),

              SafeArea(
                child: Column(
                  children: [
                    // 🍎 现代化顶部导航栏
                    ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: BackdropFilter(
                        filter: blurSettings.getImageFilter(),
                        child: Container(
                          height: 60,
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.15), // 更透明的毛玻璃效果
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(
                              color: Colors.white.withOpacity(0.4),
                              width: 1.5,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.white.withOpacity(0.2),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 40,
                                offset: const Offset(0, 12),
                              ),
                            ],
                          ),
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => Navigator.pop(context),
                                icon: const Icon(
                                  Icons.arrow_back_ios,
                                  color: Colors.black,
                                  size: 20,
                                ),
                              ),
                              const Expanded(
                                child: Text(
                                  '设定显化目标',
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(width: 48), // 平衡布局
                            ],
                          ),
                        ),
                      ),
                    ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.5, end: 0),

                    // 内容区域
                    Expanded(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            const SizedBox(height: 40),

                            // 🎨 精美目标选择卡片
                            ...ManifestationGoal.values.asMap().entries.map((entry) {
                              final index = entry.key;
                              final goal = entry.value;
                              final isSelected = _selectedGoal == goal;
                              
                              return Container(
                                margin: const EdgeInsets.only(bottom: 16),
                                child: GestureDetector(
                                  onTap: () {
                                    if (goal == ManifestationGoal.wealth) {
                                      _showVersionSelectionDialog(goal);
                                    } else {
                                      setState(() {
                                        _selectedGoal = goal;
                                      });
                                    }
                                  },
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(20),
                                    child: BackdropFilter(
                                      filter: blurSettings.getImageFilter(),
                                      child: Container(
                                        width: double.infinity,
                                        decoration: BoxDecoration(
                                          color: isSelected 
                                                ? Colors.white.withOpacity(0.15) // 更透明的毛玻璃效果
                                                : Colors.white.withOpacity(0.08),
                                          borderRadius: BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected 
                                                  ? Colors.white.withOpacity(0.6)
                                                  : Colors.white.withOpacity(0.3),
                                            width: isSelected ? 2 : 1,
                                          ),
                                          boxShadow: isSelected ? [
                                            BoxShadow(
                                              color: Colors.white.withOpacity(0.3),
                                              blurRadius: 20,
                                              offset: const Offset(0, 8),
                                            ),
                                          ] : [],
                                        ),
                                        child: Stack(
                                          children: [
                                            // 背景渐变效果
                                            if (isSelected)
                                              Positioned.fill(
                                                child: Container(
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(18),
                                                    gradient: LinearGradient(
                                                      begin: Alignment.topLeft,
                                                      end: Alignment.bottomRight,
                                                      colors: [
                                                        Colors.white.withOpacity(0.1),
                                                        Colors.white.withOpacity(0.05),
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            
                                            // 内容
                                            Padding(
                                              padding: const EdgeInsets.all(20),
                                              child: Row(
                                                children: [
                                                  // 精美图标容器
                                                  Container(
                                                    width: 70,
                                                    height: 70,
                                                    decoration: BoxDecoration(
                                                      color: isSelected 
                                                          ? Colors.white.withOpacity(0.3)
                                                          : Colors.white.withOpacity(0.15),
                                                      borderRadius: BorderRadius.circular(18),
                                                      border: isSelected ? Border.all(
                                                        color: Colors.white.withOpacity(0.4),
                                                        width: 1,
                                                      ) : null,
                                                    ),
                                                    child: Center(
                                                      child: Text(
                                                        goal.emoji,
                                                        style: const TextStyle(fontSize: 32),
                                                      ),
                                                    ),
                                                  ),
                                                  
                                                  const SizedBox(width: 16),
                                                  
                                                  // 内容文字
                                                  Expanded(
                                                    child: Column(
                                                      crossAxisAlignment: CrossAxisAlignment.start,
                                                      children: [
                                                        Text(
                                                          goal.displayName,
                                                          style: const TextStyle(
                                                            fontSize: 20,
                                                            fontWeight: FontWeight.w700,
                                                            color: Colors.black,
                                                          ),
                                                        ),
                                                        const SizedBox(height: 6),
                                                        Text(
                                                          _getGoalDescription(goal),
                                                          style: const TextStyle(
                                                            fontSize: 14,
                                                            color: Colors.black87,
                                                            height: 1.3,
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  
                                                  // 选择状态指示器
                                                  Container(
                                                    width: 24,
                                                    height: 24,
                                                    decoration: BoxDecoration(
                                                      color: isSelected 
                                                          ? Colors.white
                                                          : Colors.white.withOpacity(0.3),
                                                      shape: BoxShape.circle,
                                                      border: Border.all(
                                                        color: Colors.white.withOpacity(0.5),
                                                        width: 1,
                                                      ),
                                                    ),
                                                    child: isSelected
                                                        ? const Icon(
                                                            Icons.check,
                                                            size: 16,
                                                            color: Color(0xFF667eea),
                                                          )
                                                        : null,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ).animate().fadeIn(
                                delay: Duration(milliseconds: 100 + index * 100),
                                duration: 600.ms,
                              ).slideX(begin: 0.3, end: 0),
                            }).toList(),

                            const SizedBox(height: 32),

                            // 🚀 精美确认按钮
                            if (_selectedGoal != null)
                                Container(
                                width: double.infinity,
                                height: 56,
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      begin: Alignment.topCenter,
                                      end: Alignment.bottomCenter,
                                      colors: [
                                        Color(0xFF8B5CF6), // 塔罗紫色
                                        Color(0xFF6366F1), // 深紫色
                                      ],
                                    ),
                                    borderRadius: BorderRadius.circular(16),
                                    boxShadow: [
                                      BoxShadow(
                                        color: const Color(0xFF8B5CF6).withOpacity(0.4),
                                        blurRadius: 20,
                                        offset: const Offset(0, 8),
                                      ),
                                    ],
                                  ),
                                  child: Material(
                                    color: Colors.transparent,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(16),
                                      onTap: () {
                                        if (_selectedGoal == ManifestationGoal.wealth && _selectedVersion == null) {
                                          // 如果选择了财富但还没选择版本，显示版本选择对话框
                                          _showVersionSelectionDialog(_selectedGoal!);
                                        } else {
                                          widget.onGoalSelected(_selectedGoal!);
                                          Navigator.pop(context);
                                        }
                                      },
                                      child: const Center(
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.rocket_launch,
                                              color: Colors.white,
                                              size: 20,
                                            ),
                                            SizedBox(width: 8),
                                            Text(
                                              '开始显化之旅',
                                              style: TextStyle(
                                                fontSize: 18,
                                                fontWeight: FontWeight.w600,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ).animate().fadeIn(delay: 800.ms, duration: 600.ms).slideY(begin: 0.3, end: 0),

                            const SizedBox(height: 40),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 版本选择对话框
  void _showVersionSelectionDialog(ManifestationGoal goal) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Text(goal.emoji, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            const Text(
              '选择财富显化版本',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '请选择您偏好的财富显化风格',
              style: TextStyle(fontSize: 14, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            // 西方版选项
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(bottom: 12),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedGoal = goal;
                      _selectedVersion = ManifestationVersion.western;
                    });
                    // 选择版本后立即显示训练确认对话框
                    Future.delayed(const Duration(milliseconds: 300), () {
                      _showManifestationTrainingDialog();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.green.shade100,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Icon(Icons.monetization_on, color: Colors.green),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '西方版',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '金币、钻石等西式财富符号',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // 东方版选项
            Container(
              width: double.infinity,
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(12),
                  onTap: () {
                    Navigator.of(context).pop();
                    setState(() {
                      _selectedGoal = goal;
                      _selectedVersion = ManifestationVersion.eastern;
                    });
                    // 选择版本后立即显示训练确认对话框
                    Future.delayed(const Duration(milliseconds: 300), () {
                      _showManifestationTrainingDialog();
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: Colors.amber.shade100,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Text('🏮', style: TextStyle(fontSize: 20)),
                        ),
                        const SizedBox(width: 12),
                        const Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '东方版',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                              Text(
                                '金元宝、金砖等中式财富符号',
                                style: TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showManifestationTrainingDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Text(_selectedGoal!.emoji, style: const TextStyle(fontSize: 24)),
            const SizedBox(width: 8),
            const Text(
              '显化目标已设定',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '你已选择显化：${_selectedGoal!.displayName}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            const Text(
              '是否要立即进入显化训练？\n通过肯定语练习来加强显化效果',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              // 只设定目标，不进入训练
              widget.onGoalSelected(_selectedGoal!);
              Navigator.pop(context); // 关闭对话框
              Navigator.pop(context); // 返回上一页
            },
            child: const Text('稍后'),
          ),
          ElevatedButton(
            onPressed: () {
              // 设定目标并进入训练
              widget.onGoalSelected(_selectedGoal!);
              Navigator.pop(context); // 关闭对话框
              Navigator.pop(context); // 返回上一页

              // 进入肯定语显化页面
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => ManifestationAnimationScreen(
                    goal: _selectedGoal!,
                    affirmation: '我相信宇宙会为我带来最好的安排，我值得拥有美好的生活。',
                    version: _selectedVersion,
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF8B5CF6),
              foregroundColor: Colors.white,
            ),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  String _getGoalDescription(ManifestationGoal goal) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '吸引财富和丰盛，改善财务状况';
      case ManifestationGoal.career:
        return '提升事业运势，获得职场成功';
      case ManifestationGoal.beauty:
        return '增强个人魅力，内外兼修';
      case ManifestationGoal.fame:
        return '提升知名度，获得认可和声誉';
      case ManifestationGoal.love:
        return '吸引真爱，改善感情关系';
    }
  }
}
      },
    );
  }
