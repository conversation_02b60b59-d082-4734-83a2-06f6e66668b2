import 'dart:io';
import 'package:flutter/material.dart';

/// 网络连接测试工具
/// 用于诊断Apple登录和其他服务的网络连接问题
class NetworkTestUtils {
  
  /// 测试Apple ID服务连接
  static Future<Map<String, dynamic>> testAppleServices() async {
    final results = <String, dynamic>{};
    
    try {
      // 测试Apple ID主服务
      final appleIdResult = await InternetAddress.lookup('appleid.apple.com');
      results['appleid.apple.com'] = {
        'status': 'success',
        'addresses': appleIdResult.map((addr) => addr.address).toList(),
        'message': 'Apple ID服务连接正常'
      };
    } catch (e) {
      results['appleid.apple.com'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Apple ID服务连接失败'
      };
    }
    
    try {
      // 测试Apple身份管理服务
      final idmsaResult = await InternetAddress.lookup('idmsa.apple.com');
      results['idmsa.apple.com'] = {
        'status': 'success',
        'addresses': idmsaResult.map((addr) => addr.address).toList(),
        'message': 'Apple身份管理服务连接正常'
      };
    } catch (e) {
      results['idmsa.apple.com'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Apple身份管理服务连接失败'
      };
    }
    
    try {
      // 测试Supabase服务
      final supabaseResult = await InternetAddress.lookup('ktqlxbcauxomczubqasp.supabase.co');
      results['supabase.co'] = {
        'status': 'success',
        'addresses': supabaseResult.map((addr) => addr.address).toList(),
        'message': 'Supabase服务连接正常'
      };
    } catch (e) {
      results['supabase.co'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'Supabase服务连接失败'
      };
    }
    
    return results;
  }
  
  /// 测试HTTP连接
  static Future<Map<String, dynamic>> testHttpConnections() async {
    final results = <String, dynamic>{};
    
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse('https://appleid.apple.com'));
      final response = await request.close();
      results['https_appleid'] = {
        'status': 'success',
        'statusCode': response.statusCode,
        'message': 'HTTPS连接Apple ID服务正常'
      };
      client.close();
    } catch (e) {
      results['https_appleid'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'HTTPS连接Apple ID服务失败'
      };
    }
    
    try {
      final client = HttpClient();
      final request = await client.getUrl(Uri.parse('https://ktqlxbcauxomczubqasp.supabase.co'));
      final response = await request.close();
      results['https_supabase'] = {
        'status': 'success',
        'statusCode': response.statusCode,
        'message': 'HTTPS连接Supabase服务正常'
      };
      client.close();
    } catch (e) {
      results['https_supabase'] = {
        'status': 'error',
        'error': e.toString(),
        'message': 'HTTPS连接Supabase服务失败'
      };
    }
    
    return results;
  }
  
  /// 显示网络测试结果对话框
  static void showNetworkTestResults(BuildContext context, Map<String, dynamic> results) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Row(
            children: [
              Icon(Icons.wifi, color: Colors.blue),
              SizedBox(width: 8),
              Text('网络连接测试结果'),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: results.entries.map((entry) {
                final data = entry.value as Map<String, dynamic>;
                final isSuccess = data['status'] == 'success';
                
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 4),
                  child: Row(
                    children: [
                      Icon(
                        isSuccess ? Icons.check_circle : Icons.error,
                        color: isSuccess ? Colors.green : Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              entry.key,
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            Text(
                              data['message'] ?? '未知状态',
                              style: TextStyle(
                                color: isSuccess ? Colors.green : Colors.red,
                                fontSize: 12,
                              ),
                            ),
                            if (data['error'] != null)
                              Text(
                                '错误: ${data['error']}',
                                style: const TextStyle(
                                  color: Colors.red,
                                  fontSize: 10,
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('关闭'),
            ),
          ],
        );
      },
    );
  }
  
  /// 获取网络状态建议
  static String getNetworkAdvice(Map<String, dynamic> results) {
    final failedServices = results.entries
        .where((entry) => entry.value['status'] == 'error')
        .map((entry) => entry.key)
        .toList();
    
    if (failedServices.isEmpty) {
      return '✅ 所有网络服务连接正常，Apple登录应该可以正常工作。';
    }
    
    if (failedServices.contains('appleid.apple.com') || failedServices.contains('idmsa.apple.com')) {
      return '❌ Apple服务连接失败，这会导致Apple登录无法工作。请检查网络连接或稍后重试。';
    }
    
    if (failedServices.contains('supabase.co')) {
      return '⚠️ Supabase服务连接失败，Apple登录可能成功但无法与后端同步。请检查网络连接。';
    }
    
    return '⚠️ 部分网络服务连接异常，可能影响Apple登录功能。';
  }
} 