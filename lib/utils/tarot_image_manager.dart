import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/card_preference_service.dart';

/// 塔罗牌图片资源管理器
/// 统一管理所有塔罗牌相关的图片资源，便于维护和优化
class TarotImageManager {
  // 基础路径
  static const String _basePath = 'assets/images';
  static const String _defaultCardBackPath = '$_basePath/IMG_0992 42.png';
  
  // 大阿卡纳图片映射
  static const Map<String, String> _majorArcanaImages = {
    'The Fool': '$_basePath/thefool.jpeg',
    'The Magician': '$_basePath/themagician.jpeg',
    'The High Priestess': '$_basePath/thehighpriestess.jpeg',
    'The Empress': '$_basePath/theempress.jpeg',
    'The Emperor': '$_basePath/theemperor.jpeg',
    'The Hierophant': '$_basePath/thehierophant.jpeg',
    'The Lovers': '$_basePath/TheLovers.jpg',
    'The Chariot': '$_basePath/thechariot.jpeg',
    'Strength': '$_basePath/thestrength.jpeg',
    'The Hermit': '$_basePath/thehermit.jpeg',
    'Wheel of Fortune': '$_basePath/wheeloffortune.jpeg',
    'Justice': '$_basePath/justice.jpeg',
    'The Hanged Man': '$_basePath/thehangedman.jpeg',
    'Death': '$_basePath/death.jpeg',
    'Temperance': '$_basePath/temperance.jpeg',
    'The Devil': '$_basePath/thedevil.jpeg',
    'The Tower': '$_basePath/thetower.jpeg',
    'The Star': '$_basePath/thestar.jpeg',
    'The Moon': '$_basePath/themoon.jpeg',
    'The Sun': '$_basePath/thesun.jpeg',
    'Judgement': '$_basePath/judgement.jpeg',
    'The World': '$_basePath/theworld.jpeg',
  };
  
  // 小阿卡纳图片映射
  static const Map<String, String> _minorArcanaImages = {
    // 圣杯组
    'Ace of Cups': '$_basePath/aceofcups.jpeg',
    'Two of Cups': '$_basePath/twoofcups.jpeg',
    'Three of Cups': '$_basePath/threeofcups.jpeg',
    'Four of Cups': '$_basePath/fourofcups.jpeg',
    'Five of Cups': '$_basePath/fiveofcups.jpeg',
    'Six of Cups': '$_basePath/sixofcups.jpeg',
    'Seven of Cups': '$_basePath/sevenofcups.jpeg',
    'Eight of Cups': '$_basePath/eightofcups.jpeg',
    'Nine of Cups': '$_basePath/nineofcups.jpeg',
    'Ten of Cups': '$_basePath/tenofcups.jpeg',
    'Page of Cups': '$_basePath/pageofcups.jpeg',
    'Knight of Cups': '$_basePath/knightofcups.jpeg',
    'Queen of Cups': '$_basePath/queenofcups.jpeg',
    'King of Cups': '$_basePath/kingofcups.jpeg',
    
    // 权杖组
    'Ace of Wands': '$_basePath/aceofwands.jpeg',
    'Two of Wands': '$_basePath/twoofwands.jpeg',
    'Three of Wands': '$_basePath/threeofwands.jpeg',
    'Four of Wands': '$_basePath/fourofwands.jpeg',
    'Five of Wands': '$_basePath/fiveofwands.jpeg',
    'Six of Wands': '$_basePath/sixofwands.jpeg',
    'Seven of Wands': '$_basePath/sevenofwands.jpeg',
    'Eight of Wands': '$_basePath/eightofwands.jpeg',
    'Nine of Wands': '$_basePath/nineofwands.jpeg',
    'Ten of Wands': '$_basePath/tenofwands.jpeg',
    'Page of Wands': '$_basePath/pageofwands.jpeg',
    'Knight of Wands': '$_basePath/knightofwands.jpeg',
    'Queen of Wands': '$_basePath/queenofwands.jpeg',
    'King of Wands': '$_basePath/kingofwands.jpeg',
    
    // 宝剑组
    'Ace of Swords': '$_basePath/aceofswords.jpeg',
    'Two of Swords': '$_basePath/twoofswords.jpeg',
    'Three of Swords': '$_basePath/threeofswords.jpeg',
    'Four of Swords': '$_basePath/fourofswords.jpeg',
    'Five of Swords': '$_basePath/fiveofswords.jpeg',
    'Six of Swords': '$_basePath/sixofswords.jpeg',
    'Seven of Swords': '$_basePath/sevenofswords.jpeg',
    'Eight of Swords': '$_basePath/eightofswords.jpeg',
    'Nine of Swords': '$_basePath/nineofswords.jpeg',
    'Ten of Swords': '$_basePath/tenofswords.jpeg',
    'Page of Swords': '$_basePath/pageofswords.jpeg',
    'Knight of Swords': '$_basePath/knightofswords.jpeg',
    'Queen of Swords': '$_basePath/queenofswords.jpeg',
    'King of Swords': '$_basePath/kingofswords.jpeg',
    
    // 金币组
    'Ace of Pentacles': '$_basePath/aceofpentacles.jpeg',
    'Two of Pentacles': '$_basePath/twoofpentacles.jpeg',
    'Three of Pentacles': '$_basePath/threeofpentacles.jpeg',
    'Four of Pentacles': '$_basePath/fourofpentacles.jpeg',
    'Five of Pentacles': '$_basePath/fiveofpentacles.jpeg',
    'Six of Pentacles': '$_basePath/sixofpentacles.jpeg',
    'Seven of Pentacles': '$_basePath/sevenofpentacles.jpeg',
    'Eight of Pentacles': '$_basePath/eightofpentacles.jpeg',
    'Nine of Pentacles': '$_basePath/nineofpentacles.jpeg',
    'Ten of Pentacles': '$_basePath/tenofpentacles.jpeg',
    'Page of Pentacles': '$_basePath/pageofpentacles.jpeg',
    'Knight of Pentacles': '$_basePath/knightofpentacles.jpeg',
    'Queen of Pentacles': '$_basePath/queenofpentacles.jpeg',
    'King of Pentacles': '$_basePath/kingofpentacles.jpeg',
  };
  
  /// 获取塔罗牌图片路径
  static String getCardImage(String cardName) {
    // 先在大阿卡纳中查找
    if (_majorArcanaImages.containsKey(cardName)) {
      return _majorArcanaImages[cardName]!;
    }
    
    // 再在小阿卡纳中查找
    if (_minorArcanaImages.containsKey(cardName)) {
      return _minorArcanaImages[cardName]!;
    }
    
    // 如果找不到，返回默认的牌背图片
    return _defaultCardBackPath;
  }
  
  /// 获取牌背图片路径
  static String getCardBackImage() {
    return _defaultCardBackPath;
  }
  
  /// 获取用户选择的牌背图片路径（需要context）
  static String getUserSelectedCardBack(BuildContext context) {
    try {
      final cardService = Provider.of<CardPreferenceService>(context, listen: false);
      return '$_basePath/${cardService.selectedCardBack}';
    } catch (e) {
      debugPrint('获取用户选择的卡牌背面失败: $e');
      return _defaultCardBackPath;
    }
  }
  
  /// 构建用户选择的卡牌背面Widget
  static Widget buildCardBackImage({
    required BuildContext context,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? errorWidget,
  }) {
    return Consumer<CardPreferenceService>(
      builder: (context, cardService, child) {
        return Image.asset(
          '$_basePath/${cardService.selectedCardBack}',
          width: width,
          height: height,
          fit: fit,
          errorBuilder: (context, error, stackTrace) {
            debugPrint('加载用户选择的卡牌背面失败: ${cardService.selectedCardBack}');
            return Image.asset(
              _defaultCardBackPath,
              width: width,
              height: height,
              fit: fit,
            );
          },
        );
      },
    );
  }
  
  /// 获取UI装饰图片
  static String getUIImage(String imageName) {
    return '$_basePath/$imageName';
  }
  
  /// 预加载塔罗牌图片
  static Future<void> preloadTarotImages(BuildContext context) async {
    try {
      // 预加载大阿卡纳图片
      for (String imagePath in _majorArcanaImages.values) {
        await precacheImage(AssetImage(imagePath), context);
      }
      
      // 预加载常用的小阿卡纳图片（王牌）
      final aceCards = _minorArcanaImages.entries
          .where((entry) => entry.key.contains('Ace'))
          .map((entry) => entry.value);
      
      for (String imagePath in aceCards) {
        await precacheImage(AssetImage(imagePath), context);
      }
      
      // 预加载牌背图片
      await precacheImage(const AssetImage(_defaultCardBackPath), context);
      
    } catch (e) {
      debugPrint('预加载塔罗牌图片失败: $e');
    }
  }
  
  /// 检查图片是否存在
  static bool hasCardImage(String cardName) {
    return _majorArcanaImages.containsKey(cardName) || 
           _minorArcanaImages.containsKey(cardName);
  }
  
  /// 获取所有可用的塔罗牌名称
  static List<String> getAllCardNames() {
    return [
      ..._majorArcanaImages.keys,
      ..._minorArcanaImages.keys,
    ];
  }
  
  /// 构建带错误处理的图片Widget
  static Widget buildCardImage({
    required String cardName,
    double? width,
    double? height,
    BoxFit fit = BoxFit.cover,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    final imagePath = getCardImage(cardName);
    
    return Image.asset(
      imagePath,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        debugPrint('加载塔罗牌图片失败: $cardName, 路径: $imagePath, 错误: $error');
        
        return errorWidget ?? Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(12),
            gradient: const LinearGradient(
              colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.auto_awesome,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(height: 8),
              Text(
                cardName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        );
      },

    );
  }
}
