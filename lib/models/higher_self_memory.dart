class HigherSelfMemory {
  final String id;
  final String userId;
  final String memoryType; // 'strength', 'growth', 'pattern', 'concern'
  final String content;
  final double confidenceScore; // 0-1，记忆的可信度
  final List<String> sourceDiaryIds;
  final DateTime createdAt;
  final DateTime updatedAt;

  HigherSelfMemory({
    required this.id,
    required this.userId,
    required this.memoryType,
    required this.content,
    required this.confidenceScore,
    required this.sourceDiaryIds,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HigherSelfMemory.fromJson(Map<String, dynamic> json) {
    return HigherSelfMemory(
      id: json['id'],
      userId: json['user_id'],
      memoryType: json['memory_type'],
      content: json['content'],
      confidenceScore: (json['confidence_score'] as num).toDouble(),
      sourceDiaryIds: List<String>.from(json['source_diary_ids'] ?? []),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'memory_type': memoryType,
      'content': content,
      'confidence_score': confidenceScore,
      'source_diary_ids': sourceDiaryIds,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get memoryTypeDisplay {
    switch (memoryType) {
      case 'strength':
        return '💎 内在优势';
      case 'growth':
        return '🌱 成长轨迹';
      case 'pattern':
        return '🔄 行为模式';
      case 'concern':
        return '💝 关注点';
      default:
        return '✨ 记忆';
    }
  }

  String get confidenceDisplay {
    if (confidenceScore >= 0.8) return '非常确信';
    if (confidenceScore >= 0.6) return '比较确信';
    if (confidenceScore >= 0.4) return '有些了解';
    return '初步观察';
  }
}
