import 'package:ai_tarot_reading/services/psychological_guidance_service.dart';

/// 心理引导式塔罗解读模型
class PsychologicalTarotReading {
  final String basicReading;                    // 基础塔罗解读
  final QuestionType questionType;              // 问题类型
  final Map<String, List<String>> guidingQuestions; // 每张牌的引导问题
  final List<String> conversationHistory;      // 对话历史
  final PsychologicalPattern identifiedPattern; // 识别的心理模式
  final List<HealingTechnique> healingTechniques; // 疗愈技术
  final String manifestationGuidance;           // 显化指引
  final int conversationRound;                  // 对话轮次

  PsychologicalTarotReading({
    required this.basicReading,
    required this.questionType,
    required this.guidingQuestions,
    required this.conversationHistory,
    required this.identifiedPattern,
    required this.healingTechniques,
    required this.manifestationGuidance,
    required this.conversationRound,
  });

  /// 复制并更新
  PsychologicalTarotReading copyWith({
    String? basicReading,
    QuestionType? questionType,
    Map<String, List<String>>? guidingQuestions,
    List<String>? conversationHistory,
    PsychologicalPattern? identifiedPattern,
    List<HealingTechnique>? healingTechniques,
    String? manifestationGuidance,
    int? conversationRound,
  }) {
    return PsychologicalTarotReading(
      basicReading: basicReading ?? this.basicReading,
      questionType: questionType ?? this.questionType,
      guidingQuestions: guidingQuestions ?? this.guidingQuestions,
      conversationHistory: conversationHistory ?? this.conversationHistory,
      identifiedPattern: identifiedPattern ?? this.identifiedPattern,
      healingTechniques: healingTechniques ?? this.healingTechniques,
      manifestationGuidance: manifestationGuidance ?? this.manifestationGuidance,
      conversationRound: conversationRound ?? this.conversationRound,
    );
  }

  /// 获取问题类型的中文描述
  String get questionTypeDescription {
    switch (questionType) {
      case QuestionType.relationship:
        return '情感关系';
      case QuestionType.career:
        return '事业发展';
      case QuestionType.decision:
        return '决策选择';
      case QuestionType.emotional:
        return '情绪疗愈';
      case QuestionType.general:
        return '综合指引';
    }
  }

  /// 获取心理模式的中文描述
  String get patternDescription {
    switch (identifiedPattern) {
      case PsychologicalPattern.attachment:
        return '依恋模式 - 难以放下过去的关系或经历';
      case PsychologicalPattern.pleasing:
        return '讨好模式 - 习惯性地优先考虑他人需求';
      case PsychologicalPattern.rumination:
        return '内耗模式 - 过度思考和自我批判';
      case PsychologicalPattern.fear:
        return '恐惧模式 - 被担心和焦虑所困扰';
      case PsychologicalPattern.healthy:
        return '阳光健康模式 - 心理状态积极平衡';
      case PsychologicalPattern.unknown:
        return '探索中 - 需要更多对话来识别模式';
    }
  }

  /// 是否已识别出明确的心理模式
  bool get hasIdentifiedPattern {
    return identifiedPattern != PsychologicalPattern.unknown;
  }

  /// 是否可以进行下一轮对话（智能判断）
  bool get canContinueConversation {
    // 基础限制：最多10轮
    if (conversationRound >= 10) return false;

    // 如果识别到健康模式，3轮后可以结束
    if (identifiedPattern == PsychologicalPattern.healthy && conversationRound >= 3) {
      return false;
    }

    // 如果识别到明确的问题模式且已提供疗愈建议，4轮后可以结束
    if (hasIdentifiedPattern &&
        identifiedPattern != PsychologicalPattern.healthy &&
        conversationRound >= 4) {
      return false;
    }

    // 其他情况继续对话
    return true;
  }

  /// 获取当前阶段的描述
  String get currentStageDescription {
    switch (conversationRound) {
      case 1:
        return '初步解读 - 了解基本情况';
      case 2:
        return '深入探索 - 挖掘情感模式';
      case 3:
        return '模式识别 - 探索深层原因';
      case 4:
        return '疗愈引导 - 提供释放技术';
      case 5:
        return '整合总结 - 显化新的可能';
      default:
        return '对话完成';
    }
  }

  /// 获取下一步建议
  String get nextStepSuggestion {
    if (!hasIdentifiedPattern && canContinueConversation) {
      return '继续对话，帮助AI更好地理解你的情况';
    } else if (hasIdentifiedPattern && healingTechniques.isNotEmpty) {
      return '尝试推荐的疗愈练习，开始内在转化';
    } else {
      return '将解读的洞察应用到日常生活中';
    }
  }

  /// 转换为JSON（用于存储）
  Map<String, dynamic> toJson() {
    return {
      'basicReading': basicReading,
      'questionType': questionType.toString(),
      'guidingQuestions': guidingQuestions,
      'conversationHistory': conversationHistory,
      'identifiedPattern': identifiedPattern.toString(),
      'healingTechniques': healingTechniques.map((t) => {
        'name': t.name,
        'description': t.description,
        'instruction': t.instruction,
      }).toList(),
      'manifestationGuidance': manifestationGuidance,
      'conversationRound': conversationRound,
    };
  }

  /// 从JSON创建实例
  static PsychologicalTarotReading fromJson(Map<String, dynamic> json) {
    return PsychologicalTarotReading(
      basicReading: json['basicReading'] ?? '',
      questionType: QuestionType.values.firstWhere(
        (e) => e.toString() == json['questionType'],
        orElse: () => QuestionType.general,
      ),
      guidingQuestions: Map<String, List<String>>.from(
        json['guidingQuestions'] ?? {},
      ),
      conversationHistory: List<String>.from(json['conversationHistory'] ?? []),
      identifiedPattern: PsychologicalPattern.values.firstWhere(
        (e) => e.toString() == json['identifiedPattern'],
        orElse: () => PsychologicalPattern.unknown,
      ),
      healingTechniques: (json['healingTechniques'] as List? ?? [])
          .map((t) => HealingTechnique(
                name: t['name'] ?? '',
                description: t['description'] ?? '',
                instruction: t['instruction'] ?? '',
              ))
          .toList(),
      manifestationGuidance: json['manifestationGuidance'] ?? '',
      conversationRound: json['conversationRound'] ?? 1,
    );
  }
}
