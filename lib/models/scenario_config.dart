import 'package:ai_tarot_reading/models/tarot_spread.dart';

/// 场景化配置模型
class ScenarioConfig {
  final String id;
  final String title;
  final String subtitle;
  final String description;
  final String category;
  final String icon;
  final String color;
  final TarotSpread recommendedSpread;
  final List<String> suggestedQuestions;
  final List<String> guidingPrompts;
  final List<String> healingTechniques;
  final String manifestationFocus;

  const ScenarioConfig({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.category,
    required this.icon,
    required this.color,
    required this.recommendedSpread,
    required this.suggestedQuestions,
    required this.guidingPrompts,
    required this.healingTechniques,
    required this.manifestationFocus,
  });

  /// 所有场景化配置
  static final List<ScenarioConfig> allScenarios = [
    // 💕 情感关系类
    ScenarioConfig(
      id: 'love_entanglement',
      title: '反复纠缠关系',
      subtitle: '走出情感循环',
      description: '分析反复纠缠的情感模式，找到真正的解脱之路',
      category: '情感关系',
      icon: '💕',
      color: '#EC4899',
      recommendedSpread: TarotSpread.getSpreadByName('爱情关系牌阵')!,
      suggestedQuestions: [
        '为什么我总是被这段关系困住？',
        '这种反复纠缠的模式何时能结束？',
        '我该如何彻底放下这个人？',
        '这段关系教会了我什么？',
      ],
      guidingPrompts: [
        '这张牌可能代表你一直在回头看过去。你觉得，是什么让你这么难以割舍？',
        '这种"曾被温柔对待"的记忆，可能唤起了哪些你小时候的渴望呢？',
        '你现在在复演那个旧场景，试图重新赢得那份曾缺失的关注。',
      ],
      healingTechniques: [
        '情绪聚焦法：感受胸口的紧绷，对它说"我看见你了"',
        '书写释放：写下你最想对这个人说的话，然后撕掉',
        '正念呼吸：每当想起对方时，深呼吸3次，回到当下',
      ],
      manifestationFocus: '显化一段全然支持你、让你感到安全的新关系',
    ),

    ScenarioConfig(
      id: 'self_worth_exploration',
      title: '总是在讨好别人',
      subtitle: '重建自我边界',
      description: '探索讨好模式的根源，学会表达真实需求',
      category: '自我探索',
      icon: '🌱',
      color: '#10B981',
      recommendedSpread: TarotSpread.getSpreadByName('心灵疗愈牌阵')!,
      suggestedQuestions: [
        '为什么我总是害怕别人不高兴？',
        '我的真实需求是什么？',
        '如何学会说"不"？',
        '怎样建立健康的人际边界？',
      ],
      guidingPrompts: [
        '你可能对表达自己的真实需求感到不安，担心别人因此不喜欢你',
        '你愿意回忆一下，这种"不敢说"的模式，是从什么时候开始的？',
        '那个小小的你太辛苦了，现在你可以试着对那个小小的你说一声"我懂你了"',
      ],
      healingTechniques: [
        '内在小孩疗愈：对小时候的自己说"你可以表达真实的感受"',
        '边界练习：每天练习说一次"不"，从小事开始',
        '自我肯定：每天对镜子说"我的需求很重要"',
      ],
      manifestationFocus: '显化能够真实表达自己、被人尊重边界的生活状态',
    ),

    ScenarioConfig(
      id: 'career_breakthrough',
      title: '工作遇到瓶颈',
      subtitle: '突破职场困境',
      description: '分析职场阻力，找到突破瓶颈的关键路径',
      category: '事业发展',
      icon: '💼',
      color: '#3B82F6',
      recommendedSpread: TarotSpread.getSpreadByName('事业成长牌阵')!,
      suggestedQuestions: [
        '我的职场瓶颈在哪里？',
        '什么阻碍了我的发展？',
        '如何突破当前的困境？',
        '我的职业天赋是什么？',
      ],
      guidingPrompts: [
        '这张牌显示你可能在某个领域有未被发现的潜能',
        '你觉得是什么内在信念限制了你的发展？',
        '如果没有任何限制，你最想在职场上实现什么？',
      ],
      healingTechniques: [
        '能量清理：想象清除掉所有职场负能量',
        '成功冥想：每天花5分钟想象理想的工作状态',
        '感恩练习：列出当前工作中值得感恩的3件事',
      ],
      manifestationFocus: '显化一个能发挥你天赋、获得认可的职业环境',
    ),

    ScenarioConfig(
      id: 'inner_conflict',
      title: '内耗感很强',
      subtitle: '内在和解之路',
      description: '理解内耗的根源，找到内在和谐的方法',
      category: '自我探索',
      icon: '🧠',
      color: '#8B5CF6',
      recommendedSpread: TarotSpread.getSpreadByName('六芒星牌阵')!,
      suggestedQuestions: [
        '我为什么总是内耗？',
        '内心的冲突来自哪里？',
        '如何让内在达到和谐？',
        '什么能帮我停止自我攻击？',
      ],
      guidingPrompts: [
        '内耗往往来自内在不同声音的冲突，你能听到哪些声音？',
        '这些声音中，哪一个最像你的父母或老师？',
        '如果你的内在有一个智慧的声音，它会对你说什么？',
      ],
      healingTechniques: [
        '内在对话：与内在不同的声音进行温和的对话',
        '自我慈悲：当内耗时，问自己"如果朋友遇到这种情况，我会怎么安慰他？"',
        '正念观察：观察内耗的想法，不评判，只是看见',
      ],
      manifestationFocus: '显化内在平静、自我接纳的心理状态',
    ),

    ScenarioConfig(
      id: 'manifestation_blocks',
      title: '许愿总是无效',
      subtitle: '清理显化阻力',
      description: '发现阻碍显化的潜意识信念，重新校准显化频率',
      category: '显化阻力',
      icon: '✨',
      color: '#F59E0B',
      recommendedSpread: TarotSpread.getSpreadByName('新月许愿牌阵')!,
      suggestedQuestions: [
        '什么阻碍了我的愿望实现？',
        '我的潜意识有什么限制性信念？',
        '如何提升我的显化能力？',
        '宇宙想通过这个阻力教会我什么？',
      ],
      guidingPrompts: [
        '你的愿望可能与内在某个信念产生了冲突',
        '小时候你听到过哪些关于"不配得"的话？',
        '如果你真的得到了想要的，你最担心会发生什么？',
      ],
      healingTechniques: [
        '信念重写：写下限制性信念，然后重写为支持性信念',
        '配得感练习：每天对自己说"我值得拥有美好的事物"',
        '感恩显化：感恩已经拥有的，提升接收频率',
      ],
      manifestationFocus: '显化与宇宙频率对齐、轻松实现愿望的能力',
    ),

    ScenarioConfig(
      id: 'decision_anxiety',
      title: '重要选择焦虑',
      subtitle: '智慧决策指引',
      description: '在人生重要节点获得清晰指引，做出最适合的选择',
      category: '决策选择',
      icon: '🎯',
      color: '#EF4444',
      recommendedSpread: TarotSpread.getSpreadByName('二选一牌阵')!,
      suggestedQuestions: [
        '这个选择对我的人生意味着什么？',
        '哪个选项更符合我的灵魂使命？',
        '我的直觉在告诉我什么？',
        '如何做出不后悔的决定？',
      ],
      guidingPrompts: [
        '当你想象选择A时，身体有什么感觉？',
        '当你想象选择B时，心情如何变化？',
        '如果没有外界压力，你的内心更倾向于哪个？',
      ],
      healingTechniques: [
        '身体智慧：用身体感受来判断选择的对错',
        '未来回望：想象5年后的自己会如何看待这个选择',
        '直觉冥想：静心5分钟，让答案自然浮现',
      ],
      manifestationFocus: '显化清晰的直觉力和做出正确决策的智慧',
    ),
  ];

  /// 根据类别获取场景
  static List<ScenarioConfig> getScenariosByCategory(String category) {
    return allScenarios.where((scenario) => scenario.category == category).toList();
  }

  /// 根据ID获取场景
  static ScenarioConfig? getScenarioById(String id) {
    try {
      return allScenarios.firstWhere((scenario) => scenario.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 获取所有类别
  static List<String> getAllCategories() {
    return allScenarios.map((scenario) => scenario.category).toSet().toList();
  }
}
