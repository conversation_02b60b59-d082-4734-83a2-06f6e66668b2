class TarotSpread {
  final String name;
  final int cardCount;
  final String description;
  final List<String> positions;

  // 翻译键
  final String nameKey;
  final String descriptionKey;
  final List<String> positionKeys;

  const TarotSpread({
    required this.name,
    required this.cardCount,
    required this.description,
    required this.positions,
    required this.nameKey,
    required this.descriptionKey,
    required this.positionKeys,
  });

  // 获取翻译后的名称
  String getTranslatedName(Function(String) translate) {
    return translate(nameKey);
  }

  // 获取翻译后的描述
  String getTranslatedDescription(Function(String) translate) {
    return translate(descriptionKey);
  }

  // 获取翻译后的牌位列表
  List<String> getTranslatedPositions(Function(String) translate) {
    return positionKeys.map((key) => translate(key) as String).toList();
  }

  // 所有可用的牌阵
  static const List<TarotSpread> allSpreads = [
    TarotSpread(
      name: '单张牌阵',
      cardCount: 1,
      description: '聚焦当前能量或宇宙提示，适合快速日常指引。',
      positions: ['当前能量/宇宙提示'],
      nameKey: 'spread_single_card',
      descriptionKey: 'single_card_description',
      positionKeys: ['position_current_energy'],
    ),
    TarotSpread(
      name: '三张牌阵（经典）',
      cardCount: 3,
      description: '过去、现在、未来，揭示事件的时间脉络。',
      positions: ['过去', '现在', '未来'],
      nameKey: 'spread_three_card_classic',
      descriptionKey: 'three_card_classic_description',
      positionKeys: ['position_past', 'position_present', 'position_future'],
    ),
    TarotSpread(
      name: '建议牌阵',
      cardCount: 3,
      description: '当前状况、挑战、建议行动，适合日常问题分析。',
      positions: ['当前状况', '挑战', '建议行动'],
      nameKey: 'spread_advice',
      descriptionKey: 'advice_spread_description',
      positionKeys: ['position_current_situation', 'position_challenge', 'position_advice'],
    ),
    TarotSpread(
      name: '二选一牌阵',
      cardCount: 6,
      description: '两个选项各用三张牌，分别代表现状、影响、结果，帮助做选择。',
      positions: ['选项A现状', '选项A影响', '选项A结果', '选项B现状', '选项B影响', '选项B结果'],
      nameKey: 'spread_choice_two',
      descriptionKey: 'choice_spread_description',
      positionKeys: ['position_option_a_current', 'position_option_a_influence', 'position_option_a_result', 'position_option_b_current', 'position_option_b_influence', 'position_option_b_result'],
    ),
    TarotSpread(
      name: '爱情关系牌阵',
      cardCount: 6,
      description: '你、对方、你怎么看关系、对方怎么看、现状、未来，分析感情状态。',
      positions: ['你', '对方', '你怎么看关系', '对方怎么看', '现状', '未来'],
      nameKey: 'spread_love_relationship',
      descriptionKey: 'love_relationship_description',
      positionKeys: ['you', 'other_person', 'how_you_see', 'how_other_sees', 'current_status', 'future'],
    ),
    TarotSpread(
      name: '六芒星牌阵',
      cardCount: 6,
      description: '问题本质、阻碍、机会、建议、外部环境、最终结果，适合问题全景分析。',
      positions: ['问题本质', '阻碍', '机会', '建议', '外部环境', '最终结果'],
      nameKey: 'spread_six_point_star',
      descriptionKey: 'hexagram_spread_description',
      positionKeys: ['position_problem_essence', 'position_obstacle', 'position_opportunity', 'position_advice', 'position_external_environment', 'position_final_result'],
    ),
    TarotSpread(
      name: '凯尔特十字牌阵',
      cardCount: 10,
      description: '深度问题剖析，分析现状、阻碍、潜意识、过去未来走向、周边环境和结局等10个面向。',
      positions: [
        '当前状况',
        '挑战/阻碍',
        '遥远过去',
        '近期过去',
        '可能的未来',
        '近期未来',
        '你的方法',
        '外部影响',
        '希望与恐惧',
        '最终结果'
      ],
      nameKey: 'spread_celtic_cross',
      descriptionKey: 'celtic_cross_description',
      positionKeys: [
        'position_current_situation',
        'position_challenge_obstacle',
        'position_distant_past',
        'position_recent_past',
        'position_possible_future',
        'position_near_future',
        'position_your_approach',
        'position_external_influence',
        'position_hopes_fears',
        'position_final_outcome'
      ],
    ),
    TarotSpread(
      name: '每月运势牌阵',
      cardCount: 4,
      description: '每张代表一周/阶段的运势（或身体、情绪、事业、关系等），适合周期性预测。',
      positions: ['第一周', '第二周', '第三周', '第四周'],
      nameKey: 'spread_monthly_fortune',
      descriptionKey: 'monthly_fortune_description',
      positionKeys: ['position_week_1', 'position_week_2', 'position_week_3', 'position_week_4'],
    ),
    TarotSpread(
      name: '七日运势牌阵',
      cardCount: 7,
      description: '每张代表一周中的一天（周一至周日），快速查看本周发展。',
      positions: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      nameKey: 'spread_weekly_fortune',
      descriptionKey: 'weekly_fortune_description',
      positionKeys: ['position_monday', 'position_tuesday', 'position_wednesday', 'position_thursday', 'position_friday', 'position_saturday', 'position_sunday'],
    ),
    TarotSpread(
      name: '事业成长牌阵',
      cardCount: 5,
      description: '目前状态、内在潜能、阻碍、发展建议、最终结果，适合职场问题。',
      positions: ['目前状态', '内在潜能', '阻碍', '发展建议', '最终结果'],
      nameKey: 'spread_career_growth',
      descriptionKey: 'career_growth_description',
      positionKeys: ['position_current_state', 'position_inner_potential', 'position_obstacle', 'position_development_advice', 'position_final_result'],
    ),
    TarotSpread(
      name: '心灵疗愈牌阵',
      cardCount: 5,
      description: '内在创伤、当下情绪、内在需求、疗愈路径、转化结果，适合情绪/心理咨询类场景。',
      positions: ['内在创伤', '当下情绪', '内在需求', '疗愈路径', '转化结果'],
      nameKey: 'spread_soul_healing',
      descriptionKey: 'soul_healing_description',
      positionKeys: ['position_inner_trauma', 'position_current_emotion', 'position_inner_need', 'position_healing_path', 'position_transformation_result'],
    ),
    TarotSpread(
      name: '新月许愿牌阵',
      cardCount: 6,
      description: '当前状态、目标能量、障碍、宇宙指引、行动建议、可能结果，配合月相仪式使用。',
      positions: ['当前状态', '目标能量', '障碍', '宇宙指引', '行动建议', '可能结果'],
      nameKey: 'spread_new_moon_wish',
      descriptionKey: 'new_moon_wish_description',
      positionKeys: ['position_current_state', 'position_goal_energy', 'position_obstacle', 'position_cosmic_guidance', 'position_action_advice', 'position_possible_result'],
    ),
    TarotSpread(
      name: '全年运势牌阵',
      cardCount: 12,
      description: '每张代表一个月的整体能量，用于年度预测与规划。',
      positions: [
        '1月', '2月', '3月', '4月', '5月', '6月',
        '7月', '8月', '9月', '10月', '11月', '12月'
      ],
      nameKey: 'spread_yearly_fortune',
      descriptionKey: 'yearly_fortune_description',
      positionKeys: [
        'position_january', 'position_february', 'position_march', 'position_april', 'position_may', 'position_june',
        'position_july', 'position_august', 'position_september', 'position_october', 'position_november', 'position_december'
      ],
    ),
  ];

  // 根据名称获取牌阵
  static TarotSpread? getSpreadByName(String name) {
    try {
      return allSpreads.firstWhere((spread) => spread.name == name);
    } catch (e) {
      return null;
    }
  }

  // 获取适合特定专题的牌阵
  static List<TarotSpread> getSpreadsForTopic(String topic) {
    switch (topic) {
      case 'Yes or No':
        return [getSpreadByName('单张牌阵')!];
      case '二选一抉择':
        return [getSpreadByName('二选一牌阵')!];
      case '三选一抉择':
        return [getSpreadByName('建议牌阵')!];
      case '真爱时机':
      case '分手复合':
      case '暗恋心意':
        return [getSpreadByName('爱情关系牌阵')!];
      case '第三者问题':
        return [getSpreadByName('六芒星牌阵')!];
      case '人际关系':
        return [getSpreadByName('六芒星牌阵')!];
      case '事业发展':
      case '跳槽转职':
      case '升职加薪':
      case '创业机会':
        return [getSpreadByName('事业成长牌阵')!];
      case '学业规划':
      case '考试运势':
        return [getSpreadByName('建议牌阵')!];
      case '自我认知':
        return [getSpreadByName('六芒星牌阵')!];
      case '情绪疗愈':
        return [getSpreadByName('心灵疗愈牌阵')!];
      case '每日灵感':
        return [getSpreadByName('单张牌阵')!];
      case '购物智慧':
        return [getSpreadByName('建议牌阵')!];
      case '财富运势':
        return [getSpreadByName('事业成长牌阵')!];
      case '宠物情绪':
      case '宠物缘分':
      case '宠物感情':
      case '宠物陪伴':
      case '宠物离别':
        return [getSpreadByName('建议牌阵')!];
      default:
        return [getSpreadByName('三张牌阵（经典）')!];
    }
  }
}
