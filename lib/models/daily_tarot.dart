import 'package:ai_tarot_reading/models/tarot_card.dart';

enum ManifestationGoal {
  wealth('财富', '💰'),
  career('事业', '💼'),
  beauty('美貌', '🌸'),
  fame('名气', '✨'),
  love('感情', '💕');

  const ManifestationGoal(this.displayName, this.emoji);
  final String displayName;
  final String emoji;
}

enum ManifestationVersion {
  western('西方版'),
  eastern('东方版');

  const ManifestationVersion(this.displayName);
  final String displayName;
}

class DailyTarot {
  final DateTime date;
  final TarotCard? card;
  final String? fortune;
  final String? advice;
  final bool isDrawn;
  final ManifestationGoal? manifestationGoal;
  final String? affirmation;
  final String? manifestationJournal;

  DailyTarot({
    required this.date,
    this.card,
    this.fortune,
    this.advice,
    required this.isDrawn,
    this.manifestationGoal,
    this.affirmation,
    this.manifestationJournal,
  });

  DailyTarot copyWith({
    DateTime? date,
    TarotCard? card,
    String? fortune,
    String? advice,
    bool? isDrawn,
    ManifestationGoal? manifestationGoal,
    String? affirmation,
    String? manifestationJournal,
  }) {
    return DailyTarot(
      date: date ?? this.date,
      card: card ?? this.card,
      fortune: fortune ?? this.fortune,
      advice: advice ?? this.advice,
      isDrawn: isDrawn ?? this.isDrawn,
      manifestationGoal: manifestationGoal ?? this.manifestationGoal,
      affirmation: affirmation ?? this.affirmation,
      manifestationJournal: manifestationJournal ?? this.manifestationJournal,
    );
  }
}
