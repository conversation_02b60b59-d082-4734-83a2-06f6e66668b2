import 'package:flutter/material.dart';
import 'dart:ui';

/// 苹果Liquid Glass设计主题
/// 基于iOS 26最新设计语言和您提供的UI参考
class LiquidGlassTheme {
  // 🎨 主要颜色系统
  static const Color primaryBlue = Color(0xFF0066FF);
  static const Color primaryCyan = Color(0xFF00C2FF);
  static const Color backgroundPrimary = Color(0xFFF2F7FC);
  static const Color backgroundSecondary = Color(0xFFFEFFFF);
  static const Color surfaceGlass = Color(0x59FFFFFF); // rgba(255, 255, 255, 0.35)
  static const Color surfaceCard = Color(0x4AFFFFFF); // rgba(255, 255, 255, 0.29)
  static const Color textPrimary = Color(0xFF2A404E);
  static const Color textSecondary = Color(0x4D2A404E); // rgba(42, 64, 78, 0.3)
  static const Color accent = Color(0xFF0194FF);

  // 🌊 渐变系统
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryBlue, primaryCyan],
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    stops: [0.0, 0.7188, 1.0],
    colors: [
      Color(0x00FFFFFF), // 透明白色
      Color(0x8FFFFFFF), // 56% 白色
      Color(0x4AFFFFFF), // 29% 白色
    ],
  );

  static const LinearGradient glassGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Color(0x00FFFFFF), // 透明
      Color(0x8FFFFFFF), // 半透明白色
    ],
  );

  // 🔮 Liquid Glass容器装饰
  static BoxDecoration createLiquidGlassDecoration({
    double borderRadius = 23.46,
    double blur = 9.92,
    bool includeGradient = true,
    Color? customColor,
  }) {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(borderRadius),
      color: customColor ?? surfaceCard,
      border: Border.all(
        color: Colors.white.withOpacity(0.35),
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.08),
          blurRadius: 40,
          offset: const Offset(0, 12),
          spreadRadius: 0,
        ),
        BoxShadow(
          color: Colors.black.withOpacity(0.04),
          blurRadius: 20,
          offset: const Offset(0, 4),
          spreadRadius: 0,
        ),
      ],
    );
  }

  // 🍎 主卡片装饰
  static BoxDecoration createMainCardDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(23.46),
      gradient: const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0x4DFFFFFF), // 30% 白色
          Color(0x1AFFFFFF), // 10% 白色
        ],
      ),
      border: Border.all(
        color: Colors.white.withOpacity(0.3),
        width: 1.0,
      ),
      boxShadow: [
        BoxShadow(
          color: Colors.black.withOpacity(0.1),
          blurRadius: 42.228,
          offset: const Offset(0, -51.612),
        ),
        BoxShadow(
          color: Colors.white,
          blurRadius: 39.1,
          offset: const Offset(0, 0),
        ),
      ],
    );
  }

  // 🔘 按钮样式
  static BoxDecoration createButtonDecoration({
    bool isPressed = false,
    bool isSecondary = false,
  }) {
    if (isSecondary) {
      return BoxDecoration(
        borderRadius: BorderRadius.circular(40),
        color: surfaceGlass,
        border: Border.all(
          color: Colors.white.withOpacity(0.35),
          width: 1.0,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 13.3871,
            offset: const Offset(0, 4),
          ),
        ],
      );
    }

    return BoxDecoration(
      borderRadius: BorderRadius.circular(30),
      gradient: primaryGradient,
      boxShadow: [
        BoxShadow(
          color: primaryBlue.withOpacity(0.3),
          blurRadius: isPressed ? 15 : 20,
          offset: Offset(0, isPressed ? 4 : 8),
        ),
      ],
    );
  }

  // 📱 状态栏区域装饰
  static Widget createStatusBarArea({
    required String time,
    double batteryLevel = 1.0,
    int signalStrength = 4,
    bool hasWifi = true,
  }) {
    return Container(
      width: double.infinity,
      height: 44,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 时间显示
          Text(
            time,
            style: const TextStyle(
              fontFamily: 'Helvetica',
              fontSize: 13.59,
              fontWeight: FontWeight.w400,
              color: textSecondary,
            ),
          ),
          // 右侧图标
          Row(
            children: [
              // 信号图标
              Icon(
                Icons.signal_cellular_4_bar,
                size: 12.97,
                color: textSecondary,
              ),
              const SizedBox(width: 4),
              // WiFi图标
              if (hasWifi)
                Icon(
                  Icons.wifi,
                  size: 16.88,
                  color: textSecondary,
                ),
              const SizedBox(width: 4),
              // 电池图标
              Icon(
                batteryLevel > 0.2 ? Icons.battery_full : Icons.battery_2_bar,
                size: 16.88,
                color: textSecondary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 🌟 装饰性元素
  static Widget createFloatingElement({
    required double size,
    Color? color,
    double opacity = 0.1,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: (color ?? accent).withOpacity(opacity),
        shape: BoxShape.circle,
        border: Border.all(
          color: accent.withOpacity(0.2),
          width: 1.05946,
        ),
      ),
    );
  }

  // 📦 页面指示器
  static Widget createPageIndicator({
    required int currentPage,
    required int totalPages,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(totalPages, (index) {
        final isActive = index == currentPage;
        return Container(
          width: 8,
          height: 8,
          margin: const EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            gradient: isActive ? primaryGradient : null,
            color: isActive ? null : surfaceGlass,
            border: isActive
                ? null
                : Border.all(
                    color: Colors.white.withOpacity(0.35),
                    width: 1.0,
                  ),
          ),
        );
      }),
    );
  }

  // ✨ 发光效果
  static BoxShadow createGlowEffect({
    Color? color,
    double intensity = 0.3,
    double radius = 20,
  }) {
    return BoxShadow(
      color: (color ?? primaryCyan).withOpacity(intensity),
      blurRadius: radius,
      offset: const Offset(0, 0),
      spreadRadius: 5,
    );
  }

  // 🔄 呼吸动画装饰
  static BoxDecoration createBreathingDecoration({
    required double animationValue,
    Color? baseColor,
  }) {
    final glowIntensity = (animationValue * 0.3) + 0.1;
    return BoxDecoration(
      borderRadius: BorderRadius.circular(23.46),
      color: baseColor ?? surfaceCard,
      boxShadow: [
        BoxShadow(
          color: primaryCyan.withOpacity(glowIntensity),
          blurRadius: 30 + (animationValue * 10),
          offset: const Offset(0, 0),
          spreadRadius: 2 + (animationValue * 3),
        ),
      ],
    );
  }

  // 📏 尺寸常量
  static const double cardBorderRadius = 23.46;
  static const double buttonBorderRadius = 30.0;
  static const double smallBorderRadius = 7.038;
  static const double standardPadding = 20.0;
  static const double cardPadding = 24.0;
  static const double elementSpacing = 16.0;

  // 🎨 文字样式
  static const TextStyle headlineStyle = TextStyle(
    fontFamily: 'Circular Std',
    fontSize: 18,
    fontWeight: FontWeight.w700,
    height: 1.28,
    color: textPrimary,
  );

  static const TextStyle buttonTextStyle = TextStyle(
    fontFamily: 'Circular Std',
    fontSize: 15,
    fontWeight: FontWeight.w700,
    height: 1.5,
    color: Colors.white,
  );

  static const TextStyle cardNumberStyle = TextStyle(
    fontFamily: 'Poppins',
    fontSize: 19.55,
    fontWeight: FontWeight.w600,
    height: 1.48, // 29/19.55
    color: Colors.white,
  );
}

/// 带有Liquid Glass效果的自定义Widget
class LiquidGlassContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool showBorder;
  final bool enableBackdropFilter;
  final Color? backgroundColor;
  final double blurIntensity;

  const LiquidGlassContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(20),
    this.margin,
    this.borderRadius = 23.46,
    this.showBorder = true,
    this.enableBackdropFilter = true,
    this.backgroundColor,
    this.blurIntensity = 9.92,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Container(
      width: width,
      height: height,
      margin: margin,
      padding: padding,
      decoration: LiquidGlassTheme.createLiquidGlassDecoration(
        borderRadius: borderRadius,
        blur: blurIntensity,
        customColor: backgroundColor,
      ),
      child: child,
    );

    if (enableBackdropFilter) {
      content = ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurIntensity, sigmaY: blurIntensity),
          child: content,
        ),
      );
    }

    return content;
  }
}

/// 带有发光效果的按钮
class LiquidGlassButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final bool isSecondary;
  final IconData? icon;
  final double? width;
  final double height;

  const LiquidGlassButton({
    super.key,
    required this.text,
    this.onPressed,
    this.isSecondary = false,
    this.icon,
    this.width,
    this.height = 60,
  });

  @override
  State<LiquidGlassButton> createState() => _LiquidGlassButtonState();
}

class _LiquidGlassButtonState extends State<LiquidGlassButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
    widget.onPressed?.call();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 1.0 - (_controller.value * 0.05),
          child: GestureDetector(
            onTapDown: _handleTapDown,
            onTapUp: _handleTapUp,
            onTapCancel: _handleTapCancel,
            child: Container(
              width: widget.width,
              height: widget.height,
              decoration: LiquidGlassTheme.createButtonDecoration(
                isPressed: _isPressed,
                isSecondary: widget.isSecondary,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(widget.isSecondary ? 40 : 30),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 13.3871, sigmaY: 13.3871),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widget.icon != null) ...[
                          Icon(
                            widget.icon,
                            color: widget.isSecondary
                                ? LiquidGlassTheme.textPrimary
                                : Colors.white,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                        ],
                        Text(
                          widget.text,
                          style: widget.isSecondary
                              ? LiquidGlassTheme.buttonTextStyle.copyWith(
                                  color: LiquidGlassTheme.textPrimary,
                                )
                              : LiquidGlassTheme.buttonTextStyle,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
} 