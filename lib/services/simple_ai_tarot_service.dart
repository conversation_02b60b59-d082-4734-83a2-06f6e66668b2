import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'dart:io';

/// 简化版AI塔罗服务
/// 直接扮演塔罗师，针对用户问题和牌的结果回答
/// 如果用户有追问，通过追问分析用户底层心理问题进行开解
class SimpleAITarotService {
  static final _supabase = Supabase.instance.client;

  /// 获取当前应用语言设置
  static String _getCurrentAppLanguage() {
    // 默认英文，针对海外用户
    // 可以从SharedPreferences或Provider中获取用户设置的语言
    return 'en';
  }

  /// 获取指定语言的卡牌名称
  static String _getCardNameForLanguage(TarotCard card, String language) {
    // 简化处理：英文使用英文名，其他使用原名
    if (language == 'en') {
      // 对于英文，使用英文卡牌名称
      return _getEnglishCardName(card.id);
    }
    return card.name; // 其他语言使用原名
  }

  /// 获取指定语言的卡牌描述
  static String _getCardDescriptionForLanguage(TarotCard card, String language) {
    if (language == 'en') {
      return _getEnglishCardDescription(card.id);
    }
    return card.description;
  }

  /// 获取指定语言的卡牌含义
  static String _getCardMeaningForLanguage(TarotCard card, String language) {
    if (language == 'en') {
      return _getEnglishCardMeaning(card.id, card.isReversed);
    }
    return card.meaning;
  }

  /// 获取指定语言的卡牌关键词
  static List<String> _getCardKeywordsForLanguage(TarotCard card, String language) {
    if (language == 'en') {
      return _getEnglishCardKeywords(card.id);
    }
    return card.keywords;
  }

  /// 获取英文卡牌名称
  static String _getEnglishCardName(String cardId) {
    final englishNames = {
      'fool': 'The Fool',
      'magician': 'The Magician',
      'high_priestess': 'The High Priestess',
      'empress': 'The Empress',
      'emperor': 'The Emperor',
      'hierophant': 'The Hierophant',
      'lovers': 'The Lovers',
      'chariot': 'The Chariot',
      'strength': 'Strength',
      'hermit': 'The Hermit',
      'wheel_of_fortune': 'Wheel of Fortune',
      'justice': 'Justice',
      'hanged_man': 'The Hanged Man',
      'death': 'Death',
      'temperance': 'Temperance',
      'devil': 'The Devil',
      'tower': 'The Tower',
      'star': 'The Star',
      'moon': 'The Moon',
      'sun': 'The Sun',
      'judgement': 'Judgement',
      'world': 'The World',
    };
    return englishNames[cardId] ?? cardId;
  }

  /// 获取英文卡牌描述
  static String _getEnglishCardDescription(String cardId) {
    final englishDescriptions = {
      'fool': 'New beginnings, innocence, spontaneity',
      'magician': 'Manifestation, resourcefulness, power',
      'high_priestess': 'Intuition, sacred knowledge, divine feminine',
      'empress': 'Femininity, beauty, nature, abundance',
      'emperor': 'Authority, establishment, structure, father figure',
      'hierophant': 'Spiritual wisdom, religious beliefs, conformity',
      'lovers': 'Love, harmony, relationships, values alignment',
      'chariot': 'Control, willpower, success, determination',
      'strength': 'Strength, courage, persuasion, influence',
      'hermit': 'Soul searching, introspection, inner guidance',
      'wheel_of_fortune': 'Good luck, karma, life cycles, destiny',
      'justice': 'Justice, fairness, truth, cause and effect',
      'hanged_man': 'Suspension, restriction, letting go',
      'death': 'Endings, beginnings, change, transformation',
      'temperance': 'Balance, moderation, patience, purpose',
      'devil': 'Bondage, addiction, sexuality, materialism',
      'tower': 'Sudden change, upheaval, chaos, revelation',
      'star': 'Hope, faith, purpose, renewal, spirituality',
      'moon': 'Illusion, fear, anxiety, subconscious, intuition',
      'sun': 'Positivity, fun, warmth, success, vitality',
      'judgement': 'Judgement, rebirth, inner calling, absolution',
      'world': 'Completion, accomplishment, travel, fulfillment',
    };
    return englishDescriptions[cardId] ?? 'Tarot card meaning';
  }

  /// 获取英文卡牌含义
  static String _getEnglishCardMeaning(String cardId, bool isReversed) {
    // 简化处理，返回基本含义
    return _getEnglishCardDescription(cardId);
  }

  /// 获取英文卡牌关键词
  static List<String> _getEnglishCardKeywords(String cardId) {
    final englishKeywords = {
      'fool': ['new beginnings', 'innocence', 'spontaneity'],
      'magician': ['manifestation', 'resourcefulness', 'power'],
      'high_priestess': ['intuition', 'sacred knowledge', 'divine feminine'],
      'empress': ['femininity', 'beauty', 'nature'],
      'emperor': ['authority', 'structure', 'father figure'],
      'hierophant': ['spiritual wisdom', 'tradition', 'conformity'],
      'lovers': ['love', 'harmony', 'relationships'],
      'chariot': ['control', 'willpower', 'success'],
      'strength': ['strength', 'courage', 'influence'],
      'hermit': ['soul searching', 'introspection', 'inner guidance'],
      'wheel_of_fortune': ['good luck', 'karma', 'destiny'],
      'justice': ['justice', 'fairness', 'truth'],
      'hanged_man': ['suspension', 'letting go', 'sacrifice'],
      'death': ['transformation', 'endings', 'new beginnings'],
      'temperance': ['balance', 'moderation', 'patience'],
      'devil': ['bondage', 'addiction', 'materialism'],
      'tower': ['sudden change', 'upheaval', 'revelation'],
      'star': ['hope', 'faith', 'renewal'],
      'moon': ['illusion', 'intuition', 'subconscious'],
      'sun': ['positivity', 'success', 'vitality'],
      'judgement': ['rebirth', 'inner calling', 'absolution'],
      'world': ['completion', 'accomplishment', 'fulfillment'],
    };
    return englishKeywords[cardId] ?? ['tarot', 'card', 'meaning'];
  }

  /// 清理文本中的Markdown格式符号和多语言问题
  static String _cleanMarkdownText(String text) {
    // 移除 ** 粗体标记 - 改进版正则表达式
    String cleaned = text.replaceAll(RegExp(r'\*\*(.*?)\*\*'), r'$1');

    // 移除单独的 ** 符号（处理不成对的情况）
    cleaned = cleaned.replaceAll('**', '');

    // 移除其他可能的Markdown符号
    cleaned = cleaned.replaceAll(RegExp(r'\*(.*?)\*'), r'$1'); // 斜体
    cleaned = cleaned.replaceAll(RegExp(r'__(.*?)__'), r'$1'); // 下划线粗体
    cleaned = cleaned.replaceAll(RegExp(r'_(.*?)_'), r'$1'); // 下划线斜体

    // 移除卡牌信息部分（临时解决方案）
    cleaned = cleaned.replaceAll(RegExp(r'The tarot cards you drew are:.*?\n\n', dotAll: true), '');
    cleaned = cleaned.replaceAll(RegExp(r'您抽取的塔罗牌是：.*?\n\n', dotAll: true), '');
    cleaned = cleaned.replaceAll(RegExp(r'抽取的卡牌：.*?\n\n', dotAll: true), '');

    // 移除具体的卡牌列表（格式：1. The Fool...）
    cleaned = cleaned.replaceAll(RegExp(r'\d+\.\s+.*?（.*?）.*?-.*?\n', multiLine: true), '');
    cleaned = cleaned.replaceAll(RegExp(r'\d+\.\s+.*?\(.*?\).*?-.*?\n', multiLine: true), '');

    // 清理多余的空行
    cleaned = cleaned.replaceAll(RegExp(r'\n\s*\n\s*\n'), '\n\n');
    cleaned = cleaned.replaceAll(RegExp(r'^\s*\n+'), ''); // 移除开头的空行

    return cleaned.trim();
  }

  /// 获取初始塔罗解读
  static Future<String> getInitialReading({
    required String question,
    required List<TarotCard> cards,
    String? userLanguage,
  }) async {
    // 默认英文，支持多语言
    final language = userLanguage ?? _getCurrentAppLanguage();
    try {
      // 检查网络连接
      print('🌐 检查网络连接状态...');
      await _checkNetworkConnectivity();
      // 使用现有的Edge Function格式，传递多语言卡牌信息
      final requestData = {
        'question': question,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': _getCardNameForLanguage(card, language),
          'description': _getCardDescriptionForLanguage(card, language),
          'meaning': _getCardMeaningForLanguage(card, language),
          'keywords': _getCardKeywordsForLanguage(card, language),
          'isMajorArcana': card.isMajorArcana,
          'isReversed': card.isReversed,
        }).toList(),
        'spreadType': 'simple_reading',
        'requestType': 'initial_reading', // 使用现有的类型
        'userLanguage': language, // 使用处理后的语言
      };

      print('🔮 开始AI塔罗解读');
      print('📋 问题: $question');
      print('📋 卡牌: ${cards.map((c) => c.name).join(', ')}');
      print('📋 请求数据: $requestData');

      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      ).timeout(
        const Duration(seconds: 60), // 60秒超时
        onTimeout: () {
          print('⏰ Edge Function 调用超时');
          throw Exception('AI解读请求超时，请稍后重试');
        },
      ).catchError((error) {
        print('🚨 网络请求错误详情: $error');
        print('🚨 错误类型: ${error.runtimeType}');
        if (error.toString().contains('SocketException')) {
          throw Exception('网络连接失败，请检查网络设置后重试');
        } else if (error.toString().contains('HandshakeException')) {
          throw Exception('SSL连接失败，请检查网络安全设置');
        } else if (error.toString().contains('TimeoutException')) {
          throw Exception('请求超时，请稍后重试');
        }
        throw Exception('网络请求失败: ${error.toString()}');
      });

      print('📨 响应状态: ${response.status}');
      print('📨 响应数据: ${response.data}');

      if (response.data != null && response.data['success'] == true) {
        final reading = response.data['reading'] as String;
        final cleanedReading = _cleanMarkdownText(reading);
        print('✅ AI解读成功，长度: ${cleanedReading.length}字');
        print('✅ AI解读内容预览: ${cleanedReading.length > 100 ? cleanedReading.substring(0, 100) + "..." : cleanedReading}');
        return cleanedReading;
      } else {
        print('❌ AI解读失败: ${response.data?['error']}');
        print('❌ 完整响应: ${response.data}');
        print('❌ 响应状态码: ${response.status}');

        // 检查是否有fallback内容
        if (response.data != null && response.data['reading'] != null) {
          final fallbackReading = response.data['reading'] as String;
          print('🔄 使用Edge Function提供的fallback解读: $fallbackReading');
          return _cleanMarkdownText(fallbackReading);
        }

        print('🔄 使用本地fallback解读');
        return _cleanMarkdownText(_getFallbackReading(question, cards, language));
      }
    } catch (e) {
      print('❌ AI解读异常: $e');
      return _getFallbackReading(question, cards, language);
    }
  }

  /// 获取追问解读 - 分析用户心理问题并开解
  static Future<String> getFollowUpReading({
    required String userMessage,
    required String previousReading,
    required List<TarotCard> cards,
    String? userLanguage,
    List<String>? conversationHistory,
  }) async {
    final language = userLanguage ?? _getCurrentAppLanguage();
    try {
      final requestData = {
        'question': userMessage,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isMajorArcana': card.isMajorArcana,
          'isReversed': card.isReversed,
        }).toList(),
        'spreadType': 'follow_up',
        'requestType': 'follow_up', // 使用现有的类型
        'userLanguage': language,
        'previousReading': previousReading,
        'userMessage': userMessage,
        'conversationHistory': conversationHistory ?? [],
      };

      print('🔮 开始追问解读');
      print('📋 用户追问: $userMessage');

      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      if (response.data != null && response.data['success'] == true) {
        final reading = response.data['reading'] as String;
        final cleanedReading = _cleanMarkdownText(reading);
        print('✅ 追问解读成功');
        return cleanedReading;
      } else {
        print('❌ 追问解读失败: ${response.data?['error']}');
        return _cleanMarkdownText(_getFollowUpFallback(userMessage, language));
      }
    } catch (e) {
      print('❌ 追问解读异常: $e');
      return _cleanMarkdownText(_getFollowUpFallback(userMessage, language));
    }
  }

  /// 流式解读 - 逐字显示效果
  static Stream<String> getStreamingReading({
    required String question,
    required List<TarotCard> cards,
    String? userLanguage,
  }) async* {
    final language = userLanguage ?? _getCurrentAppLanguage();
    try {
      final reading = await getInitialReading(
        question: question,
        cards: cards,
        userLanguage: language,
      );

      // 解读结果已经在getInitialReading中清理过了，直接使用
      // 模拟打字效果
      final words = reading.split('');
      String currentText = '';

      for (int i = 0; i < words.length; i++) {
        currentText += words[i];
        yield currentText;
        await Future.delayed(const Duration(milliseconds: 30));
      }
    } catch (e) {
      yield _cleanMarkdownText(_getFallbackReading(question, cards, language));
    }
  }

  /// 简单的fallback解读 - 提供有意义的基础解读
  static String _getFallbackReading(String question, List<TarotCard> cards, String userLanguage) {
    final cardNames = cards.map((c) => c.name).join('、');
    final mainCard = cards.isNotEmpty ? cards.first : null;

    if (userLanguage == 'zh') {
      String cardInsight = '';
      if (mainCard != null) {
        // 根据卡牌提供基础解读
        if (mainCard.name.contains('愚者')) {
          cardInsight = '愚者代表新的开始和无限可能，提醒您保持初心和勇气。';
        } else if (mainCard.name.contains('魔术师')) {
          cardInsight = '魔术师象征着创造力和行动力，现在是实现想法的好时机。';
        } else if (mainCard.name.contains('女祭司')) {
          cardInsight = '女祭司代表直觉和内在智慧，建议您倾听内心的声音。';
        } else if (mainCard.name.contains('皇帝')) {
          cardInsight = '皇帝象征权威和稳定，提醒您要有条理地处理事务。';
        } else if (mainCard.name.contains('恋人')) {
          cardInsight = '恋人牌代表选择和关系，现在需要做出重要决定。';
        } else {
          cardInsight = '这张牌提醒您要相信自己的直觉，保持开放的心态。';
        }
      }

      return '''亲爱的，从$cardNames中我感受到你正处在一个重要的转折点呢。

你的问题反映了内心对方向的渴望，这是很好的自我探索机会哦。

相信自己的内在智慧，它会指引你找到正确的方向。每个挑战都是成长的机会！✨''';
    } else {
      return '''Honey, from the cards $cardNames, I sense you're at an important crossroads right now.

Your question reflects your inner desire for direction, which is a wonderful opportunity for self-exploration.

Trust your inner wisdom to guide you to the right path. Every challenge is a chance to grow! ✨''';
    }
  }

  /// 追问的fallback回复
  static String _getFollowUpFallback(String userMessage, String userLanguage) {
    if (userLanguage == 'zh') {
      return '''我理解您的关切。从您的问题中，我感受到您内心的困惑和寻求答案的渴望。

让我们一起深入探索：您提到的这个问题，背后可能反映了什么样的内在需求？有时候，我们表面的困扰往往指向更深层的心理需要。

请告诉我，当您面对这个情况时，最让您感到不安的是什么？是对未知的恐惧，还是对失去控制的担忧？

记住，每个挑战都是成长的机会。✨''';
    } else {
      return '''I understand your concern. From your question, I sense the confusion in your heart and your desire for answers.

Let's explore together: What deeper inner needs might this question reflect? Sometimes our surface troubles point to deeper psychological needs.

Please tell me, when facing this situation, what makes you most uneasy? Is it fear of the unknown, or worry about losing control?

Remember, every challenge is an opportunity for growth. ✨''';
    }
  }

  /// 检查网络连接
  static Future<void> _checkNetworkConnectivity() async {
    try {
      print('🌐 正在检查网络连接...');
      final result = await InternetAddress.lookup('supabase.co');
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        print('✅ 网络连接正常');
      }
    } on SocketException catch (e) {
      print('❌ 网络连接失败: $e');
      throw Exception('网络连接失败，请检查网络设置');
    } catch (e) {
      print('❌ 网络检查异常: $e');
      // 不抛出异常，允许继续尝试请求
    }
  }
}
