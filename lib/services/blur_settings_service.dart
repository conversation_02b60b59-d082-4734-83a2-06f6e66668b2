import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:ui' as ui;

class BlurSettingsService extends ChangeNotifier {
  static const String _blurKey = 'glass_blur_intensity';
  static const double _defaultBlur = 15.0;
  
  double _blurIntensity = _defaultBlur;
  
  double get blurIntensity => _blurIntensity;
  
  BlurSettingsService() {
    _loadBlurSettings();
  }
  
  // 加载保存的模糊度设置
  Future<void> _loadBlurSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _blurIntensity = prefs.getDouble(_blurKey) ?? _defaultBlur;
      notifyListeners();
    } catch (e) {
      print('加载模糊度设置失败: $e');
    }
  }
  
  // 设置模糊度
  Future<void> setBlurIntensity(double intensity) async {
    if (_blurIntensity == intensity) return;
    
    _blurIntensity = intensity;
    notifyListeners();
    
    // 保存到本地存储
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_blurKey, intensity);
    } catch (e) {
      print('保存模糊度设置失败: $e');
    }
  }
  
  // 获取ImageFilter
  ui.ImageFilter getImageFilter() {
    return ui.ImageFilter.blur(sigmaX: _blurIntensity, sigmaY: _blurIntensity);
  }
  
  // 重置为默认值
  Future<void> resetToDefault() async {
    await setBlurIntensity(_defaultBlur);
  }
} 