import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CardPreferenceService extends ChangeNotifier {
  static const String _cardBackKey = 'selected_card_back';
  
  // 默认卡牌图片 - 经典版使用IMG_0992 42.png
  static const String _defaultCardBack = 'IMG_0992 42.png';
  
  String _selectedCardBack = _defaultCardBack;
  
  String get selectedCardBack => _selectedCardBack;
  
  // 可选择的卡牌背面图片
  static const List<Map<String, String>> availableCardBacks = [
    {'name': '经典版', 'key': 'classic_style', 'image': 'IMG_0992 42.png'},
    {'name': '神秘版', 'key': 'mysterious_style', 'image': '1.png'},
    {'name': '优雅版', 'key': 'elegant_style', 'image': '2.png'},
    {'name': '现代版', 'key': 'modern_style', 'image': '3.png'},
    {'name': '华丽版', 'key': 'luxury_style', 'image': '4.png'},
  ];
  
  CardPreferenceService() {
    _loadSettings();
  }
  
  // 加载保存的设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _selectedCardBack = prefs.getString(_cardBackKey) ?? _defaultCardBack;
      print('🎴 加载卡牌设置: $_selectedCardBack');
      notifyListeners();
    } catch (e) {
      print('❌ 加载卡牌设置失败: $e');
    }
  }
  
  // 设置卡牌背面
  Future<void> setCardBack(String imagePath) async {
    try {
      _selectedCardBack = imagePath;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cardBackKey, imagePath);
      print('✅ 保存卡牌设置: $imagePath');
      notifyListeners();
    } catch (e) {
      print('❌ 保存卡牌设置失败: $e');
    }
  }
  
  // 重置为默认设置
  Future<void> resetToDefault() async {
    try {
      _selectedCardBack = _defaultCardBack;
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_cardBackKey, _defaultCardBack);
      print('🔄 重置为默认卡牌: $_defaultCardBack');
      notifyListeners();
    } catch (e) {
      print('❌ 重置卡牌设置失败: $e');
    }
  }
  
  // 获取当前选择的卡牌名称
  String get selectedCardName {
    for (var card in availableCardBacks) {
      if (card['image'] == _selectedCardBack) {
        return card['name'] ?? '未知';
      }
    }
    return '经典版';
  }
} 