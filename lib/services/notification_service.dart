import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz;
import 'package:ai_tarot_reading/services/affirmation_service.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

/// 推送通知服务
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  bool _isInitialized = false;

  /// 初始化通知服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    // 初始化时区
    tz.initializeTimeZones();

    // Android 初始化设置
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    // iOS 初始化设置
    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await _flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
    print('✅ 通知服务初始化完成');
  }

  /// 处理通知点击事件
  void _onNotificationTapped(NotificationResponse notificationResponse) {
    final payload = notificationResponse.payload;
    print('📱 通知被点击: $payload');
    
    // 这里可以根据 payload 导航到相应页面
    // 例如：导航到每日塔罗页面、显化目标页面等
  }

  /// 请求通知权限
  Future<bool> requestPermissions() async {
    if (Platform.isIOS) {
      final bool? result = await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(
            alert: true,
            badge: true,
            sound: true,
          );
      return result ?? false;
    } else if (Platform.isAndroid) {
      final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
          _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>();

      final bool? result = await androidImplementation?.requestNotificationsPermission();
      return result ?? false;
    }
    return false;
  }

  /// 调度每日肯定语推送
  Future<void> scheduleDailyAffirmationNotification({
    required int hour,
    required int minute,
    ManifestationGoal? manifestationGoal,
    String? languageCode,
  }) async {
    await _cancelDailyAffirmationNotification();

    final goal = manifestationGoal ?? ManifestationGoal.wealth; // 默认财富
    final language = languageCode ?? 'zh-CN'; // 默认中文

    // 获取本地化的肯定语
    final affirmation = AffirmationService.getLocalizedAffirmation(goal, language);
    final title = _getLocalizedTitle(language);

    // 设置每日推送时间
    final now = DateTime.now();
    var scheduledDate = DateTime(now.year, now.month, now.day, hour, minute);
    
    // 如果今天的时间已过，则安排到明天
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'daily_affirmation',
      '每日肯定语',
      channelDescription: '每日推送正能量肯定语',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails(
      categoryIdentifier: 'daily_affirmation',
    );

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.zonedSchedule(
      0, // 通知ID
      title,
      affirmation,
      tz.TZDateTime.from(scheduledDate, tz.local),
      platformChannelSpecifics,
      androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time, // 每天重复
      payload: 'daily_affirmation_${goal.name}',
    );

    // 保存推送设置
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('daily_notification_enabled', true);
    await prefs.setInt('notification_hour', hour);
    await prefs.setInt('notification_minute', minute);
    await prefs.setString('notification_goal', goal.name);

    print('✅ 每日肯定语推送已设置: $hour:$minute, 目标: ${goal.displayName}');
  }

  /// 取消每日肯定语推送
  Future<void> _cancelDailyAffirmationNotification() async {
    await _flutterLocalNotificationsPlugin.cancel(0);
  }

  /// 禁用每日推送
  Future<void> disableDailyNotification() async {
    await _cancelDailyAffirmationNotification();
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('daily_notification_enabled', false);
    
    print('❌ 每日肯定语推送已禁用');
  }

  /// 发送即时肯定语通知
  Future<void> sendInstantAffirmationNotification({
    required ManifestationGoal goal,
    String? languageCode,
  }) async {
    final language = languageCode ?? 'zh-CN';
    final affirmation = AffirmationService.getLocalizedAffirmation(goal, language);
    final title = _getLocalizedInstantTitle(language);

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'instant_affirmation',
      '即时肯定语',
      channelDescription: '即时推送肯定语',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      1, // 不同的通知ID
      title,
      affirmation,
      platformChannelSpecifics,
      payload: 'instant_affirmation_${goal.name}',
    );
  }

  /// 发送每日塔罗提醒
  Future<void> sendDailyTarotReminder({String? languageCode}) async {
    final language = languageCode ?? 'zh-CN';
    final title = _getLocalizedTarotReminderTitle(language);
    final body = _getLocalizedTarotReminderBody(language);

    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'daily_tarot_reminder',
      '每日塔罗提醒',
      channelDescription: '提醒用户抽取每日塔罗牌',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@mipmap/ic_launcher',
    );

    const DarwinNotificationDetails iOSPlatformChannelSpecifics =
        DarwinNotificationDetails();

    const NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics,
    );

    await _flutterLocalNotificationsPlugin.show(
      2, // 不同的通知ID
      title,
      body,
      platformChannelSpecifics,
      payload: 'daily_tarot_reminder',
    );
  }

  /// 获取本地化标题
  String _getLocalizedTitle(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '✨ 今日肯定语';
      case 'zh-TW':
        return '✨ 今日肯定語';
      case 'en-US':
        return '✨ Daily Affirmation';
      case 'es-ES':
        return '✨ Afirmación Diaria';
      case 'ja-JP':
        return '✨ 今日のアファメーション';
      case 'ko-KR':
        return '✨ 오늘의 확언';
      default:
        return '✨ 今日肯定语';
    }
  }

  /// 获取即时通知标题
  String _getLocalizedInstantTitle(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '💫 能量补充';
      case 'zh-TW':
        return '💫 能量補充';
      case 'en-US':
        return '💫 Energy Boost';
      case 'es-ES':
        return '💫 Impulso de Energía';
      case 'ja-JP':
        return '💫 エネルギーチャージ';
      case 'ko-KR':
        return '💫 에너지 충전';
      default:
        return '💫 能量补充';
    }
  }

  /// 获取塔罗提醒标题
  String _getLocalizedTarotReminderTitle(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '🔮 每日塔罗';
      case 'zh-TW':
        return '🔮 每日塔羅';
      case 'en-US':
        return '🔮 Daily Tarot';
      case 'es-ES':
        return '🔮 Tarot Diario';
      case 'ja-JP':
        return '🔮 デイリータロット';
      case 'ko-KR':
        return '🔮 데일리 타로';
      default:
        return '🔮 每日塔罗';
    }
  }

  /// 获取塔罗提醒内容
  String _getLocalizedTarotReminderBody(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '今天还没有抽取塔罗牌哦，来看看宇宙为你准备了什么指引吧！';
      case 'zh-TW':
        return '今天還沒有抽取塔羅牌哦，來看看宇宙為你準備了什麼指引吧！';
      case 'en-US':
        return "You haven't drawn your daily tarot card yet. See what guidance the universe has for you!";
      case 'es-ES':
        return '¡Aún no has sacado tu carta de tarot diaria! ¡Mira qué orientación tiene el universo para ti!';
      case 'ja-JP':
        return 'まだ今日のタロットカードを引いていませんね。宇宙があなたに用意したガイダンスを見てみましょう！';
      case 'ko-KR':
        return '아직 오늘의 타로 카드를 뽑지 않았네요. 우주가 당신을 위해 준비한 안내를 확인해보세요!';
      default:
        return '今天还没有抽取塔罗牌哦，来看看宇宙为你准备了什么指引吧！';
    }
  }

  /// 获取当前推送设置
  Future<Map<String, dynamic>> getCurrentSettings() async {
    final prefs = await SharedPreferences.getInstance();
    return {
      'enabled': prefs.getBool('daily_notification_enabled') ?? false,
      'hour': prefs.getInt('notification_hour') ?? 9,
      'minute': prefs.getInt('notification_minute') ?? 0,
      'goal': prefs.getString('notification_goal') ?? ManifestationGoal.wealth.name,
    };
  }
}
