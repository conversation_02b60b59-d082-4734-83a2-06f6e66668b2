import 'dart:convert';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/services/langfuse_service.dart';

class DeepSeekService {
  static final _supabase = Supabase.instance.client;
  
  // 不再需要直接配置API Key，通过Supabase Edge Function调用

  /// 通过Supabase Edge Function调用DeepSeek API并追踪到Langfuse
  static Future<Map<String, dynamic>> generateResponse({
    required String prompt,
    required String traceId,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async {
    final startTime = DateTime.now();
    
    try {
      // 1. 构建请求体，发送到Supabase Edge Function
      final requestBody = {
        'prompt': prompt,
        'model': 'deepseek-chat',
        'temperature': temperature,
        'max_tokens': maxTokens,
        'stream': false,
        'trace_id': traceId,
        'metadata': metadata ?? {},
        'request_type': 'deepseek_completion',
      };

      // 2. 调用Supabase Edge Function
      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading', // 使用现有的Edge Function
        body: requestBody,
      );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      if (response.data != null && response.data['success'] == true) {
        final content = response.data['response'] ?? response.data['content'];
        final usage = response.data['usage'];

        // 3. 记录到Langfuse（Edge Function内部也会记录，这里是客户端补充记录）
        await LangfuseService.createGeneration(
          traceId: traceId,
          name: 'deepseek_client_completion',
          input: {'prompt': prompt, 'model_params': requestBody},
          output: {'response': content},
          model: 'deepseek-chat',
          promptTokens: usage?['prompt_tokens'],
          completionTokens: usage?['completion_tokens'],
          metadata: {
            ...metadata ?? {},
            'duration_ms': duration.inMilliseconds,
            'temperature': temperature,
            'max_tokens': maxTokens,
            'api_provider': 'deepseek_via_supabase',
          },
        );

        return {
          'success': true,
          'content': content,
          'usage': usage,
          'duration_ms': duration.inMilliseconds,
        };
      } else {
        // Edge Function调用失败，记录错误
        final errorMsg = 'DeepSeek Edge Function错误: ${response.data}';
        await LangfuseService.createEvent(
          traceId: traceId,
          name: 'deepseek_edge_function_error',
          input: {'prompt': prompt},
          output: {'error': errorMsg},
          metadata: {
            'duration_ms': duration.inMilliseconds,
          },
        );

        return {
          'success': false,
          'error': errorMsg,
        };
      }
    } catch (e) {
      // 网络或其他错误
      final errorMsg = 'DeepSeek调用异常: $e';
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'deepseek_call_exception',
        input: {'prompt': prompt},
        output: {'error': errorMsg},
        metadata: {
          'exception': e.toString(),
        },
      );

      return {
        'success': false,
        'error': errorMsg,
      };
    }
  }

  /// 流式调用DeepSeek API
  static Stream<String> generateResponseStream({
    required String prompt,
    required String traceId,
    Map<String, dynamic>? metadata,
    double temperature = 0.7,
    int maxTokens = 1000,
  }) async* {
    final startTime = DateTime.now();
    
    try {
      final requestBody = {
        'model': 'deepseek-chat',
        'messages': [
          {
            'role': 'system',
            'content': '你是一位专业的塔罗解读师和心理引导师，擅长通过塔罗牌为用户提供深度的心理分析和人生指导。请用简洁而有深度的语言回复，控制在60字以内。'
          },
          {
            'role': 'user',
            'content': prompt,
          }
        ],
        'temperature': temperature,
        'max_tokens': maxTokens,
        'stream': true,
      };

      final request = http.Request('POST', Uri.parse('$_baseUrl/chat/completions'));
      request.headers.addAll(_headers);
      request.body = jsonEncode(requestBody);

      final streamedResponse = await request.send();
      final fullResponse = StringBuffer();

      if (streamedResponse.statusCode == 200) {
        await for (final chunk in streamedResponse.stream.transform(utf8.decoder)) {
          final lines = chunk.split('\n');
          for (final line in lines) {
            if (line.startsWith('data: ')) {
              final data = line.substring(6);
              if (data == '[DONE]') continue;
              
              try {
                final json = jsonDecode(data);
                final content = json['choices'][0]['delta']['content'];
                if (content != null) {
                  fullResponse.write(content);
                  yield content;
                }
              } catch (e) {
                // 忽略解析错误
              }
            }
          }
        }

        // 记录完整响应到Langfuse
        final endTime = DateTime.now();
        await LangfuseService.createGeneration(
          traceId: traceId,
          name: 'deepseek_stream_completion',
          input: {'prompt': prompt, 'model_params': requestBody},
          output: {'response': fullResponse.toString()},
          model: 'deepseek-chat',
          metadata: {
            ...metadata ?? {},
            'duration_ms': endTime.difference(startTime).inMilliseconds,
            'temperature': temperature,
            'max_tokens': maxTokens,
            'api_provider': 'deepseek',
            'stream': true,
          },
        );
      }
    } catch (e) {
      await LangfuseService.createEvent(
        traceId: traceId,
        name: 'deepseek_stream_error',
        input: {'prompt': prompt},
        output: {'error': e.toString()},
      );
      
      // 返回错误提示
      yield '抱歉，AI服务暂时不可用，请稍后重试。';
    }
  }

  /// 验证API密钥是否有效
  static Future<bool> validateApiKey() async {
    try {
      final response = await http.get(
        Uri.parse('$_baseUrl/models'),
        headers: _headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
} 