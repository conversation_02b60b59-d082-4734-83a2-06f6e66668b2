import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data_en.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'dart:math';

/// 塔罗牌数据服务 - 根据语言动态提供数据
class TarotDataService {
  static TarotDataService? _instance;
  static TarotDataService get instance => _instance ??= TarotDataService._();
  
  TarotDataService._();

  /// 根据语言获取所有塔罗牌
  List<TarotCard> getAllCards(String languageCode) {
    // 统一使用中文数据源，因为它有完整的翻译键配置
    // 实际的语言翻译通过 LanguageManager 处理
    final cards = TarotCardsData.allCards;

    // 修复图片路径
    return cards.map((card) => card.copyWith(
      imageUrl: card.getCorrectImageUrl(),
    )).toList();
  }

  /// 根据语言获取随机卡牌（带正逆位）
  List<TarotCard> getRandomCards(String languageCode, int count) {
    final random = Random();
    final allCards = getAllCards(languageCode);
    final shuffled = List<TarotCard>.from(allCards)..shuffle(random);

    // 为每张牌随机分配正逆位
    return shuffled.take(count).map((card) {
      final isReversed = random.nextBool(); // 50%概率逆位
      return card.copyWith(isReversed: isReversed);
    }).toList();
  }

  /// 根据语言获取单张随机卡牌（带正逆位）
  TarotCard getRandomCardWithOrientation(String languageCode) {
    final random = Random();
    final allCards = getAllCards(languageCode);
    final card = allCards[random.nextInt(allCards.length)];
    final isReversed = random.nextBool(); // 50%概率逆位
    return card.copyWith(isReversed: isReversed);
  }

  /// 根据语言获取完整含义（正逆位）
  String getFullMeaning(TarotCard card, String languageCode, {LanguageManager? languageManager}) {
    if (languageManager != null) {
      // 使用TarotCard的getTranslatedMeaning方法
      return card.getTranslatedMeaning(languageManager.translate);
    } else {
      // 如果没有提供languageManager，返回原始含义
      return card.meaning;
    }
  }

  /// 根据语言获取完整关键词（正逆位）
  List<String> getFullKeywords(TarotCard card, String languageCode, {LanguageManager? languageManager}) {
    if (languageManager != null) {
      // 使用TarotCard的getTranslatedKeywords方法
      return card.getTranslatedKeywords(languageManager.translate);
    } else {
      // 如果没有提供languageManager，返回原始关键词
      return card.keywords;
    }
  }

  /// 使用语言管理器翻译关键词
  List<String> getTranslatedKeywords(TarotCard card, LanguageManager languageManager) {
    // 使用TarotCard的getTranslatedKeywords方法
    return card.getTranslatedKeywords(languageManager.translate);
  }

  /// 根据语言和ID获取卡牌
  TarotCard? getCardById(String id, String languageCode) {
    try {
      final allCards = getAllCards(languageCode);
      // 检查是否是正逆位格式的ID（如：card_1_reversed 或 card_1_upright）
      if (id.contains('_reversed') || id.contains('_upright')) {
        final isReversed = id.contains('_reversed');
        final baseId = id.replaceAll('_reversed', '').replaceAll('_upright', '');
        final baseCard = allCards.firstWhere((card) => card.id == baseId);
        return baseCard.copyWith(isReversed: isReversed);
      } else {
        // 普通ID格式
        return allCards.firstWhere((card) => card.id == id);
      }
    } catch (e) {
      return null;
    }
  }

  /// 根据语言获取逆位含义
  String getReversedMeaning(TarotCard card, String languageCode) {
    switch (languageCode) {
      case 'en':
        return TarotCardsDataEn.getReversedMeaning(card);
      case 'zh':
      default:
        return TarotCardsData.getReversedMeaning(card);
    }
  }

  /// 将中文卡牌转换为英文（用于语言切换时的数据迁移）
  TarotCard? convertToLanguage(TarotCard card, String targetLanguageCode) {
    if (targetLanguageCode == 'en') {
      // 根据ID查找对应的英文卡牌
      return TarotCardsDataEn.getCardById(card.id)?.copyWith(
        isReversed: card.isReversed,
      );
    } else {
      // 根据ID查找对应的中文卡牌
      return TarotCardsData.getCardById(card.id)?.copyWith(
        isReversed: card.isReversed,
      );
    }
  }

  /// 批量转换语言
  List<TarotCard> convertCardsToLanguage(List<TarotCard> cards, String targetLanguageCode) {
    return cards.map((card) {
      final convertedCard = convertToLanguage(card, targetLanguageCode);
      return convertedCard ?? card; // 如果转换失败，保留原卡牌
    }).toList();
  }
} 