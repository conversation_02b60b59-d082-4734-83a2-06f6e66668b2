import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

enum AppBackgroundType {
  default_mascot,  // 默认tarot_mascot.png
  custom          // 用户自定义图片
}

class AppBackgroundService extends ChangeNotifier {
  static const String _backgroundTypeKey = 'app_background_type';
  static const String _customBackgroundPathKey = 'app_custom_background_path';
  
  AppBackgroundType _currentType = AppBackgroundType.default_mascot;
  String? _customBackgroundPath;
  
  AppBackgroundType get currentType => _currentType;
  String? get customBackgroundPath => _customBackgroundPath;
  
  // 获取当前APP背景路径
  String get currentBackgroundPath {
    switch (_currentType) {
      case AppBackgroundType.default_mascot:
        return 'assets/images/tarot_mascot.png';
      case AppBackgroundType.custom:
        return _customBackgroundPath ?? 'assets/images/tarot_mascot.png';
    }
  }
  
  // 是否使用自定义APP背景
  bool get isUsingCustomBackground => _currentType == AppBackgroundType.custom && _customBackgroundPath != null;
  
  AppBackgroundService() {
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 加载背景类型
      final typeString = prefs.getString(_backgroundTypeKey);
      if (typeString != null) {
        _currentType = AppBackgroundType.values.firstWhere(
          (e) => e.name == typeString,
          orElse: () => AppBackgroundType.default_mascot,
        );
      }
      
      // 加载自定义背景路径
      _customBackgroundPath = prefs.getString(_customBackgroundPathKey);
      
      // 验证自定义背景文件是否存在
      if (_customBackgroundPath != null && !await File(_customBackgroundPath!).exists()) {
        _customBackgroundPath = null;
        _currentType = AppBackgroundType.default_mascot;
        await _saveSettings();
      }
      
      notifyListeners();
    } catch (e) {
      print('❌ APP背景设置加载失败: $e');
    }
  }
  
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backgroundTypeKey, _currentType.name);
      
      if (_customBackgroundPath != null) {
        await prefs.setString(_customBackgroundPathKey, _customBackgroundPath!);
      } else {
        await prefs.remove(_customBackgroundPathKey);
      }
      
      print('✅ APP背景设置保存成功: type=${_currentType.name}, path=$_customBackgroundPath');
    } catch (e) {
      print('❌ APP背景设置保存失败: $e');
    }
  }
  
  // 设置为默认APP背景 (tarot_mascot.png)
  Future<void> setDefaultBackground() async {
    _currentType = AppBackgroundType.default_mascot;
    await _saveSettings();
    notifyListeners();
    print('✅ 已恢复默认APP背景: tarot_mascot.png');
  }
  
  // 设置自定义APP背景
  Future<void> setCustomBackground(String imagePath) async {
    try {
      // 验证文件是否存在
      if (await File(imagePath).exists()) {
        _currentType = AppBackgroundType.custom;
        _customBackgroundPath = imagePath;
        await _saveSettings();
        notifyListeners();
        print('✅ 自定义APP背景设置成功: $imagePath');
      } else {
        throw Exception('文件不存在: $imagePath');
      }
    } catch (e) {
      print('❌ 自定义APP背景设置失败: $e');
      rethrow;
    }
  }
  
  // 重置到默认设置
  Future<void> resetToDefault() async {
    _currentType = AppBackgroundType.default_mascot;
    _customBackgroundPath = null;
    await _saveSettings();
    notifyListeners();
  }
} 