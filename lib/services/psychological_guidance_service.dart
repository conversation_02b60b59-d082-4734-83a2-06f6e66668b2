import 'package:flutter/foundation.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';

/// 心理引导服务 - 多轮对话引擎
class PsychologicalGuidanceService {
  
  /// 问题类型识别
  static QuestionType analyzeQuestionType(String question) {
    final lowerQuestion = question.toLowerCase();
    
    // 关系类问题
    if (lowerQuestion.contains('复合') || lowerQuestion.contains('分手') || 
        lowerQuestion.contains('前任') || lowerQuestion.contains('挽回')) {
      return QuestionType.relationship;
    }
    
    // 工作类问题
    if (lowerQuestion.contains('跳槽') || lowerQuestion.contains('工作') || 
        lowerQuestion.contains('职业') || lowerQuestion.contains('升职')) {
      return QuestionType.career;
    }
    
    // 决策类问题
    if (lowerQuestion.contains('要不要') || lowerQuestion.contains('应该') || 
        lowerQuestion.contains('选择') || lowerQuestion.contains('决定')) {
      return QuestionType.decision;
    }
    
    // 情绪类问题
    if (lowerQuestion.contains('焦虑') || lowerQuestion.contains('抑郁') || 
        lowerQuestion.contains('痛苦') || lowerQuestion.contains('难受')) {
      return QuestionType.emotional;
    }
    
    return QuestionType.general;
  }

  /// 生成引导性提问
  static List<String> generateGuidingQuestions(
    QuestionType questionType, 
    TarotCard card, 
    String originalQuestion,
    int conversationRound,
  ) {
    switch (questionType) {
      case QuestionType.relationship:
        return _generateRelationshipQuestions(card, conversationRound);
      case QuestionType.career:
        return _generateCareerQuestions(card, conversationRound);
      case QuestionType.decision:
        return _generateDecisionQuestions(card, conversationRound);
      case QuestionType.emotional:
        return _generateEmotionalQuestions(card, conversationRound);
      default:
        return _generateGeneralQuestions(card, conversationRound);
    }
  }

  /// 关系类引导问题
  static List<String> _generateRelationshipQuestions(TarotCard card, int round) {
    switch (round) {
      case 1: // 第一轮：了解基本情况
        return [
          '这张${card.name}显示你们之间还有未解决的情感纠葛。你们分开多久了？',
          '从这张牌看，你对这段关系似乎还有很深的眷恋。是什么让你最难忘？',
          '${card.name}暗示着复杂的情感状态。你们分开的真正原因是什么？',
        ];
      case 2: // 第二轮：挖掘情感模式
        return [
          '听起来你很难放下这段关系。你觉得是什么让你这么难以割舍？',
          '这种感觉是不是在之前的关系中也出现过？',
          '当你想到彻底失去这个人时，内心最害怕的是什么？',
        ];
      case 3: // 第三轮：探索深层原因
        return [
          '这种"曾被温柔对待"的记忆，可能唤起了哪些你小时候的渴望呢？',
          '你觉得这段关系是否在某种程度上重现了你童年时期的某种体验？',
          '如果这个人代表了你内心缺失的某种品质，那会是什么？',
        ];
      case 4: // 第四轮：模式识别和疗愈
        return [
          '看起来你可能在复演一个旧的情感模式。你愿意尝试一些释放练习吗？',
          '这个模式已经让你痛苦了，是时候温柔地对那个受伤的内在小孩说话了。',
          '你准备好学习如何给自己那份曾经渴望的关爱了吗？',
        ];
      default:
        return ['让我们继续深入探索这个话题...'];
    }
  }

  /// 职业类引导问题
  static List<String> _generateCareerQuestions(TarotCard card, int round) {
    switch (round) {
      case 1:
        return [
          '这张${card.name}显示你在职场上可能遇到了一些挑战。具体是什么让你想要改变？',
          '从牌面看，你似乎对现状有些不满。是工作内容还是环境让你困扰？',
        ];
      case 2:
        return [
          '这种不满足感是最近才有的，还是已经持续一段时间了？',
          '你觉得是什么内在需求没有被满足？',
        ];
      case 3:
        return [
          '如果没有任何外在压力，你最想在职业上实现什么？',
          '这种对成就的渴望，是否与你对自我价值的认知有关？',
        ];
      default:
        return ['让我们继续探索你的职业内在动机...'];
    }
  }

  /// 决策类引导问题
  static List<String> _generateDecisionQuestions(TarotCard card, int round) {
    switch (round) {
      case 1:
        return [
          '这张${card.name}暗示这个决定对你很重要。当你想象选择A时，身体有什么感觉？',
          '从牌面看，你的内心可能已经有了倾向。是什么让你还在犹豫？',
        ];
      case 2:
        return [
          '这种犹豫是来自理性分析，还是内心的恐惧？',
          '你最担心做错决定会带来什么后果？',
        ];
      case 3:
        return [
          '如果你的直觉有声音，它会告诉你什么？',
          '这个决定是否触及了你对未来的某种深层恐惧或期待？',
        ];
      default:
        return ['让我们继续倾听你内在的智慧...'];
    }
  }

  /// 情绪类引导问题
  static List<String> _generateEmotionalQuestions(TarotCard card, int round) {
    switch (round) {
      case 1:
        return [
          '这张${card.name}反映了你内心的情绪状态。这种感觉是什么时候开始的？',
          '从牌面看，你可能正在经历一些内在的冲突。能描述一下这种感觉吗？',
        ];
      case 2:
        return [
          '这种情绪在你身体的哪个部位感受最强烈？',
          '有没有什么特定的情况会触发这种感觉？',
        ];
      case 3:
        return [
          '如果这种情绪有声音，它想对你说什么？',
          '你觉得这种情绪是在保护你免受什么伤害吗？',
        ];
      default:
        return ['让我们继续与这个情绪对话...'];
    }
  }

  /// 通用引导问题
  static List<String> _generateGeneralQuestions(TarotCard card, int round) {
    return [
      '这张${card.name}为你的问题带来了一些启示。你从中感受到了什么？',
      '牌面显示的能量与你当前的状态有什么共鸣？',
      '如果这张牌是宇宙给你的一个信息，你觉得它想告诉你什么？',
    ];
  }

  /// 识别心理模式
  static PsychologicalPattern identifyPattern(List<String> conversationHistory) {
    final fullText = conversationHistory.join(' ').toLowerCase();

    // 依恋模式
    if (fullText.contains('放不下') || fullText.contains('总是想起') ||
        fullText.contains('难以割舍') || fullText.contains('反复')) {
      return PsychologicalPattern.attachment;
    }

    // 讨好模式
    if (fullText.contains('害怕') && (fullText.contains('不高兴') || fullText.contains('生气')) ||
        fullText.contains('不敢说') || fullText.contains('委屈自己')) {
      return PsychologicalPattern.pleasing;
    }

    // 内耗模式
    if (fullText.contains('纠结') || fullText.contains('想太多') ||
        fullText.contains('自我攻击') || fullText.contains('内心冲突')) {
      return PsychologicalPattern.rumination;
    }

    // 恐惧模式
    if (fullText.contains('害怕') || fullText.contains('担心') ||
        fullText.contains('焦虑') || fullText.contains('不安')) {
      return PsychologicalPattern.fear;
    }

    // 阳光健康模式
    if (fullText.contains('还好') || fullText.contains('挺好的') ||
        fullText.contains('没什么问题') || fullText.contains('很开心')) {
      return PsychologicalPattern.healthy;
    }

    return PsychologicalPattern.unknown;
  }

  /// 智能判断是否应该结束对话
  static ConversationStatus analyzeConversationStatus(
    List<String> conversationHistory,
    PsychologicalPattern currentPattern,
    int currentRound,
  ) {
    // 1. 基础轮次限制（最多10轮，避免无限循环）
    if (currentRound >= 10) {
      return ConversationStatus.forceEnd;
    }

    // 2. 识别到明确模式且已提供疗愈建议
    if (currentPattern != PsychologicalPattern.unknown &&
        currentPattern != PsychologicalPattern.healthy &&
        currentRound >= 4) {
      return ConversationStatus.canEnd;
    }

    // 3. 识别到阳光健康模式
    if (currentPattern == PsychologicalPattern.healthy) {
      return ConversationStatus.switchToPositive;
    }

    // 4. 多轮后仍未识别模式，但有深挖线索
    if (currentPattern == PsychologicalPattern.unknown && currentRound >= 5) {
      final deeperClues = _hasDeepExplorationClues(conversationHistory);
      if (deeperClues) {
        return ConversationStatus.needDeeperExploration;
      } else {
        return ConversationStatus.switchToGeneral;
      }
    }

    // 5. 继续对话
    return ConversationStatus.continueChat;
  }

  /// 检查是否有深度挖掘的线索
  static bool _hasDeepExplorationClues(List<String> conversationHistory) {
    final fullText = conversationHistory.join(' ').toLowerCase();

    // 有情绪词汇但模式不明确
    final emotionalWords = ['不舒服', '不对劲', '说不出', '感觉', '觉得'];
    final hasEmotionalClues = emotionalWords.any((word) => fullText.contains(word));

    // 有关系词汇但模式不明确
    final relationshipWords = ['家人', '朋友', '同事', '老板', '父母'];
    final hasRelationshipClues = relationshipWords.any((word) => fullText.contains(word));

    // 有时间词汇暗示长期问题
    final timeWords = ['一直', '总是', '经常', '从小', '很久'];
    final hasTimeClues = timeWords.any((word) => fullText.contains(word));

    return hasEmotionalClues || hasRelationshipClues || hasTimeClues;
  }

  /// 生成不同状态下的引导策略
  static List<String> generateStatusBasedGuidance(
    ConversationStatus status,
    TarotCard card,
    List<String> conversationHistory,
  ) {
    switch (status) {
      case ConversationStatus.switchToPositive:
        return _generatePositiveGuidance(card);

      case ConversationStatus.needDeeperExploration:
        return _generateDeeperExplorationQuestions(card, conversationHistory);

      case ConversationStatus.switchToGeneral:
        return _generateGeneralWisdomGuidance(card);

      case ConversationStatus.canEnd:
        return ['我们已经深入探索了你的情况。现在让我们专注于疗愈和转化的练习。'];

      case ConversationStatus.forceEnd:
        return ['感谢你的开放分享。让我们将这些洞察转化为实际的行动。'];

      case ConversationStatus.continueChat:
      default:
        return _generateGeneralQuestions(card, 1);
    }
  }

  /// 生成正向引导（针对阳光健康的用户）
  static List<String> _generatePositiveGuidance(TarotCard card) {
    return [
      '从你的分享中感受到很多正能量！这张${card.name}为你的美好状态锦上添花。',
      '你已经拥有很好的心理状态，这张牌想为你的生活带来什么新的可能性？',
      '看起来你很懂得照顾自己的内心。这张${card.name}可能在提醒你关注哪个新的成长方向？',
    ];
  }

  /// 生成深度挖掘问题（当有线索但模式不明确时）
  static List<String> _generateDeeperExplorationQuestions(TarotCard card, List<String> history) {
    final fullText = history.join(' ').toLowerCase();

    if (fullText.contains('家人') || fullText.contains('父母')) {
      return [
        '你提到了家人，这张${card.name}可能在暗示家庭关系对你的影响。你和家人的相处模式是怎样的？',
        '从小到大，家人给你最深的印象是什么？这是否影响了你现在的选择？',
      ];
    }

    if (fullText.contains('工作') || fullText.contains('同事')) {
      return [
        '工作环境似乎对你很重要。这张${card.name}让我好奇，你在职场中最在意的是什么？',
        '你觉得理想的工作状态应该是什么样的？现在的差距在哪里？',
      ];
    }

    return [
      '我感觉你的内心还有一些没有完全表达出来的感受。这张${card.name}邀请你更深入地探索。',
      '有时候我们习惯了某种状态，反而不容易察觉。你觉得有什么是你"习以为常"但其实可以更好的？',
    ];
  }

  /// 生成通用智慧引导（当无法识别具体模式时）
  static List<String> _generateGeneralWisdomGuidance(TarotCard card) {
    return [
      '虽然我们没有发现特定的困扰模式，但这张${card.name}依然为你带来了宇宙的智慧。',
      '有时候，最大的成长来自于对现状的感恩和对未来的开放。这张牌想为你的人生带来什么启发？',
      '你已经是一个很有觉察力的人。这张${card.name}可能在提醒你关注生活中哪个被忽略的美好？',
    ];
  }

  /// 生成疗愈建议
  static List<HealingTechnique> generateHealingTechniques(PsychologicalPattern pattern) {
    switch (pattern) {
      case PsychologicalPattern.attachment:
        return [
          HealingTechnique(
            name: '情绪聚焦法',
            description: '感受胸口的紧绷，对它说"我看见你了"',
            instruction: '闭上眼，将注意力放在胸口，感受那里的感觉，温柔地对它说："我看见你了，谢谢你保护我。"',
          ),
          HealingTechnique(
            name: '书写释放',
            description: '写下你最想对这个人说的话，然后撕掉',
            instruction: '拿出纸笔，写下所有想对那个人说的话，不要审查，写完后可以选择撕掉或烧掉。',
          ),
        ];
      case PsychologicalPattern.pleasing:
        return [
          HealingTechnique(
            name: '内在小孩疗愈',
            description: '对小时候的自己说"你可以表达真实的感受"',
            instruction: '想象小时候的自己，温柔地对他/她说："你的感受很重要，你可以表达真实的自己。"',
          ),
          HealingTechnique(
            name: '边界练习',
            description: '每天练习说一次"不"，从小事开始',
            instruction: '今天找一个小的机会练习说"不"，观察自己的感受，记住这是在保护自己的能量。',
          ),
        ];
      case PsychologicalPattern.rumination:
        return [
          HealingTechnique(
            name: '正念观察',
            description: '观察内耗的想法，不评判，只是看见',
            instruction: '当发现自己在内耗时，停下来，深呼吸，对自己说："我看到了这些想法，但我不是这些想法。"',
          ),
        ];
      case PsychologicalPattern.fear:
        return [
          HealingTechnique(
            name: '恐惧对话',
            description: '与内在的恐惧进行温和的对话',
            instruction: '问问你的恐惧："你想保护我免受什么伤害？"然后感谢它的保护，告诉它你现在很安全。',
          ),
        ];
      case PsychologicalPattern.healthy:
        return [
          HealingTechnique(
            name: '感恩冥想',
            description: '深化对当前美好状态的感恩',
            instruction: '每天花5分钟感恩你生活中的美好，让这种正能量继续扩展。',
          ),
          HealingTechnique(
            name: '能量分享',
            description: '将你的正能量分享给需要的人',
            instruction: '想想身边谁可能需要支持，用你的方式给予他们温暖和鼓励。',
          ),
        ];
      default:
        return [
          HealingTechnique(
            name: '深呼吸练习',
            description: '通过呼吸回到当下',
            instruction: '深吸气4秒，屏息4秒，呼气6秒，重复5次。',
          ),
        ];
    }
  }

  /// 生成显化建议
  static String generateManifestationGuidance(PsychologicalPattern pattern) {
    switch (pattern) {
      case PsychologicalPattern.attachment:
        return '显化一段全然支持你、让你感到安全的新关系。每天花5分钟想象自己被无条件的爱包围。';
      case PsychologicalPattern.pleasing:
        return '显化能够真实表达自己、被人尊重边界的生活状态。每天对镜子说："我的需求很重要。"';
      case PsychologicalPattern.rumination:
        return '显化内在平静、自我接纳的心理状态。每天冥想10分钟，专注于内在的宁静。';
      case PsychologicalPattern.fear:
        return '显化勇气和信任的能量。每天对自己说："我有能力面对任何挑战，宇宙支持着我。"';
      case PsychologicalPattern.healthy:
        return '显化更高层次的喜悦和成就。你已经拥有很好的基础，现在可以向更大的梦想迈进。';
      default:
        return '显化与你最高善相对齐的生活状态。相信宇宙正在为你安排最好的。';
    }
  }
}

/// 问题类型枚举
enum QuestionType {
  relationship,  // 关系类
  career,       // 职业类
  decision,     // 决策类
  emotional,    // 情绪类
  general,      // 通用类
}

/// 心理模式枚举
enum PsychologicalPattern {
  attachment,   // 依恋模式
  pleasing,     // 讨好模式
  rumination,   // 内耗模式
  fear,         // 恐惧模式
  healthy,      // 阳光健康模式
  unknown,      // 未知模式
}

/// 对话状态枚举
enum ConversationStatus {
  continueChat,         // 继续对话
  canEnd,               // 可以结束（已识别模式且提供建议）
  forceEnd,             // 强制结束（达到最大轮次）
  switchToPositive,     // 转向正向引导（用户很健康）
  needDeeperExploration, // 需要更深挖掘（有线索但模式不明）
  switchToGeneral,      // 转向通用引导（无特定模式）
}

/// 疗愈技术模型
class HealingTechnique {
  final String name;
  final String description;
  final String instruction;

  HealingTechnique({
    required this.name,
    required this.description,
    required this.instruction,
  });
}
