import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:ai_tarot_reading/services/ai_emotion_analyst.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// AI塔罗专家团队
/// 根据问题类型和心理状态，调用不同专业领域的AI解读师
class AITarotSpecialists {
  static final _supabase = Supabase.instance.client;
  
  /// 根据分析结果获取专业解读
  static Future<SpecialistReadingResult> getSpecialistReading({
    required EmotionAnalysisResult analysis,
    required String question,
    required List<TarotCard> cards,
    required String userLanguage,
    List<String>? conversationHistory,
  }) async {
    try {
      // 选择专家角色
      final specialist = _selectSpecialist(analysis);
      
      // 构建请求数据 - 使用Edge Function
      final requestData = {
        'question': question,
        'cards': cards.map((card) => {
          'id': card.id,
          'name': card.name,
          'description': card.description,
          'meaning': card.meaning,
          'keywords': card.keywords,
          'isMajorArcana': card.isMajorArcana,
          'isReversed': card.isReversed,
        }).toList(),
        'spreadType': 'specialist_reading',
        'requestType': 'specialist_reading',
        'userLanguage': userLanguage, // 传递用户语言
        'analysisData': {
          'questionType': analysis.questionType.toString(),
          'emotionIntensity': analysis.emotionIntensity.toString(),
          'psychologicalBlock': analysis.psychologicalBlock.toString(),
          'urgencyLevel': analysis.urgencyLevel.toString(),
          'specialist': specialist.toString(),
        },
        'conversationHistory': conversationHistory ?? [],
      };

      // 调用Supabase Edge Function
      print('🔄 开始调用Edge Function: deepseek-tarot-reading');
      print('📋 请求数据类型: ${requestData['requestType']}');
      print('📋 问题: ${requestData['question']}');
      print('📋 卡牌数量: ${(requestData['cards'] as List?)?.length ?? 0}');
      print('📋 用户语言: ${requestData['userLanguage']}');

      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      print('📨 Edge Function响应状态: ${response.status}');
      print('� Edge Function响应数据: ${response.data}');
      
      // 详细错误信息
      if (response.data != null && response.data['success'] == false) {
        print('❌ Edge Function调用失败:');
        print('   - 错误: ${response.data['error']}');
        print('   - 回退解读: ${response.data['reading']}');
      }

      if (response.data != null && response.data['success'] == true) {
        final content = response.data['reading'] as String;
        
        // 解析回复并添加显化任务
        final manifestationTasks = _generateManifestationTasks(analysis, userLanguage);
        final journalPrompts = _generateJournalPrompts(analysis, userLanguage);
        
        return SpecialistReadingResult(
          reading: content,
          specialist: specialist,
          manifestationTasks: manifestationTasks,
          journalPrompts: journalPrompts,
          analysisInsight: analysis.analysisReason,
        );
      } else {
        final errorMsg = 'Edge Function调用失败 - 响应状态: ${response.status}, 错误: ${response.data?['error'] ?? response.data?.toString() ?? '未知错误'}';
        print('❌ $errorMsg');
        
        // 如果有fallback解读，使用它而不是抛出异常
        if (response.data != null && response.data['reading'] != null) {
          print('🔄 使用Edge Function提供的fallback解读');
          final fallbackContent = response.data['reading'] as String;
          
          final manifestationTasks = _generateManifestationTasks(analysis, userLanguage);
          final journalPrompts = _generateJournalPrompts(analysis, userLanguage);
          
          return SpecialistReadingResult(
            reading: fallbackContent,
            specialist: _selectSpecialist(analysis),
            manifestationTasks: manifestationTasks,
            journalPrompts: journalPrompts,
            analysisInsight: analysis.analysisReason,
          );
        }
        
        throw Exception(errorMsg);
      }
    } catch (e) {
      print('❌ 专家解读失败: $e');
      print('📍 错误类型: ${e.runtimeType}');
      print('📍 完整错误信息: ${e.toString()}');
      return _getFallbackReading(analysis, question, cards, userLanguage);
    }
  }
  
  static TarotSpecialist _selectSpecialist(EmotionAnalysisResult analysis) {
    // 根据心理卡点优先选择
    if (analysis.psychologicalBlock != PsychologicalBlock.none) {
      return TarotSpecialist.blockRemover;
    }
    
    // 根据问题类型选择专家
    switch (analysis.questionType) {
      case QuestionType.love:
        return TarotSpecialist.loveExpert;
      case QuestionType.career:
        return TarotSpecialist.careerExpert;
      case QuestionType.wealth:
        return TarotSpecialist.wealthExpert;
      case QuestionType.health:
        return TarotSpecialist.healthExpert;
      case QuestionType.relationship:
        return TarotSpecialist.relationshipExpert;
      case QuestionType.decision:
        return TarotSpecialist.decisionExpert;
      default:
        return TarotSpecialist.generalExpert;
    }
  }
  
  static String _buildSpecialistSystemPrompt(TarotSpecialist specialist, EmotionAnalysisResult analysis, String language) {
    final languageText = language == 'zh' ? '中文' : 'English';
    
    String basePrompt = '''你是一位经验丰富的${_getSpecialistName(specialist, language)}，擅长通过塔罗牌帮助人们获得深度洞察和心灵成长。

请用$languageText回复，保持专业、温暖且富有同理心的语调。

当前咨询情况分析：
- 问题类型：${_getQuestionTypeName(analysis.questionType, language)}
- 情感强度：${_getEmotionIntensityName(analysis.emotionIntensity, language)}
- 重复模式：${_getRepeatPatternName(analysis.repeatPattern, language)}
- 心理卡点：${_getPsychologicalBlockName(analysis.psychologicalBlock, language)}
- 紧急程度：${_getUrgencyLevelName(analysis.urgencyLevel, language)}

''';

    basePrompt += _getSpecialistGuidance(specialist, analysis, language);
    
    basePrompt += '''

回复结构要求：
1. 🔮 **洞察解读** - 基于卡牌和问题的深度分析
2. 💡 **核心指引** - 针对当前状况的具体建议
3. 🌟 **成长方向** - 长期发展和内在提升的方向
4. 💫 **能量祝福** - 温暖的鼓励和正能量

请确保回复既有深度又实用，帮助用户获得真正的洞察和成长。''';

    return basePrompt;
  }
  
  static String _getSpecialistGuidance(TarotSpecialist specialist, EmotionAnalysisResult analysis, String language) {
    switch (specialist) {
      case TarotSpecialist.loveExpert:
        return '''你的专长：
- 深度分析感情模式和关系动态
- 识别爱情中的成长机会和挑战
- 引导健康的情感表达和沟通
- 帮助建立自爱和吸引力法则''';
        
      case TarotSpecialist.careerExpert:
        return '''你的专长：
- 分析职业发展和事业机遇
- 识别天赋和核心竞争力
- 指导职场人际关系和领导力
- 帮助制定实现目标的行动计划''';
        
      case TarotSpecialist.wealthExpert:
        return '''你的专长：
- 分析财富意识和金钱关系
- 识别财富流失的心理原因
- 指导投资思维和理财策略
- 帮助建立丰盛意识和吸引财富''';
        
      case TarotSpecialist.blockRemover:
        return '''你的专长：
- 识别和化解深层心理卡点
- 引导情绪释放和内在疗愈
- 帮助重新框架负面思维模式
- 协助建立健康的应对机制
- 特别关注：${_getPsychologicalBlockGuidance(analysis.psychologicalBlock, language)}''';
        
      default:
        return '''你的专长：
- 综合性人生指导和智慧分享
- 帮助用户获得全面的生活洞察
- 引导个人成长和精神觉醒
- 提供平衡各生活领域的建议''';
    }
  }
  
  static String _getPsychologicalBlockGuidance(PsychologicalBlock block, String language) {
    switch (block) {
      case PsychologicalBlock.anxiety:
        return '焦虑恐惧的根源识别和安全感重建';
      case PsychologicalBlock.control:
        return '控制欲的放下和信任感的培养';
      case PsychologicalBlock.avoidance:
        return '面对现实的勇气和行动力激发';
      case PsychologicalBlock.selfDoubt:
        return '自我价值的重新认知和自信重建';
      case PsychologicalBlock.attachmentTrauma:
        return '依恋创伤的疗愈和健康关系模式建立';
      default:
        return '内在平衡和心理健康维护';
    }
  }
  
  static String _buildUserPrompt(String question, List<TarotCard> cards, EmotionAnalysisResult analysis, List<String> history) {
    String prompt = '''用户问题：$question

抽到的塔罗牌：
${cards.map((card) => '- ${card.name}${card.isReversed ? "（逆位）" : "（正位）"}').join('\n')}

''';

    if (history.isNotEmpty) {
      prompt += '''最近的对话记录：
${history.take(3).map((msg) => '- $msg').join('\n')}

''';
    }

    prompt += '''请根据以上信息提供专业的塔罗解读，特别关注用户的情感状态和可能的心理卡点。''';
    
    return prompt;
  }
  
  static List<ManifestationTask> _generateManifestationTasks(EmotionAnalysisResult analysis, String language) {
    List<ManifestationTask> tasks = [];
    
    // 根据问题类型生成显化任务
    switch (analysis.questionType) {
      case QuestionType.love:
        tasks.addAll([
          ManifestationTask(
            title: language == 'zh' ? '爱的自我肯定' : 'Love Self-Affirmation',
            description: language == 'zh' ? '每天对镜子说"我值得被深深爱着"' : 'Say "I deserve to be deeply loved" to the mirror daily',
            duration: 7,
            frequency: 'daily',
          ),
          ManifestationTask(
            title: language == 'zh' ? '理想关系可视化' : 'Ideal Relationship Visualization',
            description: language == 'zh' ? '花5分钟想象和理想伴侣在一起的美好时光' : 'Spend 5 minutes visualizing wonderful times with your ideal partner',
            duration: 14,
            frequency: 'daily',
          ),
        ]);
        break;
        
      case QuestionType.career:
        tasks.addAll([
          ManifestationTask(
            title: language == 'zh' ? '成功职场形象' : 'Successful Career Image',
            description: language == 'zh' ? '每天想象自己在理想工作中自信发光的样子' : 'Daily visualization of confidently shining in your ideal job',
            duration: 10,
            frequency: 'daily',
          ),
        ]);
        break;
        
      case QuestionType.wealth:
        tasks.addAll([
          ManifestationTask(
            title: language == 'zh' ? '丰盛意识练习' : 'Abundance Consciousness Practice',
            description: language == 'zh' ? '每天感恩已有的财富，观想金钱能量流入生活' : 'Daily gratitude for existing wealth and visualization of money energy flowing in',
            duration: 21,
            frequency: 'daily',
          ),
        ]);
        break;
        
      default:
        tasks.add(ManifestationTask(
          title: language == 'zh' ? '内在力量冥想' : 'Inner Strength Meditation',
          description: language == 'zh' ? '每天静心5分钟，连接内在智慧和力量' : '5 minutes daily meditation to connect with inner wisdom and strength',
          duration: 7,
          frequency: 'daily',
        ));
    }
    
    // 根据心理卡点添加特定任务
    if (analysis.psychologicalBlock != PsychologicalBlock.none) {
      tasks.add(_generateBlockRemovalTask(analysis.psychologicalBlock, language));
    }
    
    return tasks;
  }
  
  static ManifestationTask _generateBlockRemovalTask(PsychologicalBlock block, String language) {
    switch (block) {
      case PsychologicalBlock.anxiety:
        return ManifestationTask(
          title: language == 'zh' ? '安全感建立练习' : 'Security Building Exercise',
          description: language == 'zh' ? '深呼吸时重复"我是安全的，我是被保护的"' : 'Repeat "I am safe, I am protected" while deep breathing',
          duration: 14,
          frequency: 'daily',
        );
        
      case PsychologicalBlock.control:
        return ManifestationTask(
          title: language == 'zh' ? '放手与信任' : 'Letting Go and Trust',
          description: language == 'zh' ? '每天练习"我信任生命的安排，我放下控制"' : 'Daily practice: "I trust life\'s arrangements, I let go of control"',
          duration: 21,
          frequency: 'daily',
        );
        
      default:
        return ManifestationTask(
          title: language == 'zh' ? '内在疗愈冥想' : 'Inner Healing Meditation',
          description: language == 'zh' ? '向内心受伤的部分发送爱与光明' : 'Send love and light to the wounded parts within',
          duration: 14,
          frequency: 'daily',
        );
    }
  }
  
  static List<JournalPrompt> _generateJournalPrompts(EmotionAnalysisResult analysis, String language) {
    List<JournalPrompt> prompts = [];
    
    // 基础反思问题
    prompts.addAll([
      JournalPrompt(
        prompt: language == 'zh' ? '今天我从这次塔罗解读中学到了什么？' : 'What did I learn from this tarot reading today?',
        category: 'reflection',
      ),
      JournalPrompt(
        prompt: language == 'zh' ? '我准备采取什么具体行动来改善现状？' : 'What specific actions am I ready to take to improve my situation?',
        category: 'action',
      ),
    ]);
    
    // 根据问题类型添加特定问题
    switch (analysis.questionType) {
      case QuestionType.love:
        prompts.add(JournalPrompt(
          prompt: language == 'zh' ? '我在亲密关系中最需要学习的是什么？' : 'What do I most need to learn in intimate relationships?',
          category: 'love',
        ));
        break;
        
      case QuestionType.career:
        prompts.add(JournalPrompt(
          prompt: language == 'zh' ? '什么样的工作能让我感到真正的满足和成就？' : 'What kind of work would bring me true satisfaction and achievement?',
          category: 'career',
        ));
        break;
        
      default:
        prompts.add(JournalPrompt(
          prompt: language == 'zh' ? '我今天最感恩的三件事是什么？' : 'What are the three things I\'m most grateful for today?',
          category: 'gratitude',
        ));
    }
    
    return prompts;
  }
  
  static SpecialistReadingResult _getFallbackReading(EmotionAnalysisResult analysis, String question, List<TarotCard> cards, String language) {
    final reading = language == 'zh' 
        ? '''🔮 **洞察解读**
根据您抽到的卡牌，我看到您正处在一个需要内在反思的时期。卡牌提醒您要相信自己的直觉。

💡 **核心指引**
建议您保持开放的心态，勇敢面对当前的挑战。

🌟 **成长方向**
这是一个很好的学习和成长机会，请珍惜这个过程。

💫 **能量祝福**
愿您在这段旅程中获得智慧和力量。'''
        : '''🔮 **Insight Reading**
Based on the cards you drew, I see you're in a period that requires inner reflection. The cards remind you to trust your intuition.

💡 **Core Guidance**
I suggest maintaining an open mind and courageously facing current challenges.

🌟 **Growth Direction**
This is a great learning and growth opportunity, please cherish this process.

💫 **Energy Blessing**
May you gain wisdom and strength on this journey.''';
    
    return SpecialistReadingResult(
      reading: reading,
      specialist: TarotSpecialist.generalExpert,
      manifestationTasks: _generateManifestationTasks(analysis, language),
      journalPrompts: _generateJournalPrompts(analysis, language),
      analysisInsight: analysis.analysisReason,
    );
  }
  
  // 辅助方法 - 获取各种名称
  static String _getSpecialistName(TarotSpecialist specialist, String language) {
    if (language == 'zh') {
      switch (specialist) {
        case TarotSpecialist.loveExpert: return '情感关系专家';
        case TarotSpecialist.careerExpert: return '事业发展导师';
        case TarotSpecialist.wealthExpert: return '财富能量顾问';
        case TarotSpecialist.healthExpert: return '身心健康指导师';
        case TarotSpecialist.relationshipExpert: return '人际关系专家';
        case TarotSpecialist.decisionExpert: return '决策智慧导师';
        case TarotSpecialist.blockRemover: return '心理卡点解除师';
        case TarotSpecialist.generalExpert: return '综合人生导师';
      }
    } else {
      switch (specialist) {
        case TarotSpecialist.loveExpert: return 'Love & Relationship Expert';
        case TarotSpecialist.careerExpert: return 'Career Development Mentor';
        case TarotSpecialist.wealthExpert: return 'Wealth Energy Advisor';
        case TarotSpecialist.healthExpert: return 'Health & Wellness Guide';
        case TarotSpecialist.relationshipExpert: return 'Relationship Specialist';
        case TarotSpecialist.decisionExpert: return 'Decision Wisdom Mentor';
        case TarotSpecialist.blockRemover: return 'Psychological Block Remover';
        case TarotSpecialist.generalExpert: return 'General Life Coach';
      }
    }
  }
  
  static String _getQuestionTypeName(QuestionType type, String language) {
    if (language == 'zh') {
      switch (type) {
        case QuestionType.love: return '爱情';
        case QuestionType.career: return '事业';
        case QuestionType.wealth: return '财富';
        case QuestionType.health: return '健康';
        case QuestionType.relationship: return '人际';
        case QuestionType.decision: return '决策';
        case QuestionType.growth: return '成长';
      }
    } else {
      return type.toString().split('.').last.toUpperCase();
    }
  }
  
  static String _getEmotionIntensityName(EmotionIntensity intensity, String language) {
    if (language == 'zh') {
      switch (intensity) {
        case EmotionIntensity.low: return '低';
        case EmotionIntensity.medium: return '中';
        case EmotionIntensity.high: return '高';
      }
    } else {
      return intensity.toString().split('.').last.toUpperCase();
    }
  }
  
  static String _getRepeatPatternName(RepeatPattern pattern, String language) {
    if (language == 'zh') {
      switch (pattern) {
        case RepeatPattern.none: return '无重复';
        case RepeatPattern.slight: return '轻微重复';
        case RepeatPattern.obvious: return '明显重复';
        case RepeatPattern.severe: return '严重重复';
      }
    } else {
      return pattern.toString().split('.').last.toUpperCase();
    }
  }
  
  static String _getPsychologicalBlockName(PsychologicalBlock block, String language) {
    if (language == 'zh') {
      switch (block) {
        case PsychologicalBlock.none: return '无明显卡点';
        case PsychologicalBlock.anxiety: return '焦虑恐惧';
        case PsychologicalBlock.control: return '控制欲望';
        case PsychologicalBlock.avoidance: return '逃避现实';
        case PsychologicalBlock.selfDoubt: return '自我否定';
        case PsychologicalBlock.attachmentTrauma: return '依恋创伤';
      }
    } else {
      return block.toString().split('.').last.toUpperCase();
    }
  }
  
  static String _getUrgencyLevelName(UrgencyLevel level, String language) {
    if (language == 'zh') {
      switch (level) {
        case UrgencyLevel.low: return '低';
        case UrgencyLevel.medium: return '中';
        case UrgencyLevel.high: return '高';
      }
    } else {
      return level.toString().split('.').last.toUpperCase();
    }
  }
}

/// 塔罗专家类型
enum TarotSpecialist {
  loveExpert,        // 情感关系专家
  careerExpert,      // 事业发展导师
  wealthExpert,      // 财富能量顾问
  healthExpert,      // 身心健康指导师
  relationshipExpert, // 人际关系专家
  decisionExpert,    // 决策智慧导师
  blockRemover,      // 心理卡点解除师
  generalExpert,     // 综合人生导师
}

/// 专家解读结果
class SpecialistReadingResult {
  final String reading;
  final TarotSpecialist specialist;
  final List<ManifestationTask> manifestationTasks;
  final List<JournalPrompt> journalPrompts;
  final String analysisInsight;
  
  SpecialistReadingResult({
    required this.reading,
    required this.specialist,
    required this.manifestationTasks,
    required this.journalPrompts,
    required this.analysisInsight,
  });
}

/// 显化任务
class ManifestationTask {
  final String title;
  final String description;
  final int duration; // 持续天数
  final String frequency; // 频率
  
  ManifestationTask({
    required this.title,
    required this.description,
    required this.duration,
    required this.frequency,
  });
}

/// 日记提示
class JournalPrompt {
  final String prompt;
  final String category;
  
  JournalPrompt({
    required this.prompt,
    required this.category,
  });
} 