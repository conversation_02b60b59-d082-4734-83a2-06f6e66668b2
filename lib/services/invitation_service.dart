import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 邀请码服务
class InvitationService extends ChangeNotifier {
  static const String _usedInvitationCodeKey = 'used_invitation_code';
  static const String _myInvitationCodeKey = 'my_invitation_code';
  
  final SupabaseClient _supabase = Supabase.instance.client;
  
  bool _hasUsedInvitationCode = false;
  String? _myInvitationCode;
  
  bool get hasUsedInvitationCode => _hasUsedInvitationCode;
  String? get myInvitationCode => _myInvitationCode;

  /// 初始化邀请码服务
  Future<void> initialize() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _hasUsedInvitationCode = prefs.getBool(_usedInvitationCodeKey) ?? false;
      _myInvitationCode = prefs.getString(_myInvitationCodeKey);
      
      // 如果用户已登录但没有邀请码，生成一个
      if (_supabase.auth.currentUser != null && _myInvitationCode == null) {
        await _generateMyInvitationCode();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ 邀请码服务初始化失败: $e');
    }
  }

  /// 验证并使用邀请码
  Future<InvitationResult> useInvitationCode(String code) async {
    try {
      // 检查用户是否已经使用过邀请码
      if (_hasUsedInvitationCode) {
        return InvitationResult(
          success: false,
          message: '您已经使用过邀请码了',
          rewardType: null,
        );
      }

      // 验证邀请码格式
      if (!_isValidInvitationCode(code)) {
        return InvitationResult(
          success: false,
          message: '邀请码格式不正确',
          rewardType: null,
        );
      }

      // 检查邀请码是否存在且有效
      final invitationData = await _validateInvitationCode(code);
      if (invitationData == null) {
        return InvitationResult(
          success: false,
          message: '邀请码不存在或已失效',
          rewardType: null,
        );
      }

      // 使用邀请码
      final result = await _redeemInvitationCode(code, invitationData);
      
      if (result.success) {
        // 标记为已使用
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool(_usedInvitationCodeKey, true);
        _hasUsedInvitationCode = true;
        notifyListeners();
      }

      return result;
    } catch (e) {
      debugPrint('❌ 使用邀请码失败: $e');
      return InvitationResult(
        success: false,
        message: '使用邀请码时发生错误，请稍后重试',
        rewardType: null,
      );
    }
  }

  /// 生成我的邀请码
  Future<void> _generateMyInvitationCode() async {
    try {
      final user = _supabase.auth.currentUser;
      if (user == null) return;

      // 基于用户ID生成6位邀请码
      final userId = user.id;
      final code = _generateCodeFromUserId(userId);
      
      _myInvitationCode = code;
      
      // 保存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_myInvitationCodeKey, code);
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ 生成邀请码失败: $e');
    }
  }

  /// 从用户ID生成邀请码
  String _generateCodeFromUserId(String userId) {
    // 使用用户ID的哈希值生成6位字母数字组合
    final hash = userId.hashCode.abs();
    final chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    String code = '';
    
    int temp = hash;
    for (int i = 0; i < 6; i++) {
      code += chars[temp % chars.length];
      temp ~/= chars.length;
    }
    
    return code;
  }

  /// 验证邀请码格式
  bool _isValidInvitationCode(String code) {
    // 6位字母数字组合
    final regex = RegExp(r'^[A-Z0-9]{6}$');
    return regex.hasMatch(code.toUpperCase());
  }

  /// 验证邀请码是否存在
  Future<Map<String, dynamic>?> _validateInvitationCode(String code) async {
    try {
      // 这里可以连接到后端验证邀请码
      // 目前使用模拟数据
      final validCodes = {
        'HELLO1': {
          'type': 'new_user',
          'reward': 'weekly_membership',
          'description': '新用户专享周会员',
        },
        'LUCK88': {
          'type': 'activity',
          'reward': 'weekly_membership',
          'description': '幸运活动周会员',
        },
        'VIP024': {
          'type': 'special',
          'reward': 'weekly_membership',
          'description': '2024特别版周会员',
        },
        // 🎯 开发者可以在这里添加更多邀请码（必须6位）
        'SPRING': {
          'type': 'seasonal',
          'reward': 'weekly_membership',
          'description': '春季限定周会员',
        },
        'FRIEND': {
          'type': 'referral',
          'reward': 'weekly_membership',
          'description': '好友推荐周会员',
        },
        'BETA01': {
          'type': 'beta_test',
          'reward': 'weekly_membership',
          'description': '内测用户专享',
        },
        'GIFT99': {
          'type': 'gift',
          'reward': 'weekly_membership',
          'description': '礼品码周会员',
        },
        'TEST01': {
          'type': 'test',
          'reward': 'weekly_membership',
          'description': '测试专用邀请码',
        },
      };

      return validCodes[code.toUpperCase()];
    } catch (e) {
      debugPrint('❌ 验证邀请码失败: $e');
      return null;
    }
  }

  /// 兑换邀请码奖励
  Future<InvitationResult> _redeemInvitationCode(
    String code, 
    Map<String, dynamic> invitationData
  ) async {
    try {
      final rewardType = invitationData['reward'] as String;
      final description = invitationData['description'] as String;

      // 这里应该调用订阅服务来激活会员
      // 目前返回成功结果
      return InvitationResult(
        success: true,
        message: '恭喜！您已成功获得$description',
        rewardType: rewardType,
      );
    } catch (e) {
      debugPrint('❌ 兑换邀请码奖励失败: $e');
      return InvitationResult(
        success: false,
        message: '兑换奖励时发生错误',
        rewardType: null,
      );
    }
  }

  /// 获取推荐统计
  Future<InvitationStats> getInvitationStats() async {
    try {
      // 这里应该从后端获取真实数据
      // 目前返回模拟数据
      return InvitationStats(
        totalInvitations: 3,
        successfulInvitations: 2,
        totalRewardDays: 6,
      );
    } catch (e) {
      debugPrint('❌ 获取邀请统计失败: $e');
      return InvitationStats(
        totalInvitations: 0,
        successfulInvitations: 0,
        totalRewardDays: 0,
      );
    }
  }
}

/// 邀请码使用结果
class InvitationResult {
  final bool success;
  final String message;
  final String? rewardType;

  InvitationResult({
    required this.success,
    required this.message,
    required this.rewardType,
  });
}

/// 邀请统计
class InvitationStats {
  final int totalInvitations;
  final int successfulInvitations;
  final int totalRewardDays;

  InvitationStats({
    required this.totalInvitations,
    required this.successfulInvitations,
    required this.totalRewardDays,
  });
}
