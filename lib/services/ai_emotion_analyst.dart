import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:convert';

/// AI情感分析师
/// 专门负责分析用户的问题类型、情感状态和重复模式
class AIEmotionAnalyst {
  static final _supabase = Supabase.instance.client;

  /// 分析用户问题的类型和情感状态
  static Future<EmotionAnalysisResult> analyzeUserQuestion({
    required String currentQuestion,
    required List<String> questionHistory,
    required String userLanguage,
  }) async {
    try {
      final prompt = _buildAnalysisPrompt(currentQuestion, questionHistory, userLanguage);

      // 构建请求数据，使用Supabase Edge Function
      final requestData = {
        'question': currentQuestion,
        'cards': [], // 情感分析不需要卡牌信息
        'spreadType': 'emotion_analysis',
        'requestType': 'emotion_analysis',
        'userLanguage': userLanguage,
        'questionHistory': questionHistory,
        'analysisPrompt': prompt,
      };

      print('🔄 开始调用Edge Function (情感分析): deepseek-tarot-reading');
      print('📋 请求数据: ${requestData.toString()}');

      final response = await _supabase.functions.invoke(
        'deepseek-tarot-reading',
        body: requestData,
      );

      print('📨 Edge Function响应: ${response.data}');
      print('📊 响应状态: ${response.status}');

      if (response.data != null && response.data['success'] == true) {
        final content = response.data['reading'] as String;

        // 解析JSON回复
        try {
          final analysisJson = jsonDecode(content);
          return EmotionAnalysisResult.fromJson(analysisJson);
        } catch (e) {
          print('❌ 解析分析结果JSON失败: $e');
          return _getFallbackAnalysis(currentQuestion);
        }
      } else {
        print('❌ 情感分析Edge Function调用失败: ${response.data?['error'] ?? '未知错误'}');
        print('🔄 使用本地fallback分析');
        return _getFallbackAnalysis(currentQuestion);
      }
    } catch (e) {
      print('❌ 情感分析失败: $e');
      return _getFallbackAnalysis(currentQuestion);
    }
  }
  
  static String _buildAnalysisPrompt(String currentQuestion, List<String> history, String language) {
    final languageText = language == 'zh' ? '中文' : '英文';
    
    String prompt = '''请分析以下塔罗咨询情况：

当前问题：$currentQuestion

''';
    
    if (history.isNotEmpty) {
      prompt += '''历史问题记录：
${history.take(5).map((q) => '- $q').join('\n')}

''';
    }
    
    prompt += '''请用$languageText分析用户的问题类型、情感状态、是否存在重复模式和心理卡点，并给出专业建议。''';
    
    return prompt;
  }
  
  static EmotionAnalysisResult _getFallbackAnalysis(String question) {
    // 简单的关键词分析作为备用
    QuestionType type = QuestionType.growth;
    if (question.contains('爱情') || question.contains('感情') || question.contains('恋爱')) {
      type = QuestionType.love;
    } else if (question.contains('工作') || question.contains('事业') || question.contains('职业')) {
      type = QuestionType.career;
    } else if (question.contains('钱') || question.contains('财富') || question.contains('收入')) {
      type = QuestionType.wealth;
    }
    
    return EmotionAnalysisResult(
      questionType: type,
      emotionIntensity: EmotionIntensity.medium,
      repeatPattern: RepeatPattern.none,
      psychologicalBlock: PsychologicalBlock.none,
      urgencyLevel: UrgencyLevel.medium,
      analysisReason: '基于关键词的基础分析',
      recommendedApproach: 'standard_reading',
    );
  }
}

/// 情感分析结果
class EmotionAnalysisResult {
  final QuestionType questionType;
  final EmotionIntensity emotionIntensity;
  final RepeatPattern repeatPattern;
  final PsychologicalBlock psychologicalBlock;
  final UrgencyLevel urgencyLevel;
  final String analysisReason;
  final String recommendedApproach;
  
  EmotionAnalysisResult({
    required this.questionType,
    required this.emotionIntensity,
    required this.repeatPattern,
    required this.psychologicalBlock,
    required this.urgencyLevel,
    required this.analysisReason,
    required this.recommendedApproach,
  });
  
  factory EmotionAnalysisResult.fromJson(Map<String, dynamic> json) {
    return EmotionAnalysisResult(
      questionType: _parseQuestionType(json['questionType']),
      emotionIntensity: _parseEmotionIntensity(json['emotionIntensity']),
      repeatPattern: _parseRepeatPattern(json['repeatPattern']),
      psychologicalBlock: _parsePsychologicalBlock(json['psychologicalBlock']),
      urgencyLevel: _parseUrgencyLevel(json['urgencyLevel']),
      analysisReason: json['analysisReason'] ?? '',
      recommendedApproach: json['recommendedApproach'] ?? 'standard_reading',
    );
  }
  
  static QuestionType _parseQuestionType(String? type) {
    switch (type?.toLowerCase()) {
      case '爱情': case 'love': return QuestionType.love;
      case '事业': case 'career': return QuestionType.career;
      case '财富': case 'wealth': return QuestionType.wealth;
      case '健康': case 'health': return QuestionType.health;
      case '人际': case 'relationship': return QuestionType.relationship;
      case '决策': case 'decision': return QuestionType.decision;
      default: return QuestionType.growth;
    }
  }
  
  static EmotionIntensity _parseEmotionIntensity(String? intensity) {
    switch (intensity?.toLowerCase()) {
      case '低': case 'low': return EmotionIntensity.low;
      case '高': case 'high': return EmotionIntensity.high;
      default: return EmotionIntensity.medium;
    }
  }
  
  static RepeatPattern _parseRepeatPattern(String? pattern) {
    switch (pattern?.toLowerCase()) {
      case '轻微重复': case 'slight': return RepeatPattern.slight;
      case '明显重复': case 'obvious': return RepeatPattern.obvious;
      case '严重重复': case 'severe': return RepeatPattern.severe;
      default: return RepeatPattern.none;
    }
  }
  
  static PsychologicalBlock _parsePsychologicalBlock(String? block) {
    switch (block?.toLowerCase()) {
      case '焦虑恐惧': case 'anxiety': return PsychologicalBlock.anxiety;
      case '控制欲望': case 'control': return PsychologicalBlock.control;
      case '逃避现实': case 'avoidance': return PsychologicalBlock.avoidance;
      case '自我否定': case 'self_doubt': return PsychologicalBlock.selfDoubt;
      case '依恋创伤': case 'attachment': return PsychologicalBlock.attachmentTrauma;
      default: return PsychologicalBlock.none;
    }
  }
  
  static UrgencyLevel _parseUrgencyLevel(String? level) {
    switch (level?.toLowerCase()) {
      case '低': case 'low': return UrgencyLevel.low;
      case '高': case 'high': return UrgencyLevel.high;
      default: return UrgencyLevel.medium;
    }
  }
}

enum QuestionType {
  love,        // 爱情
  career,      // 事业
  wealth,      // 财富
  health,      // 健康
  relationship, // 人际
  decision,    // 决策
  growth,      // 成长
}

enum EmotionIntensity {
  low,         // 低
  medium,      // 中
  high,        // 高
}

enum RepeatPattern {
  none,        // 无重复
  slight,      // 轻微重复
  obvious,     // 明显重复
  severe,      // 严重重复
}

enum PsychologicalBlock {
  none,            // 无明显卡点
  anxiety,         // 焦虑恐惧
  control,         // 控制欲望
  avoidance,       // 逃避现实
  selfDoubt,       // 自我否定
  attachmentTrauma, // 依恋创伤
}

enum UrgencyLevel {
  low,         // 低
  medium,      // 中
  high,        // 高
} 