import 'package:supabase_flutter/supabase_flutter.dart';

/// Supabase数据服务
/// 处理AI塔罗应用的数据操作
class SupabaseDataService {
  final SupabaseClient _supabase = Supabase.instance.client;
  
  String? get currentUserId => _supabase.auth.currentUser?.id;
  bool get isAuthenticated => currentUserId != null;
  
  /// 测试数据库连接
  Future<bool> testConnection() async {
    try {
      await _supabase.from('users').select('id').limit(1);
      return true;
    } catch (e) {
      print('❌ 数据库连接测试失败: $e');
      return false;
    }
  }
  
  // ============================================================================
  // 用户偏好设置
  // ============================================================================
  
  /// 保存用户偏好设置
  Future<void> saveUserPreferences(Map<String, dynamic> preferences) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase.from('user_preferences').upsert({
        'user_id': currentUserId,
        'preferences': preferences,
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      print('✅ 用户偏好设置保存成功');
    } catch (e) {
      print('❌ 用户偏好设置保存失败: $e');
      rethrow;
    }
  }
  
  /// 获取用户偏好设置
  Future<Map<String, dynamic>> getUserPreferences() async {
    try {
      if (!isAuthenticated) return {};
      
      final response = await _supabase
          .from('user_preferences')
          .select('preferences')
          .eq('user_id', currentUserId!)
          .maybeSingle();
      
      return response?['preferences'] ?? {};
    } catch (e) {
      print('❌ 获取用户偏好设置失败: $e');
      return {};
    }
  }
  
  /// 更新语言设置
  Future<void> updateLanguage(String languageCode) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase.from('user_preferences').upsert({
        'user_id': currentUserId,
        'language': languageCode,
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      print('✅ 语言设置更新成功');
    } catch (e) {
      print('❌ 语言设置更新失败: $e');
      rethrow;
    }
  }
  
  // ============================================================================
  // 通知设置
  // ============================================================================
  
  /// 保存通知设置
  Future<void> saveNotificationSettings(Map<String, dynamic> settings) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase.from('notification_settings').upsert({
        'user_id': currentUserId,
        ...settings,
        'updated_at': DateTime.now().toIso8601String(),
      });
      
      print('✅ 通知设置保存成功');
    } catch (e) {
      print('❌ 通知设置保存失败: $e');
      rethrow;
    }
  }
  
  /// 获取通知设置
  Future<Map<String, dynamic>> getNotificationSettings() async {
    try {
      if (!isAuthenticated) return {};
      
      final response = await _supabase
          .from('notification_settings')
          .select()
          .eq('user_id', currentUserId!)
          .maybeSingle();
      
      return response ?? {};
    } catch (e) {
      print('❌ 获取通知设置失败: $e');
      return {};
    }
  }
  
  // ============================================================================
  // 数据清理
  // ============================================================================
  
  /// 清空用户所有数据
  Future<void> clearAllUserData() async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await Future.wait([
        _supabase.from('tarot_readings').delete().eq('user_id', currentUserId!),
        _supabase.from('daily_tarot').delete().eq('user_id', currentUserId!),
        _supabase.from('manifestation_records').delete().eq('user_id', currentUserId!),
        _supabase.from('user_preferences').delete().eq('user_id', currentUserId!),
        _supabase.from('notification_settings').delete().eq('user_id', currentUserId!),
      ]);
      
      print('✅ 用户数据清空成功');
    } catch (e) {
      print('❌ 用户数据清空失败: $e');
      rethrow;
    }
  }
  
  /// 清空塔罗解读历史
  Future<void> clearTarotReadings() async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase
          .from('tarot_readings')
          .delete()
          .eq('user_id', currentUserId!);
      
      print('✅ 塔罗解读历史清空成功');
    } catch (e) {
      print('❌ 塔罗解读历史清空失败: $e');
      rethrow;
    }
  }
  
  // ============================================================================
  // 统计信息
  // ============================================================================
  
  /// 获取用户统计信息
  Future<Map<String, int>> getUserStats() async {
    try {
      if (!isAuthenticated) {
        return {
          'totalReadings': 0,
          'dailyStreak': 0,
          'totalDays': 0,
          'manifestationDays': 0,
          'totalManifestationPractices': 0,
          'wealthPractices': 0,
          'careerPractices': 0,
          'beautyPractices': 0,
          'famePractices': 0,
          'lovePractices': 0,
        };
      }

      // 获取解读总数
      final readingsResponse = await _supabase
          .from('tarot_readings')
          .select('id')
          .eq('user_id', currentUserId!);

      // 获取每日塔罗天数
      final dailyTarotResponse = await _supabase
          .from('daily_tarot')
          .select('date')
          .eq('user_id', currentUserId!)
          .eq('is_drawn', true);

      // 获取显化练习统计
      final manifestationResponse = await _supabase
          .from('manifestation_records')
          .select('goal, count')
          .eq('user_id', currentUserId!);

      // 计算显化练习统计
      int totalManifestationPractices = 0;
      int wealthPractices = 0;
      int careerPractices = 0;
      int beautyPractices = 0;
      int famePractices = 0;
      int lovePractices = 0;

      for (var record in manifestationResponse) {
        final count = record['count'] as int? ?? 0;
        final goal = record['goal'] as String? ?? '';

        totalManifestationPractices += count;

        switch (goal) {
          case 'wealth':
            wealthPractices += count;
            break;
          case 'career':
            careerPractices += count;
            break;
          case 'beauty':
            beautyPractices += count;
            break;
          case 'fame':
            famePractices += count;
            break;
          case 'love':
            lovePractices += count;
            break;
        }
      }

      // 获取显化练习天数（有记录的不同日期数）
      final manifestationDaysResponse = await _supabase
          .from('manifestation_records')
          .select('date')
          .eq('user_id', currentUserId!);

      final uniqueDates = manifestationDaysResponse
          .map((record) => record['date'] as String)
          .toSet();

      return {
        'totalReadings': readingsResponse.length,
        'dailyStreak': 0, // 需要计算连续天数
        'totalDays': dailyTarotResponse.length,
        'manifestationDays': uniqueDates.length,
        'totalManifestationPractices': totalManifestationPractices,
        'wealthPractices': wealthPractices,
        'careerPractices': careerPractices,
        'beautyPractices': beautyPractices,
        'famePractices': famePractices,
        'lovePractices': lovePractices,
      };
    } catch (e) {
      print('❌ 获取用户统计信息失败: $e');
      return {
        'totalReadings': 0,
        'dailyStreak': 0,
        'totalDays': 0,
        'manifestationDays': 0,
        'totalManifestationPractices': 0,
        'wealthPractices': 0,
        'careerPractices': 0,
        'beautyPractices': 0,
        'famePractices': 0,
        'lovePractices': 0,
      };
    }
  }
  
  // ============================================================================
  // 每日塔罗数据操作
  // ============================================================================
  
  /// 保存每日塔罗数据
  Future<void> saveDailyTarot(Map<String, dynamic> dailyTarotData) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase.from('daily_tarot').upsert({
        'user_id': currentUserId,
        ...dailyTarotData,
        'created_at': DateTime.now().toIso8601String(),
      });
      
      print('✅ 每日塔罗数据保存成功');
    } catch (e) {
      print('❌ 每日塔罗数据保存失败: $e');
      rethrow;
    }
  }
  
  /// 更新每日塔罗的显化日记
  Future<void> updateDailyTarotJournal(String date, String journal) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase.from('daily_tarot').update({
        'manifestation_journal': journal,
      }).eq('user_id', currentUserId!)
        .eq('date', date);
      
      print('✅ 显化日记更新成功');
    } catch (e) {
      print('❌ 显化日记更新失败: $e');
      rethrow;
    }
  }
  
  /// 获取用户的每日塔罗记录
  Future<List<Map<String, dynamic>>> getDailyTarotRecords([int? limitDays]) async {
    try {
      if (!isAuthenticated) return [];
      
      var query = _supabase
          .from('daily_tarot')
          .select()
          .eq('user_id', currentUserId!)
          .order('date', ascending: false);
      
      if (limitDays != null) {
        query = query.limit(limitDays);
      }
      
      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('❌ 获取每日塔罗记录失败: $e');
      return [];
    }
  }
  
  /// 获取特定日期的每日塔罗
  Future<Map<String, dynamic>?> getDailyTarotByDate(String date) async {
    try {
      if (!isAuthenticated) return null;
      
      final response = await _supabase
          .from('daily_tarot')
          .select()
          .eq('user_id', currentUserId!)
          .eq('date', date)
          .maybeSingle();
      
      return response;
    } catch (e) {
      print('❌ 获取特定日期每日塔罗失败: $e');
      return null;
    }
  }

  /// 保存显化日记
  Future<void> saveManifestationJournal(Map<String, dynamic> journalData) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');

      // 尝试更新已存在的记录，如果不存在则插入新记录
      await _supabase.from('manifestation_records').upsert({
        'user_id': currentUserId,
        ...journalData,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      print('✅ 显化日记保存成功');
    } catch (e) {
      print('❌ 显化日记保存失败: $e');
      rethrow;
    }
  }

  /// 记录显化练习
  Future<void> recordManifestationPractice({
    required dynamic goal, // ManifestationGoal
    required int clickCount,
    required List<String> affirmations,
  }) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');

      final today = DateTime.now();
      final dateStr = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      final goalStr = goal.toString().split('.').last; // 将枚举转换为字符串

      // 获取今天已有的记录
      final existingResponse = await _supabase
          .from('manifestation_records')
          .select('count, total_count, affirmations')
          .eq('user_id', currentUserId!)
          .eq('goal', goalStr)
          .eq('date', dateStr)
          .maybeSingle();

      if (existingResponse != null) {
        // 更新已有记录
        final currentCount = existingResponse['count'] as int? ?? 0;
        final currentTotalCount = existingResponse['total_count'] as int? ?? 0;
        final currentAffirmations = List<String>.from(existingResponse['affirmations'] as List? ?? []);

        // 合并肯定语（去重）
        final allAffirmations = {...currentAffirmations, ...affirmations}.toList();

        await _supabase.from('manifestation_records').update({
          'count': currentCount + 1,
          'total_count': currentTotalCount + clickCount,
          'affirmations': allAffirmations,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('user_id', currentUserId!)
          .eq('goal', goalStr)
          .eq('date', dateStr);
      } else {
        // 创建新记录
        await _supabase.from('manifestation_records').insert({
          'user_id': currentUserId,
          'goal': goalStr,
          'date': dateStr,
          'count': 1,
          'total_count': clickCount,
          'affirmations': affirmations,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
      }

      print('✅ 显化练习记录成功: $goalStr, 点击次数: $clickCount');
    } catch (e) {
      print('❌ 显化练习记录失败: $e');
      rethrow;
    }
  }

  /// 获取每日塔罗数据
  Future<Map<String, dynamic>?> getDailyTarot(String date) async {
    try {
      if (!isAuthenticated) return null;

      final response = await _supabase
          .from('daily_tarot')
          .select('*')
          .eq('user_id', currentUserId!)
          .eq('date', date)
          .maybeSingle();

      return response;
    } catch (e) {
      print('❌ 获取每日塔罗数据失败: $e');
      return null;
    }
  }

  /// 获取显化日记
  Future<Map<String, dynamic>?> getManifestationJournal(String date) async {
    try {
      if (!isAuthenticated) return null;

      final response = await _supabase
          .from('daily_tarot')
          .select('manifestation_journal')
          .eq('user_id', currentUserId!)
          .eq('date', date)
          .maybeSingle();

      if (response != null) {
        return {'journal': response['manifestation_journal'] ?? ''};
      }

      return null;
    } catch (e) {
      print('❌ 获取显化日记失败: $e');
      return null;
    }
  }

  // ============================================================================
  // 塔罗解读数据操作
  // ============================================================================
  
  /// 保存塔罗解读数据
  Future<void> saveTarotReading(Map<String, dynamic> readingData) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');

      print('🔄 准备插入数据到tarot_readings表...');
      print('📊 用户ID: $currentUserId');
      print('📊 数据键: ${readingData.keys.toList()}');

      final insertData = {
        'user_id': currentUserId,
        ...readingData,
        'created_at': DateTime.now().toIso8601String(),
      };

      print('📊 最终插入数据: ${insertData.keys.toList()}');

      final response = await _supabase.from('tarot_readings').insert(insertData);

      print('✅ 塔罗解读数据保存成功');
      print('📊 插入响应: $response');
    } catch (e) {
      print('❌ 塔罗解读数据保存失败: $e');
      print('❌ 错误类型: ${e.runtimeType}');
      print('❌ 错误详情: ${e.toString()}');
      rethrow;
    }
  }


  
  /// 获取用户的塔罗解读记录
  Future<List<Map<String, dynamic>>> getTarotReadings([int? limit]) async {
    try {
      if (!isAuthenticated) return [];
      
      var query = _supabase
          .from('tarot_readings')
          .select()
          .eq('user_id', currentUserId!)
          .order('created_at', ascending: false);
      
      if (limit != null) {
        query = query.limit(limit);
      }
      
      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      print('❌ 获取塔罗解读记录失败: $e');
      return [];
    }
  }

  /// 更新塔罗解读记录
  Future<void> updateTarotReading(String readingId, Map<String, dynamic> updateData) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');

      await _supabase
          .from('tarot_readings')
          .update(updateData)
          .eq('id', readingId)
          .eq('user_id', currentUserId!);

      print('✅ 塔罗解读记录更新成功');
    } catch (e) {
      print('❌ 塔罗解读记录更新失败: $e');
      rethrow;
    }
  }

  /// 更新塔罗解读的评分
  Future<void> updateTarotReadingRating(String readingId, Map<String, dynamic> ratingData) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      await _supabase
          .from('tarot_readings')
          .update(ratingData)
          .eq('id', readingId)
          .eq('user_id', currentUserId!);
      
      print('✅ 塔罗解读评分更新成功');
    } catch (e) {
      print('❌ 塔罗解读评分更新失败: $e');
      rethrow;
    }
  }

  // ============================================================================
  // 基础数据操作
  // ============================================================================
  
  /// 保存简单的键值对数据
  Future<void> saveSimpleData(String key, dynamic value) async {
    try {
      if (!isAuthenticated) throw Exception('用户未登录');
      
      final preferences = await getUserPreferences();
      preferences[key] = value;
      
      await saveUserPreferences(preferences);
      print('✅ 数据保存成功: $key');
    } catch (e) {
      print('❌ 数据保存失败: $e');
      rethrow;
    }
  }
  
  /// 获取简单的键值对数据
  Future<T?> getSimpleData<T>(String key) async {
    try {
      final preferences = await getUserPreferences();
      return preferences[key] as T?;
    } catch (e) {
      print('❌ 数据获取失败: $e');
      return null;
    }
  }
}
