import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:io';

enum BackgroundType {
  default_bg,
  custom
}

class BackgroundService extends ChangeNotifier {
  static const String _backgroundTypeKey = 'background_type';
  static const String _customBackgroundPathKey = 'custom_background_path';
  
  BackgroundType _currentType = BackgroundType.default_bg;
  String? _customBackgroundPath;
  
  BackgroundType get currentType => _currentType;
  String? get customBackgroundPath => _customBackgroundPath;
  
  // 获取当前背景路径
  String? get currentBackgroundPath {
    switch (_currentType) {
      case BackgroundType.default_bg:
        return null; // 使用默认背景
      case BackgroundType.custom:
        return _customBackgroundPath;
    }
  }
  
  // 是否使用自定义背景
  bool get isUsingCustomBackground => _currentType == BackgroundType.custom && _customBackgroundPath != null;
  
  BackgroundService() {
    _loadSettings();
  }
  
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 加载背景类型
      final typeString = prefs.getString(_backgroundTypeKey);
      if (typeString != null) {
        _currentType = BackgroundType.values.firstWhere(
          (e) => e.name == typeString,
          orElse: () => BackgroundType.default_bg,
        );
      }
      
      // 加载自定义背景路径
      _customBackgroundPath = prefs.getString(_customBackgroundPathKey);
      
      // 验证自定义背景文件是否存在
      if (_customBackgroundPath != null && !await File(_customBackgroundPath!).exists()) {
        _customBackgroundPath = null;
        _currentType = BackgroundType.default_bg;
        await _saveSettings();
      }
      
      notifyListeners();
    } catch (e) {
      print('❌ 背景设置加载失败: $e');
    }
  }
  
  Future<void> _saveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backgroundTypeKey, _currentType.name);
      
      if (_customBackgroundPath != null) {
        await prefs.setString(_customBackgroundPathKey, _customBackgroundPath!);
      } else {
        await prefs.remove(_customBackgroundPathKey);
      }
      
      print('✅ 背景设置保存成功: type=${_currentType.name}, path=$_customBackgroundPath');
    } catch (e) {
      print('❌ 背景设置保存失败: $e');
    }
  }
  
  // 设置为默认背景
  Future<void> setDefaultBackground() async {
    _currentType = BackgroundType.default_bg;
    await _saveSettings();
    notifyListeners();
  }
  
  // 设置自定义背景
  Future<void> setCustomBackground(String imagePath) async {
    try {
      // 验证文件是否存在
      if (await File(imagePath).exists()) {
        _currentType = BackgroundType.custom;
        _customBackgroundPath = imagePath;
        await _saveSettings();
        notifyListeners();
        print('✅ 自定义背景设置成功: $imagePath');
      } else {
        throw Exception('文件不存在: $imagePath');
      }
    } catch (e) {
      print('❌ 自定义背景设置失败: $e');
      rethrow;
    }
  }
  
  // 重置到默认设置
  Future<void> resetToDefault() async {
    _currentType = BackgroundType.default_bg;
    _customBackgroundPath = null;
    await _saveSettings();
    notifyListeners();
  }
} 