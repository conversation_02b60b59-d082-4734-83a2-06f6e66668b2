import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';

/// 用户数据管理服务
/// 负责在用户登录/登出时管理本地数据的隔离和清理
class UserDataManager {
  static const String _currentUserIdKey = 'current_user_id';
  
  /// 检查是否为新用户，如果是则清理旧数据
  static Future<void> checkAndClearDataForNewUser(String newUserId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUserId = prefs.getString(_currentUserIdKey);
      
      if (lastUserId != null && lastUserId != newUserId) {
        print('🔄 检测到用户切换: $lastUserId -> $newUserId');
        await clearAllUserData();
        print('✅ 已清理上一个用户的本地数据');
      }
      
      // 保存当前用户ID
      await prefs.setString(_currentUserIdKey, newUserId);
      print('✅ 已设置当前用户ID: $newUserId');
    } catch (e) {
      print('❌ 用户数据检查失败: $e');
    }
  }
  
  /// 清理所有用户相关的本地数据
  static Future<void> clearAllUserData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 订阅服务相关数据 (需要与SubscriptionService中的键名保持一致)
      const subscriptionKeys = [
        'subscription_tier',
        'subscription_period', 
        'subscription_expiry',
        'daily_usage_count',
        'weekly_usage_count',
        'last_usage_date',
        'last_week_start',
      ];
      
      // 应用状态相关数据
      const appStateKeys = [
        'total_sessions',
        'current_streak',
        'best_streak',
        'manifestation_practices',
        'reading_history',
        'daily_tarot_readings',
      ];
      
      // 通知设置相关数据
      const notificationKeys = [
        'notification_enabled',
        'daily_reminder_time',
        'weekly_summary_enabled',
        'sound_enabled',
        'vibration_enabled',
      ];
      
      // 其他用户数据
      const otherKeys = [
        'apple_user_id',
        'blur_level',
        'card_preferences',
        'invitation_code_used',
        'background_type',
      ];
      
      // 合并所有需要清理的键
      final allKeys = [
        ...subscriptionKeys,
        ...appStateKeys, 
        ...notificationKeys,
        ...otherKeys,
      ];
      
      // 清理数据
      for (final key in allKeys) {
        await prefs.remove(key);
      }
      
      print('✅ 已清理 ${allKeys.length} 个本地数据键');
    } catch (e) {
      print('❌ 清理用户数据失败: $e');
      rethrow;
    }
  }
  
  /// 登出时清理数据
  static Future<void> clearDataOnSignOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // 清理当前用户ID
      await prefs.remove(_currentUserIdKey);
      
      // 清理所有用户数据
      await clearAllUserData();
      
      print('✅ 登出时数据清理完成');
    } catch (e) {
      print('❌ 登出时数据清理失败: $e');
    }
  }
  
  /// 获取当前保存的用户ID
  static Future<String?> getCurrentUserId() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_currentUserIdKey);
    } catch (e) {
      print('❌ 获取当前用户ID失败: $e');
      return null;
    }
  }
  
  /// 创建用户特定的键名（用于需要区分用户的数据）
  static String createUserSpecificKey(String baseKey, String userId) {
    return '${baseKey}_$userId';
  }
} 