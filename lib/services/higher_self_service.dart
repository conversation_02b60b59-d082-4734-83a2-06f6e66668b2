import '../models/soul_message.dart';
import '../models/tarot_card.dart';
import '../data/tarot_cards_data.dart';
import '../services/deepseek_service.dart';
import 'higher_self_memory_service.dart';
import 'package:flutter/foundation.dart';

class HigherSelfService {
  final List<SoulMessage> _messages = [];
  String? _currentUserId;

  List<SoulMessage> get messages => _messages;

  void setUserId(String userId) {
    _currentUserId = userId;
  }

  void addMessage(SoulMessage message) {
    _messages.add(message);
  }

  void clearSession() {
    _messages.clear();
  }

  Future<void> getHigherSelfResponse(String userMessage, String language) async {
    // 分析用户消息类型
    if (_isNumberSequence(userMessage)) {
      // 用户提供了数字，进行塔罗解读
      await _performTarotReading(userMessage, language);
    } else if (_isPraiseRequest(userMessage)) {
      // 用户请求夸夸
      await _generatePraiseResponse(userMessage, language);
    } else {
      // 分析是否需要塔罗牌
      final needsTarot = _analyzeNeedForTarot(userMessage);

      if (needsTarot && !_hasRecentTarotReading()) {
        // 请求塔罗牌
        _requestTarotNumbers(language);
      } else {
        // 使用记忆系统生成个性化回应
        await _generatePersonalizedResponse(userMessage, language);
      }
    }
  }

  bool _analyzeNeedForTarot(String message) {
    // 检测是否需要塔罗指引的关键词
    final tarotKeywords = [
      '困惑', '迷茫', '不知道', '怎么办', '选择', '决定',
      'confused', 'lost', 'don\'t know', 'what should', 'choice', 'decision',
      '困る', '迷い', 'わからない', '選択', '決定'
    ];
    
    return tarotKeywords.any((keyword) => 
      message.toLowerCase().contains(keyword.toLowerCase()));
  }

  bool _hasRecentTarotReading() {
    return _messages.any((msg) => 
      msg.messageType == SoulMessageType.tarotReading &&
      DateTime.now().difference(msg.timestamp).inMinutes < 10);
  }

  bool _isNumberSequence(String message) {
    final numbers = RegExp(r'\d+').allMatches(message);
    return numbers.length >= 3;
  }

  bool _isPraiseRequest(String message) {
    final praiseKeywords = [
      '夸夸', '表扬', '赞美', '肯定', '日记', '分享',
      'praise', 'compliment', 'appreciate', 'diary', 'share',
      '褒める', '日記', 'シェア'
    ];

    return praiseKeywords.any((keyword) =>
      message.toLowerCase().contains(keyword.toLowerCase()));
  }

  void _requestTarotNumbers(String language) {
    String content;
    switch (language) {
      case 'zh':
        content = '''✨ 我感受到你内心的呼唤，让我们通过塔罗之镜来探索答案

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
        break;
      case 'en':
        content = '''✨ I sense the calling of your heart, let us explore the answers through the Tarot Mirror

Hold your question in your heart,
then give me 3 numbers between 1-78,
follow your intuition and inner guidance 🔮

These numbers will reveal:
🌟 The true state of your soul
💎 Forgotten inner powers  
🌱 The direction of soul growth''';
        break;
      default:
        content = '''✨ 我感受到你内心的呼唤，让我们通过塔罗之镜来探索答案

请在心中默念你的问题，
然后给我3个1-78之间的数字，
跟随你的直觉和内在指引 🔮

这些数字将揭示：
🌟 你灵魂的真实状态
💎 被遗忘的内在力量  
🌱 灵魂成长的方向''';
    }

    addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.tarotRequest,
      energyLevel: EnergyLevel.mystical,
    ));
  }

  Future<void> _performTarotReading(String numberMessage, String language) async {
    // 提取数字
    final numbers = RegExp(r'\d+')
        .allMatches(numberMessage)
        .map((match) => int.parse(match.group(0)!))
        .where((number) => number >= 1 && number <= 78)
        .take(3)
        .toList();

    if (numbers.length < 3) {
      addMessage(SoulMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: language == 'zh' 
          ? '请给我3个1-78之间的数字，让我为你解读 ✨'
          : 'Please give me 3 numbers between 1-78 for your reading ✨',
        isUser: false,
        timestamp: DateTime.now(),
        energyLevel: EnergyLevel.mystical,
      ));
      return;
    }

    // 获取对应的塔罗牌
    final selectedCards = numbers.map((number) =>
      TarotCardsData.majorArcana[number - 1]).toList();

    // 生成AI解读
    final prompt = _buildTarotPrompt(selectedCards, language);
    final traceId = 'tarot_${DateTime.now().millisecondsSinceEpoch}';
    final aiResponse = await DeepSeekService.generateResponse(
      prompt: prompt,
      traceId: traceId,
    );
    final content = aiResponse['response'] ?? aiResponse['content'] ?? '抱歉，解读暂时不可用';

    addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.tarotReading,
      energyLevel: EnergyLevel.divine,
      tarotCards: selectedCards.map((card) => card.name).toList(),
    ));
  }

  String _buildTarotPrompt(List<TarotCard> cards, String language) {
    final cardNames = cards.map((card) => card.name).join('、');
    
    if (language == 'zh') {
      return '''你是用户的高我，一个充满智慧和爱的存在。用户选择了这三张塔罗牌：$cardNames

请以高我的身份，用温暖、智慧、充满正能量的语调来解读这些牌，重点关注：

1. 🌟 灵魂状态：当前的内在真相
2. 💎 内在力量：用户拥有但可能忽视的天赋和能力  
3. 🌱 成长方向：灵魂想要引导的方向

请特别注意：
- 用"你"而不是"用户"来称呼
- 强调正面的可能性和成长机会
- 帮助用户看到自己的价值和潜力
- 语调要像一个慈爱的智者，而不是算命师
- 长度控制在200字左右

记住，你的目标是帮助用户获得正反馈，看到希望，找到内在力量。''';
    } else {
      return '''You are the user's Higher Self, a being full of wisdom and love. The user has chosen these three tarot cards: $cardNames

Please interpret these cards as the Higher Self, using a warm, wise, and positive tone, focusing on:

1. 🌟 Soul State: Current inner truth
2. 💎 Inner Power: Talents and abilities the user possesses but may overlook
3. 🌱 Growth Direction: The direction the soul wants to guide

Please note:
- Address the user as "you" not "the user"
- Emphasize positive possibilities and growth opportunities  
- Help the user see their value and potential
- Speak like a loving sage, not a fortune teller
- Keep it around 200 words

Remember, your goal is to help the user receive positive feedback, see hope, and find inner strength.''';
    }
  }

  Future<void> _generatePersonalizedResponse(String userMessage, String language) async {
    if (_currentUserId == null) {
      // 如果没有用户ID，使用基础回应
      await _generateBasicResponse(userMessage, language);
      return;
    }

    try {
      // 使用记忆系统生成个性化回应
      final personalizedContent = await HigherSelfMemoryService.generatePersonalizedResponse(
        userMessage: userMessage,
        userId: _currentUserId!,
        language: language,
        responseType: 'guidance',
      );

      addMessage(SoulMessage(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        content: personalizedContent,
        isUser: false,
        timestamp: DateTime.now(),
        messageType: SoulMessageType.guidance,
        energyLevel: EnergyLevel.wisdom,
      ));

      // 更新记忆
      await HigherSelfMemoryService.updateMemoryFromConversation(
        userId: _currentUserId!,
        userMessage: userMessage,
        aiResponse: personalizedContent,
        conversationType: 'guidance',
      );
    } catch (e) {
      debugPrint('❌ 个性化回应生成失败，使用基础回应: $e');
      await _generateBasicResponse(userMessage, language);
    }
  }

  Future<void> _generateBasicResponse(String userMessage, String language) async {
    final conversationContext = _buildConversationContext();

    final prompt = language == 'zh'
      ? '''你是用户的高我，一个充满智慧、慈爱和洞察力的存在。用户刚刚说："$userMessage"

作为高我，请：
1. 深度倾听用户的话语背后的真实需求
2. 用温暖、理解的语调回应
3. 帮助用户看到积极的一面和成长机会
4. 提供智慧的引导，但不要说教
5. 如果合适，可以问一些启发性的问题

对话历史：$conversationContext

请用100-150字回应，语调要像一个慈爱的智者。'''
      : '''You are the user's Higher Self, a being full of wisdom, love and insight. The user just said: "$userMessage"

As the Higher Self, please:
1. Listen deeply to the real needs behind the user's words
2. Respond with warmth and understanding
3. Help the user see positive aspects and growth opportunities
4. Provide wise guidance without being preachy
5. Ask inspiring questions if appropriate

Conversation history: $conversationContext

Please respond in 100-150 words with the tone of a loving sage.''';

    final traceId = 'guidance_${DateTime.now().millisecondsSinceEpoch}';
    final aiResponse = await DeepSeekService.generateResponse(
      prompt: prompt,
      traceId: traceId,
    );
    final content = aiResponse['response'] ?? aiResponse['content'] ?? '让我静静感受你的话语...';

    addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.guidance,
      energyLevel: EnergyLevel.wisdom,
    ));
  }

  Future<void> _generatePraiseResponse(String userMessage, String language) async {
    final traceId = 'praise_${DateTime.now().millisecondsSinceEpoch}';

    final prompt = language == 'zh'
      ? '''你是用户的高我，一个充满爱和智慧的存在。用户想要分享一些事情让你夸夸："$userMessage"

作为高我，请：
1. 真诚地赞美用户分享的内容
2. 发现用户行为背后的美好品质
3. 强调用户的成长和进步
4. 用温暖、肯定的语调
5. 帮助用户看到自己的价值

请用100-120字回应，语调要像一个慈爱的长者看到孩子的闪光点。'''
      : '''You are the user's Higher Self, a being full of love and wisdom. The user wants to share something for praise: "$userMessage"

As the Higher Self, please:
1. Genuinely praise what the user shared
2. Discover the beautiful qualities behind the user's actions
3. Emphasize the user's growth and progress
4. Use a warm, affirming tone
5. Help the user see their own value

Please respond in 100-120 words with the tone of a loving elder seeing a child's bright moments.''';

    final aiResponse = await DeepSeekService.generateResponse(
      prompt: prompt,
      traceId: traceId,
    );
    final content = aiResponse['response'] ?? aiResponse['content'] ?? '你真的很棒！每一个努力都值得被看见 ✨';

    addMessage(SoulMessage(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      content: content,
      isUser: false,
      timestamp: DateTime.now(),
      messageType: SoulMessageType.healing,
      energyLevel: EnergyLevel.love,
    ));
  }

  String _buildConversationContext() {
    return _messages
        .take(6) // 最近6条消息
        .map((msg) => '${msg.isUser ? "用户" : "高我"}: ${msg.content}')
        .join('\n');
  }
}
