import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ai_tarot_reading/services/ai_tarot_specialists.dart';

/// 显化任务和日记管理服务
class ManifestationService {
  static const String _tasksKey = 'manifestation_tasks';
  static const String _journalKey = 'journal_entries';
  static const String _progressKey = 'task_progress';
  
  /// 保存显化任务
  static Future<void> saveManifestationTasks(List<ManifestationTask> tasks) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksJson = tasks.map((task) => {
        'title': task.title,
        'description': task.description,
        'duration': task.duration,
        'frequency': task.frequency,
        'createdAt': DateTime.now().toIso8601String(),
      }).toList();
      
      await prefs.setString(_tasksKey, jsonEncode(tasksJson));
      print('✅ 显化任务已保存: ${tasks.length}个任务');
    } catch (e) {
      print('❌ 保存显化任务失败: $e');
    }
  }
  
  /// 获取当前显化任务
  static Future<List<ManifestationTaskWithProgress>> getCurrentTasks() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tasksString = prefs.getString(_tasksKey);
      final progressString = prefs.getString(_progressKey);
      
      if (tasksString == null) return [];
      
      final tasksData = jsonDecode(tasksString) as List;
      final progressData = progressString != null 
          ? jsonDecode(progressString) as Map<String, dynamic>
          : <String, dynamic>{};
      
      return tasksData.map((taskData) {
        final task = ManifestationTask(
          title: taskData['title'],
          description: taskData['description'], 
          duration: taskData['duration'],
          frequency: taskData['frequency'],
        );
        
        final createdAt = DateTime.parse(taskData['createdAt']);
        final taskKey = _getTaskKey(task.title, createdAt);
        final progress = progressData[taskKey] as Map<String, dynamic>?;
        
        return ManifestationTaskWithProgress(
          task: task,
          createdAt: createdAt,
          completedDays: Set<String>.from(progress?['completedDays'] ?? []),
          totalDays: _calculateTotalDays(createdAt, task.duration),
          isActive: _isTaskActive(createdAt, task.duration),
        );
      }).toList();
    } catch (e) {
      print('❌ 获取显化任务失败: $e');
      return [];
    }
  }
  
  /// 标记任务完成
  static Future<void> markTaskCompleted(String taskTitle, DateTime taskCreatedAt) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final progressString = prefs.getString(_progressKey) ?? '{}';
      final progressData = jsonDecode(progressString) as Map<String, dynamic>;
      
      final taskKey = _getTaskKey(taskTitle, taskCreatedAt);
      final today = DateTime.now().toIso8601String().substring(0, 10);
      
      if (progressData[taskKey] == null) {
        progressData[taskKey] = {'completedDays': []};
      }
      
      final completedDays = Set<String>.from(progressData[taskKey]['completedDays'] ?? []);
      completedDays.add(today);
      progressData[taskKey]['completedDays'] = completedDays.toList();
      
      await prefs.setString(_progressKey, jsonEncode(progressData));
      print('✅ 任务标记完成: $taskTitle - $today');
    } catch (e) {
      print('❌ 标记任务完成失败: $e');
    }
  }
  
  /// 保存日记条目
  static Future<void> saveJournalEntry(String content, List<String> prompts) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final journalString = prefs.getString(_journalKey) ?? '[]';
      final journalData = jsonDecode(journalString) as List;
      
      final entry = {
        'content': content,
        'prompts': prompts,
        'date': DateTime.now().toIso8601String(),
        'wordCount': content.split(' ').length,
      };
      
      journalData.insert(0, entry); // 最新的在前面
      
      // 只保留最近100条记录
      if (journalData.length > 100) {
        journalData.removeRange(100, journalData.length);
      }
      
      await prefs.setString(_journalKey, jsonEncode(journalData));
      print('✅ 日记条目已保存: ${content.length}字符');
    } catch (e) {
      print('❌ 保存日记失败: $e');
    }
  }
  
  /// 获取日记条目
  static Future<List<JournalEntry>> getJournalEntries({int limit = 20}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final journalString = prefs.getString(_journalKey) ?? '[]';
      final journalData = jsonDecode(journalString) as List;
      
      return journalData.take(limit).map((entryData) => JournalEntry(
        content: entryData['content'],
        prompts: List<String>.from(entryData['prompts'] ?? []),
        date: DateTime.parse(entryData['date']),
        wordCount: entryData['wordCount'] ?? 0,
      )).toList();
    } catch (e) {
      print('❌ 获取日记条目失败: $e');
      return [];
    }
  }
  
  /// 获取进步统计
  static Future<ProgressStats> getProgressStats() async {
    try {
      final tasks = await getCurrentTasks();
      final journals = await getJournalEntries(limit: 100);
      
      int totalTasksCompleted = 0;
      int activeTasks = 0;
      double averageCompletion = 0.0;
      
      for (final taskWithProgress in tasks) {
        if (taskWithProgress.isActive) {
          activeTasks++;
          totalTasksCompleted += taskWithProgress.completedDays.length;
          averageCompletion += taskWithProgress.completionPercentage;
        }
      }
      
      if (activeTasks > 0) {
        averageCompletion /= activeTasks;
      }
      
      // 本周日记条目数
      final thisWeek = DateTime.now().subtract(Duration(days: 7));
      final journalsThisWeek = journals.where((journal) => 
          journal.date.isAfter(thisWeek)).length;
      
      // 总字数
      final totalWords = journals.fold(0, (sum, journal) => sum + journal.wordCount);
      
      return ProgressStats(
        activeTasks: activeTasks,
        totalTasksCompleted: totalTasksCompleted,
        averageCompletion: averageCompletion,
        journalEntriesThisWeek: journalsThisWeek,
        totalJournalEntries: journals.length,
        totalWordsWritten: totalWords,
        streakDays: _calculateStreak(journals),
      );
    } catch (e) {
      print('❌ 获取进步统计失败: $e');
      return ProgressStats(
        activeTasks: 0,
        totalTasksCompleted: 0,
        averageCompletion: 0.0,
        journalEntriesThisWeek: 0,
        totalJournalEntries: 0,
        totalWordsWritten: 0,
        streakDays: 0,
      );
    }
  }
  
  /// 清理过期任务
  static Future<void> cleanExpiredTasks() async {
    try {
      final tasks = await getCurrentTasks();
      final activeTasks = tasks.where((task) => task.isActive).toList();
      
      if (activeTasks.length < tasks.length) {
        await saveManifestationTasks(activeTasks.map((t) => t.task).toList());
        print('✅ 清理了${tasks.length - activeTasks.length}个过期任务');
      }
    } catch (e) {
      print('❌ 清理过期任务失败: $e');
    }
  }
  
  // 辅助方法
  static String _getTaskKey(String title, DateTime createdAt) {
    return '${title}_${createdAt.toIso8601String().substring(0, 10)}';
  }
  
  static int _calculateTotalDays(DateTime createdAt, int duration) {
    final daysPassed = DateTime.now().difference(createdAt).inDays + 1;
    return daysPassed > duration ? duration : daysPassed;
  }
  
  static bool _isTaskActive(DateTime createdAt, int duration) {
    final daysPassed = DateTime.now().difference(createdAt).inDays;
    return daysPassed < duration;
  }
  
  static int _calculateStreak(List<JournalEntry> journals) {
    if (journals.isEmpty) return 0;
    
    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    // 从今天开始往前算连续天数
    for (int i = 0; i < 30; i++) { // 最多检查30天
      final checkDate = currentDate.subtract(Duration(days: i));
      final dateString = checkDate.toIso8601String().substring(0, 10);
      
      bool hasEntry = journals.any((journal) => 
          journal.date.toIso8601String().substring(0, 10) == dateString);
      
      if (hasEntry) {
        streak++;
      } else if (i > 0) { // 第一天没写不算打断
        break;
      }
    }
    
    return streak;
  }
}

/// 带进度的显化任务
class ManifestationTaskWithProgress {
  final ManifestationTask task;
  final DateTime createdAt;
  final Set<String> completedDays;
  final int totalDays;
  final bool isActive;
  
  ManifestationTaskWithProgress({
    required this.task,
    required this.createdAt,
    required this.completedDays,
    required this.totalDays,
    required this.isActive,
  });
  
  double get completionPercentage {
    if (totalDays == 0) return 0.0;
    return (completedDays.length / totalDays).clamp(0.0, 1.0);
  }
  
  int get remainingDays {
    final daysPassed = DateTime.now().difference(createdAt).inDays;
    return (task.duration - daysPassed).clamp(0, task.duration);
  }
  
  bool get isCompletedToday {
    final today = DateTime.now().toIso8601String().substring(0, 10);
    return completedDays.contains(today);
  }
}

/// 日记条目
class JournalEntry {
  final String content;
  final List<String> prompts;
  final DateTime date;
  final int wordCount;
  
  JournalEntry({
    required this.content,
    required this.prompts,
    required this.date,
    required this.wordCount,
  });
}

/// 进步统计
class ProgressStats {
  final int activeTasks;
  final int totalTasksCompleted;
  final double averageCompletion;
  final int journalEntriesThisWeek;
  final int totalJournalEntries;
  final int totalWordsWritten;
  final int streakDays;
  
  ProgressStats({
    required this.activeTasks,
    required this.totalTasksCompleted,
    required this.averageCompletion,
    required this.journalEntriesThisWeek,
    required this.totalJournalEntries,
    required this.totalWordsWritten,
    required this.streakDays,
  });
} 