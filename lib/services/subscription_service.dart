import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/services/user_data_manager.dart';

enum SubscriptionTier {
  free,      // Free: 1 single card reading per week
  basic,     // Basic Member: 1 reading per day, all spreads, $2.99/week
  premium    // Premium Member: 5 readings per day, all spreads, $4.99/week
}

enum SubscriptionPeriod {
  weekly,    // Weekly subscription
  monthly,   // Monthly subscription (20% off)
  yearly     // Annual subscription (40% off)
}

class SubscriptionService extends ChangeNotifier {
  // 产品ID定义（美元定价）
  static const String _basicWeeklyId = 'com.G3RHCPDDQR.aitarotreading.basic_weekly_usd';
  static const String _basicMonthlyId = 'com.G3RHCPDDQR.aitarotreading.basic_monthly_usd';
  static const String _basicYearlyId = 'com.G3RHCPDDQR.aitarotreading.basic_yearly_usd';
  static const String _premiumWeeklyId = 'com.G3RHCPDDQR.aitarotreading.p_weekly_usd';
  static const String _premiumMonthlyId = 'com.G3RHCPDDQR.aitarotreading.p_monthly_usd';
  static const String _premiumYearlyId = 'com.G3RHCPDDQR.aitarotreading.p_yearly_usd';

  // 🧪 TestFlight测试模式 - 临时禁用订阅功能
  static const bool _isTestFlightMode = false;
  
  static const String _subscriptionTierKey = 'subscription_tier';
  static const String _subscriptionPeriodKey = 'subscription_period';
  static const String _subscriptionExpiryKey = 'subscription_expiry';
  static const String _dailyUsageCountKey = 'daily_usage_count';
  static const String _weeklyUsageCountKey = 'weekly_usage_count';
  static const String _lastUsageDateKey = 'last_usage_date';
  static const String _lastWeekStartKey = 'last_week_start';
  
  // 获取当前用户ID
  String? get _currentUserId => Supabase.instance.client.auth.currentUser?.id;
  
  // 生成用户特定的键名
  String _getUserSpecificKey(String baseKey) {
    final userId = _currentUserId;
    if (userId != null) {
      return UserDataManager.createUserSpecificKey(baseKey, userId);
    }
    // ❌ 修复：如果没有用户ID，使用临时设备键，避免数据污染
    return 'temp_device_$baseKey'; 
  }
  
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  
  bool _isAvailable = false;
  SubscriptionTier _currentTier = SubscriptionTier.free;
  SubscriptionPeriod _currentPeriod = SubscriptionPeriod.weekly;
  List<ProductDetails> _products = [];
  bool _isLoading = false;
  String? _errorMessage;
  int _dailyUsageCount = 0;
  
  // Getters
  bool get isAvailable => _isAvailable;
  SubscriptionTier get currentTier => _currentTier;
  SubscriptionPeriod get currentPeriod => _currentPeriod;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  List<ProductDetails> get products => _products;
  int get dailyUsageCount => _dailyUsageCount;
  
  // 会员权限检查
  bool get isSubscribed => _currentTier != SubscriptionTier.free;
  bool get isPremium => _currentTier == SubscriptionTier.premium;
  bool get isBasic => _currentTier == SubscriptionTier.basic;
  
  // 使用次数限制
  int get usageLimit {
    switch (_currentTier) {
      case SubscriptionTier.free:
        return 1; // 每周1次
      case SubscriptionTier.basic:
        return 1; // 每天1次
      case SubscriptionTier.premium:
        return 5; // 每天5次
    }
  }
  
  // 是否按周限制（免费会员）
  bool get isWeeklyLimit => _currentTier == SubscriptionTier.free;
  
  bool get canUseToday {
    if (isWeeklyLimit) {
      // 免费会员按周限制
      return _weeklyUsageCount < usageLimit;
    } else {
      // 付费会员按日限制
      return _dailyUsageCount < usageLimit;
    }
  }
  
  // 添加周使用次数
  int _weeklyUsageCount = 0;
  int get weeklyUsageCount => _weeklyUsageCount;
  
  // 获取剩余免费使用次数
  int get remainingFreeUsage {
    if (isWeeklyLimit) {
      // 免费会员按周限制
      return (usageLimit - _weeklyUsageCount).clamp(0, usageLimit);
    } else {
      // 付费会员按日限制
      return (usageLimit - _dailyUsageCount).clamp(0, usageLimit);
    }
  }
  
  SubscriptionService() {
    _initialize();
  }
  
  Future<void> _initialize() async {
    debugPrint('🚀 开始初始化订阅服务...');

    _isAvailable = await _inAppPurchase.isAvailable();
    debugPrint('📱 应用内购买可用性: $_isAvailable');

    if (_isAvailable) {
      debugPrint('✅ 应用内购买可用，开始设置...');

      // 监听购买更新
      _subscription = _inAppPurchase.purchaseStream.listen(
        _onPurchaseUpdate,
        onDone: () => _subscription.cancel(),
        onError: (error) => _handleError('购买监听错误: $error'),
      );
      debugPrint('✅ 购买流监听已设置');

      // 加载产品信息
      debugPrint('🔍 开始加载产品信息...');
      await _loadProducts();

      // 检查当前订阅状态
      debugPrint('🔍 检查当前订阅状态...');
      await _checkSubscriptionStatus();

      // ✅ 新增：检查并重置新用户数据
      debugPrint('🔍 检查新用户数据...');
      await _checkAndResetForNewUser();

      // 检查每日和每周使用次数
      debugPrint('🔍 检查使用次数...');
      await _checkDailyUsage();
      await _checkWeeklyUsage();

      // 恢复之前的购买
      debugPrint('🔍 恢复之前的购买...');
      await restorePurchases();

      debugPrint('✅ 订阅服务初始化完成');
    } else {
      debugPrint('❌ 应用内购买不可用');
      _handleError('应用内购买功能不可用，请检查设备设置');
    }

    notifyListeners();
  }
  
  Future<void> _loadProducts() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      const Set<String> productIds = {
        _basicWeeklyId,
        _basicMonthlyId,
        _basicYearlyId,
        _premiumWeeklyId,
        _premiumMonthlyId,
        _premiumYearlyId,
      };
      
      final ProductDetailsResponse response = await _inAppPurchase.queryProductDetails(productIds);
      
      debugPrint('🔍 查询产品结果:');
      debugPrint('✅ 找到的产品: ${response.productDetails.map((p) => p.id).toList()}');
      debugPrint('❌ 未找到的产品: ${response.notFoundIDs}');
      
      if (response.notFoundIDs.isNotEmpty) {
        _handleError('找不到产品: ${response.notFoundIDs.join(", ")}\n请确保在 App Store Connect 中正确配置了这些订阅产品');
      }
      
      _products = response.productDetails;
      _isLoading = false;
      notifyListeners();
    } catch (e) {
      _handleError('加载产品失败: $e');
    }
  }
  
  // 购买指定套餐
  Future<void> purchaseSubscription(SubscriptionTier tier, SubscriptionPeriod period) async {
    if (!_isAvailable) {
      _handleError('订阅服务不可用');
      return;
    }
    
    try {
      _isLoading = true;
      _errorMessage = null;
      notifyListeners();
      
      final String productId = _getProductId(tier, period);
      final ProductDetails? productDetails = _products.where((p) => p.id == productId).firstOrNull;
      
      debugPrint('🔍 尝试购买产品: $productId');
      debugPrint('🔍 可用产品列表: ${_products.map((p) => p.id).toList()}');
      
      if (productDetails == null) {
        _handleError('找不到对应的产品: $productId\n\n可能原因:\n1. App Store Connect 中未创建此产品\n2. 产品状态不是"Ready for Sale"\n3. 产品ID拼写错误');
        return;
      }
      
      debugPrint('✅ 找到产品: ${productDetails.title} - ${productDetails.price}');
      
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
      );
      
      // 对于订阅产品，仍使用 buyNonConsumable
      // 关键是产品在 App Store Connect 中要配置为 Subscription 类型
      await _inAppPurchase.buyNonConsumable(purchaseParam: purchaseParam);
    } catch (e) {
      _handleError('购买失败: $e');
    }
  }
  
  String _getProductId(SubscriptionTier tier, SubscriptionPeriod period) {
    switch (tier) {
      case SubscriptionTier.basic:
        switch (period) {
          case SubscriptionPeriod.weekly:
            return _basicWeeklyId;
          case SubscriptionPeriod.monthly:
            return _basicMonthlyId;
          case SubscriptionPeriod.yearly:
            return _basicYearlyId;
        }
      case SubscriptionTier.premium:
        switch (period) {
          case SubscriptionPeriod.weekly:
            return _premiumWeeklyId;
          case SubscriptionPeriod.monthly:
            return _premiumMonthlyId;
          case SubscriptionPeriod.yearly:
            return _premiumYearlyId;
        }
      case SubscriptionTier.free:
        return '';
    }
  }
  
  // 获取产品价格
  String getPrice(SubscriptionTier tier, SubscriptionPeriod period) {
    final String productId = _getProductId(tier, period);
    final ProductDetails? product = _products.where((p) => p.id == productId).firstOrNull;
    
    if (product != null) {
      return product.price;
    }
    
         // 默认价格（美元）
     switch (tier) {
       case SubscriptionTier.basic:
         switch (period) {
           case SubscriptionPeriod.weekly:
             return '\$2.99';
           case SubscriptionPeriod.monthly:
             return '\$9.99'; // 2.99 * 4 * 0.8
           case SubscriptionPeriod.yearly:
             return '\$89.99'; // 2.99 * 52 * 0.6
         }
       case SubscriptionTier.premium:
         switch (period) {
           case SubscriptionPeriod.weekly:
             return '\$4.99';
           case SubscriptionPeriod.monthly:
             return '\$17.99'; // 4.99 * 4 * 0.8
           case SubscriptionPeriod.yearly:
             return '\$149.99'; // 4.99 * 52 * 0.6
         }
       case SubscriptionTier.free:
         return 'Free';
     }
  }
  
  // 获取优惠信息
  String getDiscountText(SubscriptionPeriod period) {
    switch (period) {
      case SubscriptionPeriod.weekly:
        return '';
      case SubscriptionPeriod.monthly:
        return '月度8折优惠';
      case SubscriptionPeriod.yearly:
        return '年度6折优惠';
    }
  }
  
  // 记录使用次数
  Future<void> recordUsage() async {
    await _checkDailyUsage(); // 先检查是否是新的一天
    await _checkWeeklyUsage(); // 检查是否是新的一周
    
    if (!canUseToday) {
      if (isWeeklyLimit) {
        throw Exception('本周使用次数已达上限');
      } else {
        throw Exception('今日使用次数已达上限');
      }
    }
    
    final prefs = await SharedPreferences.getInstance();
    
    if (isWeeklyLimit) {
      // 免费会员记录周使用次数
      _weeklyUsageCount++;
      await prefs.setInt(_getUserSpecificKey(_weeklyUsageCountKey), _weeklyUsageCount);
    } else {
      // 付费会员记录日使用次数
      _dailyUsageCount++;
      await prefs.setInt(_getUserSpecificKey(_dailyUsageCountKey), _dailyUsageCount);
    }
    
    await prefs.setString(_getUserSpecificKey(_lastUsageDateKey), DateTime.now().toIso8601String());
    
    notifyListeners();
  }
  
  Future<void> _checkDailyUsage() async {
    final prefs = await SharedPreferences.getInstance();
    final lastUsageString = prefs.getString(_getUserSpecificKey(_lastUsageDateKey));
    final today = DateTime.now();
    
    if (lastUsageString != null) {
      final lastUsageDate = DateTime.parse(lastUsageString);
      if (!_isSameDay(today, lastUsageDate)) {
        // 新的一天，重置计数
        _dailyUsageCount = 0;
        await prefs.setInt(_getUserSpecificKey(_dailyUsageCountKey), 0);
      } else {
        // 同一天，加载已使用次数
        _dailyUsageCount = prefs.getInt(_getUserSpecificKey(_dailyUsageCountKey)) ?? 0;
      }
    } else {
      // 首次使用
      _dailyUsageCount = 0;
    }
  }
  
  Future<void> _checkWeeklyUsage() async {
    final prefs = await SharedPreferences.getInstance();
    final lastWeekStartString = prefs.getString(_getUserSpecificKey(_lastWeekStartKey));
    final today = DateTime.now();
    final weekStart = _getWeekStart(today);
    
    if (lastWeekStartString != null) {
      final lastWeekStart = DateTime.parse(lastWeekStartString);
      if (!_isSameWeek(weekStart, lastWeekStart)) {
        // 新的一周，重置计数
        _weeklyUsageCount = 0;
        await prefs.setInt(_getUserSpecificKey(_weeklyUsageCountKey), 0);
        await prefs.setString(_getUserSpecificKey(_lastWeekStartKey), weekStart.toIso8601String());
      } else {
        // 同一周，加载已使用次数
        _weeklyUsageCount = prefs.getInt(_getUserSpecificKey(_weeklyUsageCountKey)) ?? 0;
      }
    } else {
      // 首次使用
      _weeklyUsageCount = 0;
      await prefs.setString(_getUserSpecificKey(_lastWeekStartKey), weekStart.toIso8601String());
    }
  }

  // ✅ 新增：检查并重置新用户数据
  Future<void> _checkAndResetForNewUser() async {
    final userId = _currentUserId;
    if (userId == null) {
      print('⚠️ 用户ID为空，跳过新用户检查');
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();
      final userCheckKey = 'user_data_checked_$userId';
      
      // 检查是否已经为这个用户初始化过数据
      final hasChecked = prefs.getBool(userCheckKey) ?? false;
      
      if (!hasChecked) {
        print('🔄 检测到新用户，重置使用次数数据: $userId');
        
        // 重置所有使用次数相关数据
        _dailyUsageCount = 0;
        _weeklyUsageCount = 0;
        
        // 清除本地存储
        await prefs.remove(_getUserSpecificKey(_dailyUsageCountKey));
        await prefs.remove(_getUserSpecificKey(_weeklyUsageCountKey));
        await prefs.remove(_getUserSpecificKey(_lastUsageDateKey));
        await prefs.remove(_getUserSpecificKey(_lastWeekStartKey));
        
        // 标记已检查
        await prefs.setBool(userCheckKey, true);
        
        print('✅ 新用户数据重置完成');
      }
    } catch (e) {
      print('❌ 新用户检查失败: $e');
    }
  }
  
  DateTime _getWeekStart(DateTime date) {
    // 获取本周的开始日期（周一）
    final weekday = date.weekday;
    return date.subtract(Duration(days: weekday - 1));
  }
  
  bool _isSameWeek(DateTime date1, DateTime date2) {
    final week1Start = _getWeekStart(date1);
    final week2Start = _getWeekStart(date2);
    return _isSameDay(week1Start, week2Start);
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
  
  Future<void> restorePurchases() async {
    try {
      _isLoading = true;
      notifyListeners();
      
      await _inAppPurchase.restorePurchases();
    } catch (e) {
      _handleError('Failed to restore purchases: $e');
    }
  }
  
  void _onPurchaseUpdate(List<PurchaseDetails> purchaseDetailsList) {
    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      _handlePurchase(purchaseDetails);
    }
  }
  
  Future<void> _handlePurchase(PurchaseDetails purchaseDetails) async {
    try {
      if (purchaseDetails.status == PurchaseStatus.purchased ||
          purchaseDetails.status == PurchaseStatus.restored) {
        
        // 验证购买
        final bool isValid = await _verifyPurchase(purchaseDetails);
        
        if (isValid) {
          await _activateSubscription(purchaseDetails);
        }
        
        // 完成购买
        if (purchaseDetails.pendingCompletePurchase) {
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      } else if (purchaseDetails.status == PurchaseStatus.error) {
        final errorMessage = _getLocalizedErrorMessage(purchaseDetails.error);
        _handleError(errorMessage);
      } else if (purchaseDetails.status == PurchaseStatus.canceled) {
        _handleError('用户取消了购买');
      }
    } catch (e) {
      _handleError('Failed to process purchase: $e');
    }
    
    _isLoading = false;
    notifyListeners();
  }
  
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    try {
      debugPrint('🔍 开始验证购买收据...');

      // 获取收据数据
      String? receiptData;

      if (Platform.isIOS) {
        // iOS: 从StoreKit获取收据数据
        if (purchaseDetails.verificationData.serverVerificationData.isNotEmpty) {
          receiptData = purchaseDetails.verificationData.serverVerificationData;
        } else {
          debugPrint('❌ 无法获取iOS收据数据');
          return false;
        }
      } else {
        debugPrint('❌ 当前只支持iOS收据验证');
        return false;
      }

      if (receiptData.isEmpty) {
        debugPrint('❌ 收据数据为空');
        return false;
      }

      // 调用Supabase Edge Function验证收据
      final response = await Supabase.instance.client.functions.invoke(
        'swift-responder',  // 使用你部署的函数名称
        body: {
          'receiptData': receiptData,
          // 如果有App Store Connect共享密钥，可以在这里添加
          // 'password': 'your_shared_secret'
        },
      );

      if (response.status == 200 && response.data != null) {
        final verificationResult = response.data as Map<String, dynamic>;
        final bool isValid = verificationResult['success'] == true;
        final String? environment = verificationResult['environment'];

        if (isValid) {
          debugPrint('✅ 收据验证成功 - 环境: $environment');
          return true;
        } else {
          final String? error = verificationResult['error'];
          debugPrint('❌ 收据验证失败: $error');
          _handleError('收据验证失败: $error');
          return false;
        }
      } else {
        debugPrint('❌ 收据验证服务调用失败: ${response.status}');
        _handleError('收据验证服务不可用');
        return false;
      }


    } catch (e) {
      debugPrint('❌ 收据验证异常: $e');
      // 临时：即使出错也返回true
      debugPrint('⚠️ 验证异常，临时允许购买继续');
      return true;
    }
  }
  
  Future<void> _activateSubscription(PurchaseDetails purchaseDetails) async {
    final productId = purchaseDetails.productID;
    
    // 解析产品ID确定订阅等级和周期
    SubscriptionTier tier = SubscriptionTier.free;
    SubscriptionPeriod period = SubscriptionPeriod.weekly;
    
    if (productId.contains('basic')) {
      tier = SubscriptionTier.basic;
    } else if (productId.contains('premium')) {
      tier = SubscriptionTier.premium;
    }
    
    if (productId.contains('weekly')) {
      period = SubscriptionPeriod.weekly;
    } else if (productId.contains('monthly')) {
      period = SubscriptionPeriod.monthly;
    } else if (productId.contains('yearly')) {
      period = SubscriptionPeriod.yearly;
    }
    
    _currentTier = tier;
    _currentPeriod = period;
    
    // 保存订阅状态到本地
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_getUserSpecificKey(_subscriptionTierKey), tier.name);
    await prefs.setString(_getUserSpecificKey(_subscriptionPeriodKey), period.name);
    
    // 设置过期时间
    DateTime expiryTime;
    switch (period) {
      case SubscriptionPeriod.weekly:
        expiryTime = DateTime.now().add(const Duration(days: 7));
        break;
      case SubscriptionPeriod.monthly:
        expiryTime = DateTime.now().add(const Duration(days: 30));
        break;
      case SubscriptionPeriod.yearly:
        expiryTime = DateTime.now().add(const Duration(days: 365));
        break;
    }
    
    await prefs.setString(_getUserSpecificKey(_subscriptionExpiryKey), expiryTime.toIso8601String());
    
    // 这里可以调用 Supabase 更新用户订阅状态
    await _updateUserSubscriptionInDatabase(tier, period);
    
    debugPrint('✅ Subscription activated successfully! Tier: ${tier.name}, Period: ${period.name}');
  }
  
  Future<void> _updateUserSubscriptionInDatabase(SubscriptionTier tier, SubscriptionPeriod period) async {
    // ✅ 订阅状态由Apple管理，本地存储已足够
    // 不需要同步到Supabase，保持架构简单
    debugPrint('✅ 订阅激活成功 - Apple管理订阅状态，本地存储用户权限');
  }
  
  Future<void> _checkSubscriptionStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final tierString = prefs.getString(_getUserSpecificKey(_subscriptionTierKey));
      final periodString = prefs.getString(_getUserSpecificKey(_subscriptionPeriodKey));
      
      if (tierString != null && periodString != null) {
        _currentTier = SubscriptionTier.values.firstWhere(
          (e) => e.name == tierString,
          orElse: () => SubscriptionTier.free,
        );
        _currentPeriod = SubscriptionPeriod.values.firstWhere(
          (e) => e.name == periodString,
          orElse: () => SubscriptionPeriod.weekly,
        );
        
        // 检查订阅是否过期
        final expiryString = prefs.getString(_getUserSpecificKey(_subscriptionExpiryKey));
        if (expiryString != null) {
          final expiryTime = DateTime.parse(expiryString);
          if (DateTime.now().isAfter(expiryTime)) {
            // 订阅已过期
            _currentTier = SubscriptionTier.free;
            await prefs.setString(_getUserSpecificKey(_subscriptionTierKey), SubscriptionTier.free.name);
            await _updateUserSubscriptionInDatabase(SubscriptionTier.free, SubscriptionPeriod.weekly);
          }
        }
      }
    } catch (e) {
      debugPrint('Failed to check subscription status: $e');
    }
  }
  
  void _handleError(String message) {
    _errorMessage = message;
    _isLoading = false;
    debugPrint('❌ Subscription service error: $message');
    notifyListeners();
  }

  String _getLocalizedErrorMessage(IAPError? error) {
    if (error == null) {
      return '购买失败，请重试';
    }

    // 根据错误代码返回本地化消息
    switch (error.code) {
      case 'storekit_duplicate_product_object':
        return '产品重复，请稍后重试';
      case 'storekit_invalid_payment':
        return '支付信息无效';
      case 'storekit_invalid_product_id':
        return '产品不存在或已下架';
      case 'storekit_payment_cancelled':
        return '用户取消了支付';
      case 'storekit_payment_invalid':
        return '支付请求无效';
      case 'storekit_payment_not_allowed':
        return '当前设备不允许支付';
      case 'storekit_store_product_not_available':
        return '产品暂时不可用';
      case 'storekit_cloud_service_permission_denied':
        return '云服务权限被拒绝';
      case 'storekit_cloud_service_network_connection_failed':
        return '网络连接失败，请检查网络';
      case 'storekit_cloud_service_revoked':
        return '云服务已被撤销';
      case 'storekit_privacy_acknowledgement_required':
        return '需要确认隐私协议';
      case 'storekit_unauthorized_request_data':
        return '请求数据未授权';
      case 'storekit_invalid_offer_identifier':
        return '优惠标识符无效';
      case 'storekit_invalid_signature':
        return '签名验证失败';
      case 'storekit_missing_offer_params':
        return '缺少优惠参数';
      case 'storekit_invalid_offer_price':
        return '优惠价格无效';
      default:
        return error.message.isNotEmpty
            ? '购买失败: ${error.message}'
            : '购买失败，请重试';
    }
  }
  
  // 获取会员等级文本 - 返回翻译键
  String getTierTranslationKey() {
    switch (_currentTier) {
      case SubscriptionTier.free:
        return 'free_member';
      case SubscriptionTier.basic:
        return 'basic_member';
      case SubscriptionTier.premium:
        return 'premium_member';
    }
  }
  




  // ✅ 新增：强制重置当前用户的使用次数
  Future<void> forceResetUsageForCurrentUser() async {
    final userId = _currentUserId;
    if (userId == null) {
      debugPrint('❌ 无法重置：用户未登录');
      return;
    }

    try {
      final prefs = await SharedPreferences.getInstance();

      debugPrint('🔄 开始强制重置用户使用次数: $userId');
      
      // 重置内存中的计数
      _dailyUsageCount = 0;
      _weeklyUsageCount = 0;
      
      // 清除本地存储
      await prefs.remove(_getUserSpecificKey(_dailyUsageCountKey));
      await prefs.remove(_getUserSpecificKey(_weeklyUsageCountKey));
      await prefs.remove(_getUserSpecificKey(_lastUsageDateKey));
      await prefs.remove(_getUserSpecificKey(_lastWeekStartKey));
      
      // 重新标记为已检查，避免重复重置
      final userCheckKey = 'user_data_checked_$userId';
      await prefs.setBool(userCheckKey, true);
      
      notifyListeners();
      debugPrint('✅ 用户使用次数强制重置完成');
    } catch (e) {
      debugPrint('❌ 强制重置失败: $e');
    }
  }



  @override
  void dispose() {
    _subscription.cancel();
    super.dispose();
  }
} 