import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'dart:math';

class AffirmationService {
  /// 国际化肯定语数据
  static final Map<String, Map<ManifestationGoal, List<String>>> _localizedAffirmations = {
    'zh-CN': _chineseAffirmations,
    'zh-TW': _traditionalChineseAffirmations,
    'en-US': _englishAffirmations,
    'es-ES': _spanishAffirmations,
    'ja-JP': _japaneseAffirmations,
    'ko-KR': _koreanAffirmations,
  };

  /// 中文简体肯定语
  static final Map<ManifestationGoal, List<String>> _chineseAffirmations = {
    ManifestationGoal.wealth: [
      '我值得拥有丰富的财富',
      '金钱轻松地流向我',
      '我是财富的磁石',
      '我的收入不断增加',
      '我拥有创造财富的能力',
      '宇宙为我提供无限的丰盛',
      '我与金钱有着健康的关系',
      '我的财务状况越来越好',
      '我吸引着意想不到的财富',
      '我的投资带来丰厚回报',
    ],
    ManifestationGoal.career: [
      '我在事业上不断取得成功',
      '我的才能被认可和重视',
      '我拥有实现职业目标的能力',
      '机会不断向我涌来',
      '我在工作中充满创造力',
      '我的事业蒸蒸日上',
      '我与同事和谐合作',
      '我的专业技能日益精进',
      '我在职场上充满自信',
      '我的努力得到应有的回报',
    ],
    ManifestationGoal.beauty: [
      '我由内而外散发着美丽',
      '我的美丽是独一无二的',
      '我爱我的身体和外表',
      '我的美丽随着时间增长',
      '我散发着迷人的魅力',
      '我的肌肤健康光泽',
      '我的美丽来自内心的光芒',
      '我对自己的外表充满自信',
      '我的美丽吸引着正能量',
      '我每天都变得更加美丽',
    ],
    ManifestationGoal.fame: [
      '我的才华被世界看见',
      '我值得获得认可和赞美',
      '我的影响力不断扩大',
      '我在我的领域中闪闪发光',
      '我的声誉越来越好',
      '我吸引着媒体的正面关注',
      '我的作品感动着无数人',
      '我是我所在领域的佼佼者',
      '我的名字被人们记住',
      '我用我的才能服务世界',
    ],
    ManifestationGoal.love: [
      '我值得被深深地爱着',
      '爱情轻松地进入我的生活',
      '我吸引着真诚的爱情',
      '我的心充满爱与被爱',
      '我与伴侣和谐相处',
      '我的关系充满理解和支持',
      '我散发着爱的能量',
      '我准备好接受真爱',
      '我的爱情关系越来越深厚',
      '我与所爱的人心灵相通',
    ],
  };

  /// 英文肯定语
  static final Map<ManifestationGoal, List<String>> _englishAffirmations = {
    ManifestationGoal.wealth: [
      'I deserve to have abundant wealth',
      'Money flows to me easily',
      'I am a magnet for wealth',
      'My income is constantly increasing',
      'I have the ability to create wealth',
      'The universe provides me with unlimited abundance',
      'I have a healthy relationship with money',
      'My financial situation is getting better',
      'I attract unexpected wealth',
      'My investments bring rich returns',
    ],
    ManifestationGoal.career: [
      'I continuously achieve success in my career',
      'My talents are recognized and valued',
      'I have the ability to achieve my career goals',
      'Opportunities keep flowing to me',
      'I am full of creativity at work',
      'My career is thriving',
      'I work harmoniously with colleagues',
      'My professional skills are constantly improving',
      'I am confident in the workplace',
      'My efforts are rewarded as they deserve',
    ],
    ManifestationGoal.beauty: [
      'I radiate beauty from within',
      'My beauty is unique',
      'I love my body and appearance',
      'My beauty grows with time',
      'I radiate charming charisma',
      'My skin is healthy and glowing',
      'My beauty comes from the light within',
      'I am confident about my appearance',
      'My beauty attracts positive energy',
      'I become more beautiful every day',
    ],
    ManifestationGoal.fame: [
      'My talent is seen by the world',
      'I deserve recognition and praise',
      'My influence is constantly expanding',
      'I shine in my field',
      'My reputation is getting better',
      'I attract positive media attention',
      'My work touches countless people',
      'I am a leader in my field',
      'My name is remembered by people',
      'I serve the world with my talent',
    ],
    ManifestationGoal.love: [
      'I deserve to be deeply loved',
      'Love enters my life easily',
      'I attract sincere love',
      'My heart is full of love and being loved',
      'I get along harmoniously with my partner',
      'My relationship is full of understanding and support',
      'I radiate the energy of love',
      'I am ready to receive true love',
      'My love relationship is getting deeper',
      'I connect with my loved one soul to soul',
    ],
  };

  /// 中文繁体肯定语
  static final Map<ManifestationGoal, List<String>> _traditionalChineseAffirmations = {
    ManifestationGoal.wealth: [
      '我值得擁有豐富的財富',
      '金錢輕鬆地流向我',
      '我是財富的磁石',
      '我的收入不斷增加',
      '我擁有創造財富的能力',
      '宇宙為我提供無限的豐盛',
      '我與金錢有著健康的關係',
      '我的財務狀況越來越好',
      '我吸引著意想不到的財富',
      '我的投資帶來豐厚回報',
    ],
    ManifestationGoal.career: [
      '我在事業上不斷取得成功',
      '我的才能被認可和重視',
      '我擁有實現職業目標的能力',
      '機會不斷向我湧來',
      '我在工作中充滿創造力',
      '我的事業蒸蒸日上',
      '我與同事和諧合作',
      '我的專業技能日益精進',
      '我在職場上充滿自信',
      '我的努力得到應有的回報',
    ],
    ManifestationGoal.beauty: [
      '我由內而外散發著美麗',
      '我的美麗是獨一無二的',
      '我愛我的身體和外表',
      '我的美麗隨著時間增長',
      '我散發著迷人的魅力',
      '我的肌膚健康光澤',
      '我的美麗來自內心的光芒',
      '我對自己的外表充滿自信',
      '我的美麗吸引著正能量',
      '我每天都變得更加美麗',
    ],
    ManifestationGoal.fame: [
      '我的才華被世界看見',
      '我值得獲得認可和讚美',
      '我的影響力不斷擴大',
      '我在我的領域中閃閃發光',
      '我的聲譽越來越好',
      '我吸引著媒體的正面關注',
      '我的作品感動著無數人',
      '我是我所在領域的佼佼者',
      '我的名字被人們記住',
      '我用我的才能服務世界',
    ],
    ManifestationGoal.love: [
      '我值得被深深地愛著',
      '愛情輕鬆地進入我的生活',
      '我吸引著真誠的愛情',
      '我的心充滿愛與被愛',
      '我與伴侶和諧相處',
      '我的關係充滿理解和支持',
      '我散發著愛的能量',
      '我準備好接受真愛',
      '我的愛情關係越來越深厚',
      '我與所愛的人心靈相通',
    ],
  };

  /// 西班牙语肯定语
  static final Map<ManifestationGoal, List<String>> _spanishAffirmations = {
    ManifestationGoal.wealth: [
      'Merezco tener abundante riqueza',
      'El dinero fluye hacia mí fácilmente',
      'Soy un imán para la riqueza',
      'Mis ingresos aumentan constantemente',
      'Tengo la capacidad de crear riqueza',
    ],
    ManifestationGoal.career: [
      'Logro continuamente el éxito en mi carrera',
      'Mis talentos son reconocidos y valorados',
      'Tengo la capacidad de lograr mis objetivos profesionales',
      'Las oportunidades siguen fluyendo hacia mí',
      'Estoy lleno de creatividad en el trabajo',
    ],
    ManifestationGoal.beauty: [
      'Irradio belleza desde adentro',
      'Mi belleza es única',
      'Amo mi cuerpo y mi apariencia',
      'Mi belleza crece con el tiempo',
      'Irradio carisma encantador',
    ],
    ManifestationGoal.fame: [
      'Mi talento es visto por el mundo',
      'Merezco reconocimiento y elogios',
      'Mi influencia se expande constantemente',
      'Brillo en mi campo',
      'Mi reputación está mejorando',
    ],
    ManifestationGoal.love: [
      'Merezco ser profundamente amado',
      'El amor entra en mi vida fácilmente',
      'Atraigo amor sincero',
      'Mi corazón está lleno de amor y ser amado',
      'Me llevo armoniosamente con mi pareja',
    ],
  };

  /// 日语肯定语
  static final Map<ManifestationGoal, List<String>> _japaneseAffirmations = {
    ManifestationGoal.wealth: [
      '私は豊かな富を持つに値します',
      'お金は私に簡単に流れてきます',
      '私は富の磁石です',
      '私の収入は常に増加しています',
      '私には富を創造する能力があります',
    ],
    ManifestationGoal.career: [
      '私はキャリアで継続的に成功を収めています',
      '私の才能は認められ、評価されています',
      '私にはキャリア目標を達成する能力があります',
      '機会は私に向かって流れ続けています',
      '私は仕事で創造性に満ちています',
    ],
    ManifestationGoal.beauty: [
      '私は内側から美しさを放射しています',
      '私の美しさは唯一無二です',
      '私は自分の体と外見を愛しています',
      '私の美しさは時間とともに成長します',
      '私は魅力的なカリスマを放射しています',
    ],
    ManifestationGoal.fame: [
      '私の才能は世界に見られています',
      '私は認識と称賛に値します',
      '私の影響力は常に拡大しています',
      '私は自分の分野で輝いています',
      '私の評判は良くなっています',
    ],
    ManifestationGoal.love: [
      '私は深く愛されるに値します',
      '愛は私の人生に簡単に入ってきます',
      '私は誠実な愛を引き寄せます',
      '私の心は愛と愛されることで満たされています',
      '私はパートナーと調和して過ごしています',
    ],
  };

  /// 韩语肯定语
  static final Map<ManifestationGoal, List<String>> _koreanAffirmations = {
    ManifestationGoal.wealth: [
      '나는 풍부한 부를 가질 자격이 있다',
      '돈이 나에게 쉽게 흘러온다',
      '나는 부의 자석이다',
      '내 수입은 지속적으로 증가하고 있다',
      '나는 부를 창조할 능력이 있다',
    ],
    ManifestationGoal.career: [
      '나는 내 경력에서 지속적으로 성공을 거두고 있다',
      '내 재능은 인정받고 가치를 인정받는다',
      '나는 내 경력 목표를 달성할 능력이 있다',
      '기회가 나에게 계속 흘러온다',
      '나는 직장에서 창의성으로 가득하다',
    ],
    ManifestationGoal.beauty: [
      '나는 내면에서 아름다움을 발산한다',
      '내 아름다움은 독특하다',
      '나는 내 몸과 외모를 사랑한다',
      '내 아름다움은 시간과 함께 성장한다',
      '나는 매력적인 카리스마를 발산한다',
    ],
    ManifestationGoal.fame: [
      '내 재능은 세상에 보여진다',
      '나는 인정과 칭찬을 받을 자격이 있다',
      '내 영향력은 지속적으로 확장되고 있다',
      '나는 내 분야에서 빛난다',
      '내 평판이 좋아지고 있다',
    ],
    ManifestationGoal.love: [
      '나는 깊이 사랑받을 자격이 있다',
      '사랑이 내 삶에 쉽게 들어온다',
      '나는 진실한 사랑을 끌어당긴다',
      '내 마음은 사랑과 사랑받음으로 가득하다',
      '나는 파트너와 조화롭게 지낸다',
    ],
  };

  /// 获取本地化肯定语
  static String getLocalizedAffirmation(ManifestationGoal goal, String languageCode) {
    final affirmations = _localizedAffirmations[languageCode] ?? _localizedAffirmations['zh-CN']!;
    final goalAffirmations = affirmations[goal] ?? [];

    if (goalAffirmations.isEmpty) {
      return _getDefaultAffirmation(languageCode);
    }

    final random = Random();
    return goalAffirmations[random.nextInt(goalAffirmations.length)];
  }

  /// 获取默认肯定语
  static String _getDefaultAffirmation(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '我今天充满正能量';
      case 'zh-TW':
        return '我今天充滿正能量';
      case 'en-US':
        return 'I am full of positive energy today';
      case 'es-ES':
        return 'Estoy lleno de energía positiva hoy';
      case 'ja-JP':
        return '今日は前向きなエネルギーに満ちています';
      case 'ko-KR':
        return '오늘 나는 긍정적인 에너지로 가득하다';
      default:
        return '我今天充满正能量';
    }
  }

  /// 生成肯定语（支持国际化）
  static String generateAffirmation(ManifestationGoal goal, TarotCard card, {String? languageCode}) {
    final language = languageCode ?? 'zh-CN';
    final affirmations = _localizedAffirmations[language] ?? _localizedAffirmations['zh-CN']!;
    final baseAffirmations = affirmations[goal] ?? [];

    if (baseAffirmations.isEmpty) return _getDefaultAffirmation(language);

    final random = Random();
    final baseAffirmation = baseAffirmations[random.nextInt(baseAffirmations.length)];

    // 根据塔罗牌的关键词增强肯定语
    final cardKeyword = card.keywords.isNotEmpty ? card.keywords.first : _getDefaultKeyword(language);

    final enhancedAffirmations = _getEnhancedAffirmation(goal, baseAffirmation, cardKeyword, language);

    return enhancedAffirmations;
  }

  /// 获取默认关键词
  static String _getDefaultKeyword(String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return '力量';
      case 'zh-TW':
        return '力量';
      case 'en-US':
        return 'strength';
      case 'es-ES':
        return 'fuerza';
      case 'ja-JP':
        return '力';
      case 'ko-KR':
        return '힘';
      default:
        return '力量';
    }
  }

  static String _getEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword, String languageCode) {
    switch (languageCode) {
      case 'zh-CN':
        return _getChineseEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      case 'zh-TW':
        return _getTraditionalChineseEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      case 'en-US':
        return _getEnglishEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      case 'es-ES':
        return _getSpanishEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      case 'ja-JP':
        return _getJapaneseEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      case 'ko-KR':
        return _getKoreanEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
      default:
        return _getChineseEnhancedAffirmation(goal, baseAffirmation, cardKeyword);
    }
  }

  static String _getChineseEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation，我用$cardKeyword的力量创造财富';
      case ManifestationGoal.career:
        return '$baseAffirmation，我以$cardKeyword的品质在事业中发光';
      case ManifestationGoal.beauty:
        return '$baseAffirmation，我的$cardKeyword让我更加美丽';
      case ManifestationGoal.fame:
        return '$baseAffirmation，我的$cardKeyword让我在人群中闪耀';
      case ManifestationGoal.love:
        return '$baseAffirmation，我用$cardKeyword的心态拥抱爱情';
    }
  }

  static String _getTraditionalChineseEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation，我用$cardKeyword的力量創造財富';
      case ManifestationGoal.career:
        return '$baseAffirmation，我以$cardKeyword的品質在事業中發光';
      case ManifestationGoal.beauty:
        return '$baseAffirmation，我的$cardKeyword讓我更加美麗';
      case ManifestationGoal.fame:
        return '$baseAffirmation，我的$cardKeyword讓我在人群中閃耀';
      case ManifestationGoal.love:
        return '$baseAffirmation，我用$cardKeyword的心態擁抱愛情';
    }
  }

  static String _getEnglishEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation, I create wealth with the power of $cardKeyword';
      case ManifestationGoal.career:
        return '$baseAffirmation, I shine in my career with the quality of $cardKeyword';
      case ManifestationGoal.beauty:
        return '$baseAffirmation, my $cardKeyword makes me more beautiful';
      case ManifestationGoal.fame:
        return '$baseAffirmation, my $cardKeyword makes me shine among people';
      case ManifestationGoal.love:
        return '$baseAffirmation, I embrace love with the mindset of $cardKeyword';
    }
  }

  static String _getSpanishEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation, creo riqueza con el poder de $cardKeyword';
      case ManifestationGoal.career:
        return '$baseAffirmation, brillo en mi carrera con la cualidad de $cardKeyword';
      case ManifestationGoal.beauty:
        return '$baseAffirmation, mi $cardKeyword me hace más hermoso/a';
      case ManifestationGoal.fame:
        return '$baseAffirmation, mi $cardKeyword me hace brillar entre la gente';
      case ManifestationGoal.love:
        return '$baseAffirmation, abrazo el amor con la mentalidad de $cardKeyword';
    }
  }

  static String _getJapaneseEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation、私は$cardKeywordの力で富を創造します';
      case ManifestationGoal.career:
        return '$baseAffirmation、私は$cardKeywordの品質でキャリアで輝きます';
      case ManifestationGoal.beauty:
        return '$baseAffirmation、私の$cardKeywordが私をより美しくします';
      case ManifestationGoal.fame:
        return '$baseAffirmation、私の$cardKeywordが人々の中で私を輝かせます';
      case ManifestationGoal.love:
        return '$baseAffirmation、私は$cardKeywordの心構えで愛を受け入れます';
    }
  }

  static String _getKoreanEnhancedAffirmation(ManifestationGoal goal, String baseAffirmation, String cardKeyword) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return '$baseAffirmation, 나는 $cardKeyword의 힘으로 부를 창조한다';
      case ManifestationGoal.career:
        return '$baseAffirmation, 나는 $cardKeyword의 품질로 내 경력에서 빛난다';
      case ManifestationGoal.beauty:
        return '$baseAffirmation, 내 $cardKeyword가 나를 더 아름답게 만든다';
      case ManifestationGoal.fame:
        return '$baseAffirmation, 내 $cardKeyword가 사람들 사이에서 나를 빛나게 한다';
      case ManifestationGoal.love:
        return '$baseAffirmation, 나는 $cardKeyword의 마음가짐으로 사랑을 받아들인다';
    }
  }

  // 获取20个随机肯定语用于显化训练
  static List<String> get20RandomAffirmations(ManifestationGoal goal, {String? languageCode}) {
    final language = languageCode ?? 'zh-CN';
    final affirmations = _localizedAffirmations[language] ?? _localizedAffirmations['zh-CN']!;
    final goalAffirmations = affirmations[goal] ?? [];

    if (goalAffirmations.isEmpty) {
      // 如果没有找到对应的肯定语，返回默认肯定语
      return List.generate(20, (index) => _getDefaultAffirmation(language));
    }

    final random = Random();
    final selectedAffirmations = <String>[];
    final usedIndices = <int>{};

    while (selectedAffirmations.length < 20) {
      final index = random.nextInt(goalAffirmations.length);

      // 如果已经用完所有肯定语，重置已使用索引
      if (usedIndices.length == goalAffirmations.length) {
        usedIndices.clear();
      }

      if (!usedIndices.contains(index)) {
        usedIndices.add(index);
        selectedAffirmations.add(goalAffirmations[index]);
      }
    }

    return selectedAffirmations;
  }

  // 获取所有肯定语（200个）
  static List<String> getAllAffirmations(ManifestationGoal goal, {String? languageCode}) {
    final language = languageCode ?? 'zh-CN';
    final affirmations = _localizedAffirmations[language] ?? _localizedAffirmations['zh-CN']!;
    final baseAffirmations = affirmations[goal] ?? [];
    final extendedAffirmations = _getExtendedAffirmations(goal);
    return [...baseAffirmations, ...extendedAffirmations];
  }

  // 扩展的肯定语（每个目标190个额外的肯定语）
  static List<String> _getExtendedAffirmations(ManifestationGoal goal) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return [
          '我的财富意识不断提升',
          '我创造多元化的收入来源',
          '我的金钱观念健康积极',
          '我吸引着财富机会',
          '我的财务自由指日可待',
          '我用智慧管理我的财富',
          '我的投资眼光独到',
          '我值得拥有更多财富',
          '我的财富为我带来安全感',
          '我用财富创造更大价值',
          // ... 继续添加更多肯定语到190个
        ];
      case ManifestationGoal.career:
        return [
          '我的职业道路清晰明确',
          '我在工作中发挥所长',
          '我的领导力不断提升',
          '我创造性地解决问题',
          '我的工作充满意义',
          '我与团队协作无间',
          '我的专业声誉卓著',
          '我勇于接受新挑战',
          '我的工作带来满足感',
          '我在职场中充满活力',
          // ... 继续添加更多肯定语到190个
        ];
      case ManifestationGoal.beauty:
        return [
          '我的美丽源于自信',
          '我的笑容温暖动人',
          '我的气质优雅迷人',
          '我的美丽不受年龄限制',
          '我的内在美闪闪发光',
          '我的美丽激励他人',
          '我的风格独特个性',
          '我的美丽来自健康',
          '我的魅力无法抗拒',
          '我的美丽是天赋礼物',
          // ... 继续添加更多肯定语到190个
        ];
      case ManifestationGoal.fame:
        return [
          '我的才华被世界认可',
          '我的作品影响深远',
          '我的声音被人听见',
          '我的成就鼓舞他人',
          '我的名声建立在实力上',
          '我用名气传播正能量',
          '我的影响力积极向上',
          '我的成功激励他人',
          '我的才能服务社会',
          '我的名声带来责任',
          // ... 继续添加更多肯定语到190个
        ];
      case ManifestationGoal.love:
        return [
          '我的心充满爱的能量',
          '我吸引着灵魂伴侣',
          '我的爱情纯真美好',
          '我在关系中成长',
          '我的爱情充满激情',
          '我与伴侣心灵相通',
          '我的爱情经得起考验',
          '我在爱中找到自己',
          '我的爱情带来快乐',
          '我用爱治愈一切',
          // ... 继续添加更多肯定语到190个
        ];
    }
  }

  static List<String> getManifestationTips(ManifestationGoal goal) {
    switch (goal) {
      case ManifestationGoal.wealth:
        return [
          '每天感恩你已经拥有的财富',
          '想象自己已经实现了财务目标',
          '对金钱保持积极的态度',
          '相信宇宙的丰盛',
        ];
      case ManifestationGoal.career:
        return [
          '清晰地设定你的职业目标',
          '每天为目标采取行动',
          '相信自己的能力和才华',
          '保持学习和成长的心态',
        ];
      case ManifestationGoal.beauty:
        return [
          '每天对镜子中的自己说"我很美"',
          '关注内在美的培养',
          '保持健康的生活方式',
          '用爱的眼光看待自己',
        ];
      case ManifestationGoal.fame:
        return [
          '专注于提升自己的技能',
          '真诚地为他人创造价值',
          '保持谦逊和感恩的心',
          '相信自己值得被看见',
        ];
      case ManifestationGoal.love:
        return [
          '先学会爱自己',
          '对爱情保持开放的心态',
          '释放过去的情感包袱',
          '相信真爱会在合适的时候到来',
        ];
    }
  }
}
