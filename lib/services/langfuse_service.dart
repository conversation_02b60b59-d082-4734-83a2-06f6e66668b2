import 'dart:convert';
import 'package:http/http.dart' as http;

class LangfuseService {
  static const String _baseUrl = 'https://cloud.langfuse.com';
  static const String _publicKey = 'pk-lf-84abb16e-ff7e-4cb5-b05d-ee11ac542420';
  static const String _secretKey = 'sk-lf-01338a20-1b5c-4ece-b7d2-68c5c80f76c1';
  
  static final Map<String, String> _headers = {
    'Content-Type': 'application/json',
    'Authorization': 'Basic ${base64Encode(utf8.encode('$_publicKey:$_secretKey'))}',
  };

  /// 创建新的Trace（一次完整的塔罗解读会话）
  static Future<String> createTrace({
    required String userId,
    required String sessionId,
    required Map<String, dynamic> metadata,
  }) async {
    final traceId = 'trace_${DateTime.now().millisecondsSinceEpoch}_${userId}';
    
    final body = {
      'id': traceId,
      'name': 'tarot_reading_session',
      'userId': userId,
      'sessionId': sessionId,
      'metadata': {
        ...metadata,
        'platform': 'flutter_app',
        'timestamp': DateTime.now().toIso8601String(),
      },
      'tags': ['tarot', 'ai_reading', 'deepseek'],
    };

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/public/traces'),
        headers: _headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        print('✅ Langfuse Trace创建成功: $traceId');
        return traceId;
      } else {
        print('❌ Langfuse Trace创建失败: ${response.statusCode} - ${response.body}');
        return traceId; // 返回ID但不阻塞主流程
      }
    } catch (e) {
      print('❌ Langfuse网络错误: $e');
      return traceId; // 返回ID但不阻塞主流程
    }
  }

  /// 记录AI解读生成过程（Generation）
  static Future<String> createGeneration({
    required String traceId,
    required String name,
    required Map<String, dynamic> input,
    required Map<String, dynamic> output,
    required String model,
    int? promptTokens,
    int? completionTokens,
    Map<String, dynamic>? metadata,
  }) async {
    final generationId = 'gen_${DateTime.now().millisecondsSinceEpoch}';
    
    final body = {
      'id': generationId,
      'traceId': traceId,
      'name': name,
      'startTime': DateTime.now().toIso8601String(),
      'endTime': DateTime.now().add(const Duration(seconds: 1)).toIso8601String(),
      'model': model,
      'modelParameters': {
        'temperature': 0.7,
        'max_tokens': 1000,
      },
      'input': input,
      'output': output,
      'usage': {
        if (promptTokens != null) 'promptTokens': promptTokens,
        if (completionTokens != null) 'completionTokens': completionTokens,
        if (promptTokens != null && completionTokens != null)
          'totalTokens': promptTokens + completionTokens,
      },
      'metadata': {
        ...metadata ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    };

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/public/generations'),
        headers: _headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        print('✅ Langfuse Generation记录成功: $generationId');
        return generationId;
      } else {
        print('❌ Langfuse Generation记录失败: ${response.statusCode}');
        return generationId;
      }
    } catch (e) {
      print('❌ Langfuse Generation网络错误: $e');
      return generationId;
    }
  }

  /// 记录用户反馈评分
  static Future<void> createScore({
    required String traceId,
    required String generationId,
    required String name,
    required double value,
    String? comment,
    Map<String, dynamic>? metadata,
  }) async {
    final body = {
      'traceId': traceId,
      'observationId': generationId,
      'name': name,
      'value': value,
      'comment': comment,
      'metadata': {
        ...metadata ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    };

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/public/scores'),
        headers: _headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        print('✅ Langfuse Score记录成功: $name = $value');
      } else {
        print('❌ Langfuse Score记录失败: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Langfuse Score网络错误: $e');
    }
  }

  /// 记录用户行为事件
  static Future<void> createEvent({
    required String traceId,
    required String name,
    Map<String, dynamic>? input,
    Map<String, dynamic>? output,
    Map<String, dynamic>? metadata,
  }) async {
    final body = {
      'traceId': traceId,
      'name': name,
      'startTime': DateTime.now().toIso8601String(),
      'input': input,
      'output': output,
      'metadata': {
        ...metadata ?? {},
        'timestamp': DateTime.now().toIso8601String(),
      },
    };

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/public/events'),
        headers: _headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        print('✅ Langfuse Event记录成功: $name');
      }
    } catch (e) {
      print('❌ Langfuse Event网络错误: $e');
    }
  }

  /// 批量上传多个观察记录（提高性能）
  static Future<void> batchIngestion(List<Map<String, dynamic>> data) async {
    final body = {
      'batch': data,
    };

    try {
      final response = await http.post(
        Uri.parse('$_baseUrl/api/public/ingestion'),
        headers: _headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 207) {
        print('✅ Langfuse批量上传成功');
      }
    } catch (e) {
      print('❌ Langfuse批量上传错误: $e');
    }
  }
} 