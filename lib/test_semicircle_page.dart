import 'package:flutter/material.dart';
import 'dart:math' as math;

class TestSemicirclePage extends StatelessWidget {
  const TestSemicirclePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('半圆测试'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
      ),
      backgroundColor: const Color(0xFF1A202C),
      body: const SemicircleTestWidget(),
    );
  }
}

class SemicircleTestWidget extends StatelessWidget {
  const SemicircleTestWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // 半圆的中心点
    final centerX = (screenWidth - 40) / 2;
    final centerY = (screenHeight - 320) * 0.6;

    // 半圆半径
    final radius = math.min(centerX * 0.85, (screenHeight - 320) * 0.45);

    // 半圆角度范围
    const startAngle = -math.pi / 2; // -90° (正上方)
    const sweepAngle = math.pi;      // 180°，只渲染半圆

    // 72张卡牌
    const totalCards = 72;

    return Container(
      margin: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // 绘制半圆参考线
          CustomPaint(
            size: Size(screenWidth - 40, screenHeight - 320),
            painter: SemicirclePainter(
              centerX: centerX,
              centerY: centerY,
              radius: radius,
            ),
          ),
          
          // 绘制卡牌
          ...List.generate(totalCards, (i) {
            final angle = startAngle + (sweepAngle / (totalCards - 1)) * i;
            final x = centerX + radius * math.cos(angle);
            final y = centerY + radius * math.sin(angle);

            return Positioned(
              left: x - 15, // 15是卡牌宽度的一半
              top: y - 22.5, // 22.5是卡牌高度的一半
              child: Transform.rotate(
                angle: angle + math.pi / 2,
                child: Transform.scale(
                  scale: 0.5,
                  child: Container(
                    width: 30,
                    height: 45,
                    decoration: BoxDecoration(
                      color: const Color(0xFF746DFF),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Colors.white,
                        width: 1,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        '$i',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 8,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            );
          }),
          
          // 显示信息
          Positioned(
            top: 20,
            left: 20,
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.7),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '半圆测试',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '中心: (${centerX.toInt()}, ${centerY.toInt()})',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  Text(
                    '半径: ${radius.toInt()}',
                    style: const TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  const Text(
                    '卡牌数量: $totalCards',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  const Text(
                    '角度范围: -90° 到 90°',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SemicirclePainter extends CustomPainter {
  final double centerX;
  final double centerY;
  final double radius;

  SemicirclePainter({
    required this.centerX,
    required this.centerY,
    required this.radius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    // 绘制半圆参考线
    canvas.drawArc(
      Rect.fromCircle(center: Offset(centerX, centerY), radius: radius),
      -math.pi / 2, // 起始角度
      math.pi,      // 扫描角度
      false,
      paint,
    );

    // 绘制中心点
    final centerPaint = Paint()
      ..color = Colors.red
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(Offset(centerX, centerY), 3, centerPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
