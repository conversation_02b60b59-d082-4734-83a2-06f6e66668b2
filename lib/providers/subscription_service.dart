import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SubscriptionService with ChangeNotifier {
  bool _isSubscribed = false;
  int _dailyUsageCount = 0;
  String _lastUsageDate = '';
  
  static const int maxDailyFreeUsage = 3;
  static const String _keyIsSubscribed = 'is_subscribed';
  static const String _keyDailyUsage = 'daily_usage_count';
  static const String _keyLastUsageDate = 'last_usage_date';

  bool get isSubscribed => _isSubscribed;
  int get dailyUsageCount => _dailyUsageCount;
  bool get canUseToday {
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_lastUsageDate != today) {
      return true;
    }
    return _isSubscribed || _dailyUsageCount < maxDailyFreeUsage;
  }

  int get remainingFreeUsage {
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_lastUsageDate != today) {
      return maxDailyFreeUsage;
    }
    return maxDailyFreeUsage - _dailyUsageCount;
  }

  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    _isSubscribed = prefs.getBool(_keyIsSubscribed) ?? false;
    _dailyUsageCount = prefs.getInt(_keyDailyUsage) ?? 0;
    _lastUsageDate = prefs.getString(_keyLastUsageDate) ?? '';
    
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_lastUsageDate != today) {
      await _resetDailyUsage();
    }
    
    notifyListeners();
  }

  Future<void> _resetDailyUsage() async {
    final prefs = await SharedPreferences.getInstance();
    final today = DateTime.now().toIso8601String().split('T')[0];
    
    _dailyUsageCount = 0;
    _lastUsageDate = today;
    
    await prefs.setInt(_keyDailyUsage, _dailyUsageCount);
    await prefs.setString(_keyLastUsageDate, _lastUsageDate);
    
    notifyListeners();
  }

  Future<void> incrementUsage() async {
    if (_isSubscribed) return;
    
    final today = DateTime.now().toIso8601String().split('T')[0];
    if (_lastUsageDate != today) {
      await _resetDailyUsage();
    }
    
    _dailyUsageCount++;
    _lastUsageDate = today;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_keyDailyUsage, _dailyUsageCount);
    await prefs.setString(_keyLastUsageDate, _lastUsageDate);
    
    notifyListeners();
  }

  Future<void> subscribe() async {
    _isSubscribed = true;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsSubscribed, true);
    notifyListeners();
  }

  Future<void> unsubscribe() async {
    _isSubscribed = false;
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsSubscribed, false);
    notifyListeners();
  }
} 