import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/services/tarot_data_service.dart';
import 'package:ai_tarot_reading/services/affirmation_service.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/services/user_data_manager.dart';
import 'package:ai_tarot_reading/services/simple_ai_tarot_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:convert';

class AppStateProvider extends ChangeNotifier {
  // Current language code
  String _currentLanguageCode = 'zh';
  String get currentLanguageCode => _currentLanguageCode;

  // Current tab index
  int _currentTabIndex = 1; // Default to Tarot Reading tab
  int get currentTabIndex => _currentTabIndex;

  // Reading history
  List<TarotReading> _readingHistory = [];
  List<TarotReading> get readingHistory => _readingHistory;

  // Daily tarot readings
  Map<String, DailyTarot> _dailyTarotReadings = {};
  Map<String, DailyTarot> get dailyTarotReadings => _dailyTarotReadings;

  // User streak
  int _currentStreak = 0;
  int get currentStreak => _currentStreak;

  // Total sessions
  int _totalSessions = 0;
  int get totalSessions => _totalSessions;

  // Selected spread type
  SpreadType _selectedSpreadType = SpreadType.none;
  SpreadType get selectedSpreadType => _selectedSpreadType;

  // Selected cards
  List<TarotCard> _selectedCards = [];
  List<TarotCard> get selectedCards => _selectedCards;

  // Current reading
  TarotReading? _currentReading;
  TarotReading? get currentReading => _currentReading;

  // Current question
  String _currentQuestion = '';
  String get currentQuestion => _currentQuestion;

  // Reading stage
  ReadingStage _readingStage = ReadingStage.askQuestion;
  ReadingStage get readingStage => _readingStage;

  // Current manifestation goal
  ManifestationGoal? _currentManifestationGoal;
  ManifestationGoal? get currentManifestationGoal => _currentManifestationGoal;

  ManifestationVersion? _currentManifestationVersion;
  ManifestationVersion? get currentManifestationVersion => _currentManifestationVersion;

  // 正念练习统计
  Map<ManifestationGoal, int> _mindfulnessPractices = {
    ManifestationGoal.wealth: 0,
    ManifestationGoal.career: 0,
    ManifestationGoal.beauty: 0,
    ManifestationGoal.fame: 0,
    ManifestationGoal.love: 0,
  };
  Map<ManifestationGoal, int> get mindfulnessPractices => _mindfulnessPractices;

  // 总正念次数
  int get totalMindfulnessCount => _mindfulnessPractices.values.fold(0, (sum, count) => sum + count);

  // 用户名
  String _username = '神秘探索者';
  String get username => _username;

  // 头像
  IconData _avatarIcon = Icons.person;
  IconData get avatarIcon => _avatarIcon;

  List<Color> _avatarGradient = [const Color(0xFFFFD700), const Color(0xFFFF8C00)];
  List<Color> get avatarGradient => _avatarGradient;

  // 自定义头像路径
  String? _customAvatarPath;
  String? get customAvatarPath => _customAvatarPath;

  // Constructor
  AppStateProvider() {
    _initializeData();
    // 确保初始状态
    resetReadingProcess();
  }

  // 设置当前语言代码（由LanguageManager调用）
  void setCurrentLanguageCode(String languageCode) {
    if (_currentLanguageCode != languageCode) {
      _currentLanguageCode = languageCode;
      notifyListeners();
    }
  }

  // 设置用户名
  void setUsername(String username) {
    if (_username != username) {
      _username = username;
      notifyListeners();
    }
  }

  // 设置头像
  void setAvatar(IconData icon, List<Color> gradient) {
    _avatarIcon = icon;
    _avatarGradient = gradient;
    _customAvatarPath = null; // 清除自定义头像
    notifyListeners();
  }

  // 设置自定义头像路径
  Future<void> setCustomAvatarPath(String path) async {
    _customAvatarPath = path;
    notifyListeners();

    // 保存到SharedPreferences
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('custom_avatar_path', path);
  }

  // Initialize with some sample data
  void _initializeData() async {
    // 检查用户切换并清理数据
    await _checkUserSwitchAndClearData();
    
    // 加载自定义头像路径
    await _loadCustomAvatarPath();

    // 先加载后端真实数据
    await _loadRealDataFromBackend();

    // 如果没有数据，则添加示例数据
    if (_readingHistory.isEmpty) {
      _addSampleReadings();
    }

    // 添加示例正念练习数据
    _addSampleMindfulnessData();

    // Add today's daily tarot if not already drawn
    await _checkAndInitializeDailyTarot();

    // Calculate streak
    _calculateStreak();

    notifyListeners();
  }

  // 检查用户切换并清理数据
  Future<void> _checkUserSwitchAndClearData() async {
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null) {
        await UserDataManager.checkAndClearDataForNewUser(currentUser.id);
      }
    } catch (e) {
      print('⚠️ 检查用户切换失败: $e');
    }
  }

  // 加载自定义头像路径
  Future<void> _loadCustomAvatarPath() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      _customAvatarPath = prefs.getString('custom_avatar_path');
    } catch (e) {
      print('⚠️ 加载自定义头像路径失败: $e');
    }
  }

  // 从后端加载真实数据
  Future<void> _loadRealDataFromBackend() async {
    try {
      final supabaseService = SupabaseDataService();
      final readingsData = await supabaseService.getTarotReadings();
      
      _readingHistory = readingsData.map((data) {
        // 添加详细的解析日志
        print('🔍 解析历史记录: ${data['question']}');
        print('🃏 原始卡牌数据: ${data['cards']}');

        final parsedCards = _parseCards(data['cards']);
        print('🃏 解析后卡牌数量: ${parsedCards.length}');
        for (int i = 0; i < parsedCards.length; i++) {
          print('   卡牌${i+1}: ${parsedCards[i].name} ${parsedCards[i].isReversed ? "(逆位)" : "(正位)"}');
        }

        print('📖 解读内容长度: ${data['interpretation']?.toString().length ?? 0}字');
        print('💬 追问数量: ${data['follow_up_questions']?.length ?? 0}个');

        return TarotReading(
          id: data['id'] ?? const Uuid().v4(),
          question: data['question'] ?? '',
          spreadType: _parseSpreadType(data['spread_type']),
          cards: parsedCards,
          interpretation: data['interpretation'] ?? '',
          date: DateTime.parse(data['created_at'] ?? DateTime.now().toIso8601String()),
          accuracy: data['accuracy'],
          usefulness: data['usefulness'],
          satisfaction: data['satisfaction'],
          feedback: data['feedback'],
          followUpQuestions: List<String>.from(data['follow_up_questions'] ?? []),
          followUpResponses: List<String>.from(data['follow_up_responses'] ?? []),
        );
      }).toList();
      
      _totalSessions = _readingHistory.length;
      print('✅ 从后端加载了 ${_readingHistory.length} 条历史记录');
    } catch (e) {
      print('⚠️ 从后端加载历史记录失败: $e');
      _readingHistory = [];
    }
  }

  // 解析牌阵类型
  SpreadType _parseSpreadType(String? spreadTypeString) {
    switch (spreadTypeString) {
      case 'single':
        return SpreadType.single;
      case 'three':
        return SpreadType.three;
      case 'celtic':
        return SpreadType.celtic;
      default:
        return SpreadType.single;
    }
  }

  // 解析卡牌数据
  List<TarotCard> _parseCards(dynamic cardsData) {
    if (cardsData == null) return [];
    
    try {
      List<dynamic> cardsList = cardsData is String 
          ? jsonDecode(cardsData) 
          : cardsData;
      
      return cardsList.map((cardData) {
        return TarotCard(
          id: cardData['id'] ?? '',
          name: cardData['name'] ?? '',
          description: cardData['description'] ?? '',
          meaning: cardData['meaning'] ?? '',
          keywords: List<String>.from(cardData['keywords'] ?? []),
          imageUrl: cardData['imageUrl'] ?? '',
          isMajorArcana: cardData['isMajorArcana'] ?? false,
          isReversed: cardData['isReversed'] ?? false,
          // 添加翻译键，使用通用格式
          nameKey: cardData['nameKey'] ?? 'card_${cardData['id']}_name',
          descriptionKey: cardData['descriptionKey'] ?? 'card_${cardData['id']}_description',
          meaningKey: cardData['meaningKey'] ?? 'card_${cardData['id']}_meaning_upright',
          reversedMeaningKey: cardData['reversedMeaningKey'] ?? 'card_${cardData['id']}_meaning_reversed',
          keywordKeys: List<String>.from(cardData['keywordKeys'] ?? ['card_${cardData['id']}_keyword_1', 'card_${cardData['id']}_keyword_2']),
        );
      }).toList();
    } catch (e) {
      print('⚠️ 解析卡牌数据失败: $e');
      return [];
    }
  }

  void _addSampleReadings() {
    final uuid = const Uuid();
    
    _readingHistory = [
      TarotReading(
        id: uuid.v4(),
        question: "Will I find success in my new career?",
        spreadType: SpreadType.single,
        cards: [TarotCard.majorArcana[7]], // Chariot
        interpretation: "The Chariot suggests that you will indeed find success, but it will require determination and willpower. Stay focused on your goals and maintain control over opposing forces in your life.",
        date: DateTime.now().subtract(const Duration(days: 7)),
        accuracy: 4,
        usefulness: 5,
        satisfaction: 4,
        feedback: "Very insightful reading!",
      ),
      TarotReading(
        id: uuid.v4(),
        question: "What should I focus on in my relationship?",
        spreadType: SpreadType.three,
        cards: [
          TarotCard.majorArcana[1], // Magician
          TarotCard.majorArcana[2], // High Priestess
          TarotCard.majorArcana[6], // Lovers
        ],
        interpretation: "The Magician suggests you need to take action and use your resources wisely. The High Priestess indicates the importance of intuition and understanding unspoken feelings. The Lovers card emphasizes the need for authentic connection and making conscious choices together.",
        date: DateTime.now().subtract(const Duration(days: 3)),
        accuracy: 5,
        usefulness: 5,
        satisfaction: 5,
        feedback: "这帮助我更好地理解了我的关系动态。",
      ),
    ];
    
    _totalSessions = _readingHistory.length;
  }

  // 添加示例正念练习数据
  void _addSampleMindfulnessData() {
    _mindfulnessPractices = {
      ManifestationGoal.wealth: 15,
      ManifestationGoal.career: 8,
      ManifestationGoal.beauty: 12,
      ManifestationGoal.fame: 5,
      ManifestationGoal.love: 10,
    };
  }

  Future<void> _checkAndInitializeDailyTarot() async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    
    if (!_dailyTarotReadings.containsKey(today)) {
      // 先尝试从后端加载今天的数据
      try {
        final supabaseService = SupabaseDataService();
        final dailyTarotData = await supabaseService.getDailyTarot(today);
        
        if (dailyTarotData != null && dailyTarotData['is_drawn'] == true) {
          // 后端有数据，解析并加载
          print('✅ 发现后端今日塔罗数据，正在加载...');
          
          TarotCard? card;
          if (dailyTarotData['card'] != null) {
            final cardData = dailyTarotData['card'] as Map<String, dynamic>;
            final cardId = cardData['id'] as String?;
            if (cardId != null) {
              card = TarotCardsData.allCards.firstWhere(
                (c) => c.id == cardId,
                orElse: () => TarotCardsData.allCards.first,
              );
              
              // 重要：设置正逆位状态
              final isReversed = cardData['isReversed'] as bool? ?? false;
              card = card.copyWith(isReversed: isReversed);
            }
          }
          
          // 解析显化目标
          ManifestationGoal? manifestationGoal;
          if (dailyTarotData['manifestation_goal'] != null) {
            final goalStr = dailyTarotData['manifestation_goal'] as String;
            try {
              manifestationGoal = ManifestationGoal.values.firstWhere(
                (g) => g.name == goalStr,
              );
            } catch (e) {
              print('⚠️ 无法解析显化目标: $goalStr');
            }
          }
          
          _dailyTarotReadings[today] = DailyTarot(
            date: DateTime.now(),
            card: card,
            fortune: dailyTarotData['fortune'] as String?,
            advice: dailyTarotData['advice'] as String?,
            isDrawn: true, // 重要：标记为已抽卡
            manifestationGoal: manifestationGoal,
            affirmation: dailyTarotData['affirmation'] as String?,
          );
          
          print('✅ 成功加载后端今日塔罗数据');
        } else {
          // 后端没有数据，初始化为未抽卡状态
          _dailyTarotReadings[today] = DailyTarot(
            date: DateTime.now(),
            isDrawn: false,
          );
        }
      } catch (e) {
        print('⚠️ 加载后端今日塔罗数据失败: $e');
        // 后端加载失败，初始化为未抽卡状态
        _dailyTarotReadings[today] = DailyTarot(
          date: DateTime.now(),
          isDrawn: false,
        );
      }
    }
  }

  void _calculateStreak() {
    int streak = 0;
    DateTime currentDate = DateTime.now();
    
    // Start from yesterday and go backwards
    for (int i = 1; i <= 365; i++) {
      final date = currentDate.subtract(Duration(days: i));
      final dateString = DateFormat('yyyy-MM-dd').format(date);
      
      if (_dailyTarotReadings.containsKey(dateString) && 
          _dailyTarotReadings[dateString]!.isDrawn) {
        streak++;
      } else {
        break;
      }
    }
    
    // Check if today's card is drawn
    final today = DateFormat('yyyy-MM-dd').format(currentDate);
    if (_dailyTarotReadings.containsKey(today) && 
        _dailyTarotReadings[today]!.isDrawn) {
      streak++;
    }
    
    _currentStreak = streak;
  }

  // Change current tab
  void setCurrentTab(int index) {
    _currentTabIndex = index;
    notifyListeners();
  }

  // Set current question
  void setCurrentQuestion(String question) {
    _currentQuestion = question;
    notifyListeners();
  }

  // Set selected spread type
  void setSelectedSpreadType(SpreadType spreadType) {
    _selectedSpreadType = spreadType;
    _readingStage = ReadingStage.selectSpread;
    notifyListeners();
  }

  // Start card selection
  void startCardSelection() {
    _readingStage = ReadingStage.selectCards;
    _selectedCards = [];
    notifyListeners();
  }

  // Select a card
  void selectCard(TarotCard card) {
    if (_selectedCards.length < getRequiredCardCount()) {
      _selectedCards.add(card);
      
      if (_selectedCards.length == getRequiredCardCount()) {
        _readingStage = ReadingStage.confirmSelection;
      }
      
      notifyListeners();
    }
  }

  // Get required card count based on spread type
  int getRequiredCardCount() {
    switch (_selectedSpreadType) {
      case SpreadType.single:
        return 1;
      case SpreadType.three:
        return 3;
      case SpreadType.celtic:
        return 10;
      default:
        return 0;
    }
  }

  // Reset card selection
  void resetCardSelection() {
    _selectedCards = [];
    _readingStage = ReadingStage.selectCards;
    notifyListeners();
  }

  // Confirm card selection and get AI reading
  void confirmCardSelection() async {
    print('🚨🚨🚨 AppStateProvider.confirmCardSelection() 开始 🚨🚨🚨');
    print('🚨🚨🚨 这是正式流程的AI解读调用 🚨🚨🚨');
    _readingStage = ReadingStage.aiReading;

    // 先创建一个临时解读对象，显示加载状态
    final uuid = const Uuid();
    _currentReading = TarotReading(
      id: uuid.v4(),
      question: _currentQuestion,
      spreadType: _selectedSpreadType,
      cards: List.from(_selectedCards),
      interpretation: '正在生成AI解读...',
      date: DateTime.now(),
      followUpQuestions: [],
      followUpResponses: [],
    );

    notifyListeners();

    // 调用真正的AI解读服务
    try {
      print('🔄 开始调用AI解读服务...');
      print('📋 问题: $_currentQuestion');
      print('🃏 卡牌: ${_selectedCards.map((c) => c.name).join(', ')}');

      // 获取用户在个人页面设置的语言（而不是设备语言）
      final languageManager = LanguageManager();
      final userLanguageCode = languageManager.currentLanguage;

      // 转换为AI服务支持的语言格式
      String aiLanguage;
      switch (userLanguageCode) {
        case 'zh-CN':
        case 'zh-TW':
          aiLanguage = 'zh';
          break;
        case 'ja-JP':
          aiLanguage = 'ja';
          break;
        case 'ko-KR':
          aiLanguage = 'ko';
          break;
        case 'es-ES':
          aiLanguage = 'es';
          break;
        case 'en-US':
        default:
          aiLanguage = 'en';
          break;
      }

      print('🌐 用户设置语言: $userLanguageCode → AI解读语言: $aiLanguage');

      final interpretation = await SimpleAITarotService.getInitialReading(
        question: _currentQuestion,
        cards: _selectedCards,
        userLanguage: aiLanguage,
      );

      print('✅ AI解读生成成功，长度: ${interpretation.length}字');
      print('🎯 准备更新解读内容并触发UI更新...');

      // 更新解读内容
      _currentReading = _currentReading!.copyWith(
        interpretation: interpretation,
      );

      notifyListeners();

    } catch (e) {
      print('❌ AI解读生成失败: $e');

      // 失败时使用模拟解读
      final fallbackInterpretation = _generateMockInterpretation();
      _currentReading = _currentReading!.copyWith(
        interpretation: fallbackInterpretation,
      );

      notifyListeners();
    }
  }

  // Save the current reading to history
  void saveReading({int? accuracy, int? usefulness, int? satisfaction, String? feedback}) async {
    print('🚀 AppStateProvider.saveReading() 被调用');
    print('📊 参数: accuracy=$accuracy, usefulness=$usefulness, satisfaction=$satisfaction');

    if (_currentReading != null) {
      print('✅ 当前解读存在，开始保存...');
      print('📖 解读内容: ${_currentReading!.interpretation.substring(0, 50)}...');
      final updatedReading = _currentReading!.copyWith(
        accuracy: accuracy,
        usefulness: usefulness,
        satisfaction: satisfaction,
        feedback: feedback,
      );
      
      _readingHistory.add(updatedReading);
      _totalSessions++;
      
      // 同步到后端
      try {
        print('🔄 开始同步到后端数据库...');
        final supabaseService = SupabaseDataService();

        // 检查用户登录状态
        print('🔍 检查用户登录状态: ${supabaseService.isAuthenticated}');
        if (!supabaseService.isAuthenticated) {
          print('⚠️ 用户未登录，跳过后端保存');
          return;
        }

        await supabaseService.saveTarotReading({
          'question': updatedReading.question,
          'spread_type': updatedReading.spreadType.name,
          'cards': updatedReading.cards.map((card) => {
            'id': card.id,
            'name': card.name,
            'description': card.description,
            'meaning': card.meaning,
            'keywords': card.keywords,
            'imageUrl': card.imageUrl,
            'isMajorArcana': card.isMajorArcana,
            'isReversed': card.isReversed,
          }).toList(),
          'interpretation': updatedReading.interpretation,
          'accuracy': accuracy,
          'usefulness': usefulness,
          'satisfaction': satisfaction,
          'feedback': feedback,
          'follow_up_questions': updatedReading.followUpQuestions,
          'follow_up_responses': updatedReading.followUpResponses,
          'created_at': updatedReading.date.toIso8601String(),
        });
        print('✅ 塔罗解读已同步到后端');
      } catch (e) {
        print('⚠️ 塔罗解读后端同步失败: $e');
      }
      
      // Reset current reading
      _currentReading = null;
      _currentQuestion = '';
      _selectedSpreadType = SpreadType.none;
      _selectedCards = [];
      _readingStage = ReadingStage.askQuestion;
      
      notifyListeners();
    }
  }

  // Set manifestation goal
  void setManifestationGoal(ManifestationGoal goal) {
    _currentManifestationGoal = goal;

    // 如果今天已经有每日塔罗，更新其显化目标
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    if (_dailyTarotReadings.containsKey(today)) {
      final existingTarot = _dailyTarotReadings[today]!;
      final updatedTarot = existingTarot.copyWith(
        manifestationGoal: goal,
        affirmation: existingTarot.card != null
            ? AffirmationService.generateAffirmation(goal, existingTarot.card!)
            : null,
      );
      _dailyTarotReadings[today] = updatedTarot;
    }

    notifyListeners();
  }

  // 增加正念练习次数
  void incrementMindfulnessPractice(ManifestationGoal goal) {
    _mindfulnessPractices[goal] = (_mindfulnessPractices[goal] ?? 0) + 1;
    notifyListeners();
  }

  // 重置正念练习统计
  void resetMindfulnessStatistics() {
    _mindfulnessPractices = {
      ManifestationGoal.wealth: 0,
      ManifestationGoal.career: 0,
      ManifestationGoal.beauty: 0,
      ManifestationGoal.fame: 0,
      ManifestationGoal.love: 0,
    };
    notifyListeners();
  }

  // Update language and refresh card data
  void updateLanguage(String languageCode) {
    if (_currentLanguageCode == languageCode) return;
    
    final oldLanguageCode = _currentLanguageCode;
    _currentLanguageCode = languageCode;
    
    // Convert current reading cards to new language
    if (_currentReading != null) {
      final convertedCards = TarotDataService.instance.convertCardsToLanguage(
        _currentReading!.cards, 
        languageCode
      );
      _currentReading = _currentReading!.copyWith(cards: convertedCards);
    }
    
    // Convert selected cards to new language
    if (_selectedCards.isNotEmpty) {
      _selectedCards = TarotDataService.instance.convertCardsToLanguage(
        _selectedCards, 
        languageCode
      );
    }
    
    // Convert daily tarot cards to new language
    for (final dateKey in _dailyTarotReadings.keys) {
      final dailyTarot = _dailyTarotReadings[dateKey];
      if (dailyTarot != null && dailyTarot.card != null) {
        final convertedCard = TarotDataService.instance.convertToLanguage(
          dailyTarot.card!, 
          languageCode
        );
        if (convertedCard != null) {
          _dailyTarotReadings[dateKey] = dailyTarot.copyWith(card: convertedCard);
        }
      }
    }
    
    notifyListeners();
  }

  // Draw daily tarot card
  void drawDailyTarot() async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    // Randomly select a card from the complete deck with orientation
    final random = TarotDataService.instance.getRandomCardWithOrientation(_currentLanguageCode);

    // Generate affirmation if manifestation goal is set
    String? affirmation;
    if (_currentManifestationGoal != null) {
      affirmation = AffirmationService.generateAffirmation(_currentManifestationGoal!, random);
    }

    final dailyTarot = DailyTarot(
      date: DateTime.now(),
      card: random,
      fortune: _generateMockFortune(random),
      advice: _generateMockAdvice(random),
      isDrawn: true,
      manifestationGoal: _currentManifestationGoal,
      affirmation: affirmation,
    );

    _dailyTarotReadings[today] = dailyTarot;

    // 同步到后端
    try {
      final supabaseService = SupabaseDataService();
      await supabaseService.saveDailyTarot({
        'date': today,
        'card': {
          'id': random.id,
          'name': random.name,
          'description': random.description,
          'meaning': random.meaning,
          'keywords': random.keywords,
          'imageUrl': random.imageUrl,
          'isMajorArcana': random.isMajorArcana,
          'isReversed': random.isReversed,
        },
        'fortune': dailyTarot.fortune,
        'advice': dailyTarot.advice,
        'is_drawn': true,
        'manifestation_goal': _currentManifestationGoal?.name,
        'affirmation': affirmation,
      });
      print('✅ 每日塔罗已同步到后端');
    } catch (e) {
      print('⚠️ 每日塔罗后端同步失败: $e');
    }

    _calculateStreak();
    notifyListeners();
  }

  // Draw daily tarot card with specific card (for shuffle screen)
  void drawDailyTarotWithSpecificCard(TarotCard selectedCard) async {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    // Generate affirmation if manifestation goal is set
    String? affirmation;
    if (_currentManifestationGoal != null) {
      affirmation = AffirmationService.generateAffirmation(_currentManifestationGoal!, selectedCard);
    }

    final dailyTarot = DailyTarot(
      date: DateTime.now(),
      card: selectedCard,
      fortune: _generateMockFortune(selectedCard),
      advice: _generateMockAdvice(selectedCard),
      isDrawn: true,
      manifestationGoal: _currentManifestationGoal,
      affirmation: affirmation,
    );

    _dailyTarotReadings[today] = dailyTarot;

    // 同步到后端
    try {
      final supabaseService = SupabaseDataService();
      await supabaseService.saveDailyTarot({
        'date': today,
        'card': {
          'id': selectedCard.id,
          'name': selectedCard.name,
          'description': selectedCard.description,
          'meaning': selectedCard.meaning,
          'keywords': selectedCard.keywords,
          'imageUrl': selectedCard.imageUrl,
          'isMajorArcana': selectedCard.isMajorArcana,
          'isReversed': selectedCard.isReversed,
        },
        'fortune': dailyTarot.fortune,
        'advice': dailyTarot.advice,
        'is_drawn': true,
        'manifestation_goal': _currentManifestationGoal?.name,
        'affirmation': affirmation,
      });
      print('✅ 每日塔罗已同步到后端');
    } catch (e) {
      print('⚠️ 每日塔罗后端同步失败: $e');
    }

    _calculateStreak();
    notifyListeners();
  }

  // Mock interpretation generator
  String _generateMockInterpretation() {
    if (_selectedCards.isEmpty) return '';
    
    String interpretation = '';
    
    if (_selectedSpreadType == SpreadType.single) {
      interpretation = "The ${_selectedCards[0].name} represents ${_selectedCards[0].meaning}. In the context of your question '${_currentQuestion}', this suggests that you should focus on your inner strength and trust your intuition. The path forward may require patience and careful consideration of your options.";
    } else if (_selectedSpreadType == SpreadType.three) {
      interpretation = "Past: The ${_selectedCards[0].name} indicates ${_selectedCards[0].meaning}.\n\n"
          "Present: The ${_selectedCards[1].name} suggests ${_selectedCards[1].meaning}.\n\n"
          "Future: The ${_selectedCards[2].name} points to ${_selectedCards[2].meaning}.\n\n"
          "Regarding your question '${_currentQuestion}', this spread reveals a journey from ${_selectedCards[0].keywords.first} through ${_selectedCards[1].keywords.first} toward ${_selectedCards[2].keywords.first}. Trust the process and remain open to new possibilities.";
    }
    
    return interpretation;
  }

  // Mock fortune generator - 简化版本，实际内容由UI动态生成
  String _generateMockFortune(TarotCard card, {Function(String)? translate}) {
    // 返回一个简单的标识符，实际运势内容由UI层的_generateLocalizedFortune生成
    return "fortune_placeholder";
  }

  // Mock advice generator - 简化版本，实际内容由UI动态生成
  String _generateMockAdvice(TarotCard card, {Function(String)? translate}) {
    // 返回一个简单的标识符，实际建议内容由UI层的_generateLocalizedAdvice生成
    return "advice_placeholder";
  }

  // 获取塔罗牌的英文含义
  String _getEnglishMeaning(TarotCard card) {
    final englishMeanings = {
      'Ten of Swords': 'The end of pain, though betrayal has been experienced, a new beginning is about to come.',
      'King of Wands': 'Strong leadership, clear vision and entrepreneurial spirit.',
      'The Fool': 'A new journey begins with optimism and trust. Represents infinite possibilities and new beginnings.',
      'The Magician': 'You have the power to manifest your desires. Perfect combination of skill, talent and willpower.',
      'The High Priestess': 'Listen to your inner voice, trust your intuition and inner wisdom.',
      'The Empress': 'Symbol of fertility, creativity and maternal energy.',
      'The Emperor': 'Stable foundation, authority and leadership. Establishing order and structure.',
      'The Hierophant': 'Seeking spiritual guidance and traditional wisdom. A time of learning and teaching.',
      'The Lovers': 'Deep connection, harmonious relationships and important choices.',
      'The Chariot': 'Overcoming obstacles through focus and determination, achieving victory.',
      'Strength': 'Inner strength and courage, gently overcoming challenges.',
      'The Hermit': 'A time of inner exploration and seeking truth. Solitude and reflection.',
      'Wheel of Fortune': 'Life\'s cycles and changes, good fortune is coming.',
      'Justice': 'Fairness, truth and moral balance. Justice will be served.',
      'The Hanged Man': 'A time of pause and reflection, gaining new perspective through letting go.',
      'Death': 'Major transformation and new beginnings. The old ends, the new begins.',
      'Temperance': 'Balance and harmony, patiently blending opposing forces.',
      'The Devil': 'Being bound by material desires or unhealthy patterns.',
      'The Tower': 'Sudden change and revelation, collapse of old structures.',
      'The Star': 'Hope, healing and spiritual guidance. Light after darkness.',
      'The Moon': 'Illusion and fear, need to trust intuition through uncertainty.',
      'The Sun': 'Joy, success and positive energy. Celebration of life.',
      'Judgement': 'Spiritual awakening and rebirth, heeding the inner call.',
      'The World': 'Completion and achievement, the end of an important cycle.',

      // 权杖组英文含义
      'Ace of Wands': 'Beginning of new projects, burst of creativity, and strong inspiration.',
      'Two of Wands': 'Making long-term plans, showing personal power and foresight.',
      'Three of Wands': 'Expanding horizons, showing leadership and long-term planning.',
      'Four of Wands': 'Celebration and harmony, stable foundation and homecoming.',
      'Five of Wands': 'Competition and conflict, struggling for position and recognition.',
      'Six of Wands': 'Victory and success, receiving recognition and achieving goals.',
      'Seven of Wands': 'Defending your position, standing your ground with courage.',
      'Eight of Wands': 'Swift action and progress, rapid communication and movement.',
      'Nine of Wands': 'Resilience and persistence, making a final stand with strength.',
      'Ten of Wands': 'Heavy burden and responsibility, working hard toward completion.',
      'Page of Wands': 'Exploring new possibilities, showing free spirit and innovative ideas.',
      'Knight of Wands': 'Impulsive and adventurous spirit, high energy action.',
      'Queen of Wands': 'Showing confidence and independence, achieving goals with determination.',
      'King of Wands': 'Strong leadership, clear vision and entrepreneurial spirit.',
    };

    return englishMeanings[card.name] ?? 'A time for reflection and new understanding in this area of life.';
  }

  // 获取塔罗牌的英文关键词
  List<String> _getEnglishKeywords(TarotCard card) {
    final englishKeywords = {
      'Ten of Swords': ['ending pain', 'betrayal', 'new beginning', 'release', 'rebirth'],
      'King of Wands': ['leadership', 'vision', 'entrepreneurship', 'authority', 'success'],
      'The Fool': ['new beginning', 'innocence', 'spontaneity', 'adventure', 'trust'],
      'The Magician': ['manifestation', 'skill', 'willpower', 'creation', 'focus'],
      'The High Priestess': ['intuition', 'wisdom', 'mystery', 'subconscious', 'inner knowledge'],
      'The Empress': ['fertility', 'creativity', 'motherhood', 'nature', 'beauty'],
      'The Emperor': ['authority', 'leadership', 'structure', 'stability', 'control'],
      'The Hierophant': ['tradition', 'spiritual guidance', 'education', 'wisdom', 'faith'],
      'The Lovers': ['love', 'relationships', 'choice', 'harmony', 'connection'],
      'The Chariot': ['willpower', 'determination', 'victory', 'control', 'progress'],
      'Strength': ['inner strength', 'courage', 'patience', 'gentleness', 'resilience'],
      'The Hermit': ['introspection', 'seeking truth', 'solitude', 'reflection', 'wisdom'],
      'Wheel of Fortune': ['destiny', 'luck', 'cycles', 'change', 'opportunity'],
      'Justice': ['justice', 'fairness', 'truth', 'balance', 'morality'],
      'The Hanged Man': ['pause', 'letting go', 'sacrifice', 'new perspective', 'waiting'],
      'Death': ['transformation', 'ending', 'rebirth', 'new beginning', 'release'],
      'Temperance': ['balance', 'patience', 'harmony', 'moderation', 'blending'],
      'The Devil': ['bondage', 'addiction', 'materialism', 'temptation', 'limitation'],
      'The Tower': ['sudden change', 'upheaval', 'revelation', 'collapse', 'awakening'],
      'The Star': ['hope', 'faith', 'healing', 'guidance', 'inspiration'],
      'The Moon': ['illusion', 'fear', 'subconscious', 'intuition', 'uncertainty'],
      'The Sun': ['joy', 'success', 'vitality', 'positivity', 'celebration'],
      'Judgement': ['rebirth', 'awakening', 'calling', 'forgiveness', 'renewal'],
      'The World': ['completion', 'achievement', 'fulfillment', 'success', 'journey\'s end'],

      // 权杖组英文关键词
      'Ace of Wands': ['new beginnings', 'creativity', 'inspiration', 'energy', 'potential'],
      'Two of Wands': ['planning', 'progress', 'personal power', 'vision', 'control'],
      'Three of Wands': ['expansion', 'foresight', 'leadership', 'opportunity', 'growth'],
      'Four of Wands': ['celebration', 'harmony', 'homecoming', 'stability', 'foundation'],
      'Five of Wands': ['competition', 'conflict', 'struggle', 'disagreement', 'challenge'],
      'Six of Wands': ['victory', 'success', 'recognition', 'achievement', 'leadership'],
      'Seven of Wands': ['defense', 'perseverance', 'standing ground', 'courage', 'determination'],
      'Eight of Wands': ['speed', 'movement', 'progress', 'communication', 'action'],
      'Nine of Wands': ['resilience', 'persistence', 'last stand', 'strength', 'endurance'],
      'Ten of Wands': ['burden', 'responsibility', 'hard work', 'pressure', 'perseverance'],
      'Page of Wands': ['exploration', 'free spirit', 'new ideas', 'adventure', 'learning'],
      'Knight of Wands': ['impulsiveness', 'adventure', 'energy', 'passion', 'recklessness'],
      'Queen of Wands': ['confidence', 'independence', 'determination', 'leadership', 'passion'],
      'King of Wands': ['leadership', 'vision', 'entrepreneurship', 'authority', 'success'],
    };

    return englishKeywords[card.name] ?? ['reflection', 'growth', 'understanding', 'balance'];
  }

  // Add a follow-up question and response
  void addFollowUpQuestion(String question, String response) async {
    print('🔄 添加追问: $question');

    if (_currentReading != null) {
      final updatedReading = _currentReading!.copyWith(
        followUpQuestions: [..._currentReading!.followUpQuestions, question],
        followUpResponses: [..._currentReading!.followUpResponses, response],
      );

      _currentReading = updatedReading;
      notifyListeners();

      // 自动保存追问到数据库
      print('💾 自动保存追问到数据库...');
      await _saveFollowUpToDatabase(updatedReading);
    }
  }

  // 保存追问到数据库
  Future<void> _saveFollowUpToDatabase(TarotReading reading) async {
    try {
      final supabaseService = SupabaseDataService();

      if (!supabaseService.isAuthenticated) {
        print('⚠️ 用户未登录，跳过追问保存');
        return;
      }

      // 更新数据库中的追问记录
      await supabaseService.updateTarotReading(reading.id, {
        'follow_up_questions': reading.followUpQuestions,
        'follow_up_responses': reading.followUpResponses,
      });

      print('✅ 追问保存到数据库成功');
    } catch (e) {
      print('❌ 追问保存失败: $e');
    }
  }

  // Rate a reading from history
  void rateReading(String id, {int? accuracy, int? usefulness, int? satisfaction, String? feedback}) async {
    final index = _readingHistory.indexWhere((reading) => reading.id == id);
    
    if (index != -1) {
      final updatedReading = _readingHistory[index].copyWith(
        accuracy: accuracy ?? _readingHistory[index].accuracy,
        usefulness: usefulness ?? _readingHistory[index].usefulness,
        satisfaction: satisfaction ?? _readingHistory[index].satisfaction,
        feedback: feedback ?? _readingHistory[index].feedback,
      );
      
      _readingHistory[index] = updatedReading;
      notifyListeners();
      
      // 同步评分到后端
      try {
        final supabaseService = SupabaseDataService();
        await supabaseService.updateTarotReadingRating(id, {
          'accuracy': updatedReading.accuracy,
          'usefulness': updatedReading.usefulness,
          'satisfaction': updatedReading.satisfaction,
          'feedback': updatedReading.feedback,
        });
        print('✅ 评分已同步到后端');
      } catch (e) {
        print('⚠️ 评分后端同步失败: $e');
      }
    }
  }

  // Set spread type
  void setSpreadType(SpreadType spreadType) {
    _selectedSpreadType = spreadType;
    notifyListeners();
  }

  // Reset the reading process
  void resetReadingProcess() {
    _currentQuestion = '';
    _selectedSpreadType = SpreadType.none;
    _selectedCards = [];
    _currentReading = null;
    _readingStage = ReadingStage.askQuestion;
    notifyListeners();
  }

  // Update daily tarot (for saving journal or other updates)
  void updateDailyTarot(DateTime date, DailyTarot updatedDailyTarot) {
    final dateString = DateFormat('yyyy-MM-dd').format(date);
    _dailyTarotReadings[dateString] = updatedDailyTarot;
    notifyListeners();
  }

  // Clear all readings (for backend integration)
  void clearAllReadings() {
    _readingHistory.clear();
    _dailyTarotReadings.clear();
    _totalSessions = 0;
    _currentStreak = 0;
    notifyListeners();
  }

  // Add a new reading to history (for real-time updates)
  void addReadingToHistory(TarotReading reading) {
    _readingHistory.insert(0, reading); // 插入到顶部
    _totalSessions = _readingHistory.length;
    notifyListeners();
  }

  // Refresh reading history from backend
  Future<void> refreshReadingHistory() async {
    await _loadRealDataFromBackend();
    notifyListeners();
  }

  // Debug: 强制清理并重新抽今日塔罗牌（用于测试）
  void debugClearAndRedrawTodaysTarot() {
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());
    
    print('🐛 调试：清理今日塔罗数据并重新抽卡');
    
    // 清理本地数据
    _dailyTarotReadings.remove(today);
    
    // 重新抽卡
    drawDailyTarot();
    
    print('🐛 调试：重新抽卡完成');
  }
}

enum ReadingStage {
  askQuestion,
  selectSpread,
  selectCards,
  confirmSelection,
  aiReading,
}
