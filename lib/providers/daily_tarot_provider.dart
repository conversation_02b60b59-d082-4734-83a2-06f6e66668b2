import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:math';
import '../models/daily_tarot.dart';
import '../models/tarot_card.dart';
import '../data/tarot_cards_data.dart';
import '../services/tarot_data_service.dart';
import 'dart:convert';

class DailyTarotProvider with ChangeNotifier {
  DailyTarot? _todaysTarot;
  final Map<String, DailyTarot> _tarotHistory = {};

  DailyTarot? get todaysTarot => _todaysTarot;
  Map<String, DailyTarot> get tarotHistory => _tarotHistory;

  DailyTarotProvider() {
    _loadTodaysTarot();
  }

  // 获取今日塔罗
  DailyTarot? getTodaysTarot() {
    final today = _getTodayKey();
    return _tarotHistory[today];
  }

  // 抽取每日卡牌
  Future<void> drawDailyCard() async {
    final today = _getTodayKey();
    
    if (_tarotHistory.containsKey(today)) {
      return; // 今天已经抽过了
    }

    // 随机选择一张卡牌
    final allCards = TarotDataService.instance.getAllCards('zh');
    final random = Random();
    final selectedCard = allCards[random.nextInt(allCards.length)];
    
    // 随机决定正位或逆位
    final isReversed = random.nextBool();
    final cardWithOrientation = TarotCard(
      id: selectedCard.id,
      name: selectedCard.name,
      description: selectedCard.description,
      meaning: selectedCard.meaning,
      keywords: selectedCard.keywords,
      imageUrl: selectedCard.imageUrl,
      isMajorArcana: selectedCard.isMajorArcana,
      isReversed: isReversed,
      nameKey: selectedCard.nameKey,
      descriptionKey: selectedCard.descriptionKey,
      meaningKey: selectedCard.meaningKey,
      reversedMeaningKey: selectedCard.reversedMeaningKey,
      keywordKeys: selectedCard.keywordKeys,
    );

    final dailyTarot = DailyTarot(
      date: DateTime.now(),
      card: cardWithOrientation,
      isDrawn: true,
    );

    _tarotHistory[today] = dailyTarot;
    _todaysTarot = dailyTarot;
    
    await _saveTodaysTarot();
    notifyListeners();
  }

  // 使用特定卡牌设置今日塔罗
  Future<void> setTodaysTarot(TarotCard card) async {
    final today = _getTodayKey();
    
    final dailyTarot = DailyTarot(
      date: DateTime.now(),
      card: card,
      isDrawn: true,
    );

    _tarotHistory[today] = dailyTarot;
    _todaysTarot = dailyTarot;
    
    await _saveTodaysTarot();
    notifyListeners();
  }

  // 重新抽卡
  Future<void> redrawDailyCard() async {
    final today = _getTodayKey();
    _tarotHistory.remove(today);
    _todaysTarot = null;
    
    await drawDailyCard();
  }

  // 获取今天的key
  String _getTodayKey() {
    final now = DateTime.now();
    return '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
  }

  // 加载今日塔罗
  Future<void> _loadTodaysTarot() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = _getTodayKey();
      final savedData = prefs.getString('daily_tarot_$today');
      
      if (savedData != null) {
        final data = jsonDecode(savedData);
        final card = TarotCard(
          id: data['card']['id'],
          name: data['card']['name'],
          description: data['card']['description'] ?? '',
          meaning: data['card']['meaning'] ?? '',
          keywords: List<String>.from(data['card']['keywords'] ?? []),
          imageUrl: data['card']['imageUrl'] ?? '',
          isMajorArcana: data['card']['isMajorArcana'] ?? false,
          isReversed: data['card']['isReversed'] ?? false,
          nameKey: data['card']['nameKey'] ?? 'card_${data['card']['id']}_name',
          descriptionKey: data['card']['descriptionKey'] ?? 'card_${data['card']['id']}_description',
          meaningKey: data['card']['meaningKey'] ?? 'card_${data['card']['id']}_meaning_upright',
          reversedMeaningKey: data['card']['reversedMeaningKey'] ?? 'card_${data['card']['id']}_meaning_reversed',
          keywordKeys: List<String>.from(data['card']['keywordKeys'] ?? ['card_${data['card']['id']}_keyword_1', 'card_${data['card']['id']}_keyword_2']),
        );
        
        final dailyTarot = DailyTarot(
          date: DateTime.parse(data['date']),
          card: card,
          isDrawn: data['isDrawn'] ?? false,
        );
        
        _tarotHistory[today] = dailyTarot;
        _todaysTarot = dailyTarot;
      }
    } catch (e) {
      print('加载每日塔罗失败: $e');
    }
    
    notifyListeners();
  }

  // 保存今日塔罗
  Future<void> _saveTodaysTarot() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = _getTodayKey();
      
      if (_todaysTarot != null && _todaysTarot!.card != null) {
        final card = _todaysTarot!.card!;
        final data = {
          'date': _todaysTarot!.date.toIso8601String(),
          'isDrawn': _todaysTarot!.isDrawn,
          'card': {
            'id': card.id,
            'name': card.name,
            'description': card.description,
            'meaning': card.meaning,
            'keywords': card.keywords,
            'imageUrl': card.imageUrl,
            'isMajorArcana': card.isMajorArcana,
            'isReversed': card.isReversed,
            'nameKey': card.nameKey,
            'descriptionKey': card.descriptionKey,
            'meaningKey': card.meaningKey,
            'reversedMeaningKey': card.reversedMeaningKey,
            'keywordKeys': card.keywordKeys,
          },
        };
        
        await prefs.setString('daily_tarot_$today', jsonEncode(data));
      }
    } catch (e) {
      print('保存每日塔罗失败: $e');
    }
  }

  // 清除历史记录
  Future<void> clearHistory() async {
    _tarotHistory.clear();
    _todaysTarot = null;
    
    final prefs = await SharedPreferences.getInstance();
    final keys = prefs.getKeys().where((key) => key.startsWith('daily_tarot_'));
    for (final key in keys) {
      await prefs.remove(key);
    }
    
    notifyListeners();
  }
} 