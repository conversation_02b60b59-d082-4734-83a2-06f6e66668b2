import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider extends ChangeNotifier {
  static const String _localeKey = 'app_locale';
  
  Locale _locale = const Locale('zh', 'CN'); // 默认中文
  Locale get locale => _locale;

  // 支持的6种语言
  static const List<Locale> supportedLocales = [
    Locale('zh', 'CN'), // 中文简体
    Locale('en', 'US'), // 英文
    Locale('ja', 'JP'), // 日文
    Locale('ko', 'KR'), // 韩文
    Locale('fr', 'FR'), // 法文
    Locale('es', 'ES'), // 西班牙文
  ];

  // 语言名称映射
  static const Map<String, String> localeNames = {
    'zh_CN': '中文简体',
    'en_US': 'English',
    'ja_JP': '日本語',
    'ko_KR': '한국어',
    'fr_FR': 'Français',
    'es_ES': 'Español',
  };

  // 语言标志映射
  static const Map<String, String> localeFlags = {
    'zh_CN': '🇨🇳',
    'en_US': '🇺🇸',
    'ja_JP': '🇯🇵',
    'ko_KR': '🇰🇷',
    'fr_FR': '🇫🇷',
    'es_ES': '🇪🇸',
  };

  LocaleProvider() {
    _loadLocale();
  }

  // 加载保存的语言设置
  Future<void> _loadLocale() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final localeString = prefs.getString(_localeKey);
      
      if (localeString != null) {
        final parts = localeString.split('_');
        if (parts.length == 2) {
          _locale = Locale(parts[0], parts[1]);
          notifyListeners();
        }
      }
    } catch (e) {
      print('加载语言设置失败: $e');
    }
  }

  // 设置语言
  Future<void> setLocale(Locale locale) async {
    if (_locale == locale) return;
    
    _locale = locale;
    notifyListeners();
    
    // 保存到本地存储
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_localeKey, '${locale.languageCode}_${locale.countryCode}');
    } catch (e) {
      print('保存语言设置失败: $e');
    }
  }

  // 快捷设置方法
  Future<void> setToChinese() async {
    await setLocale(const Locale('zh', 'CN'));
  }

  Future<void> setToEnglish() async {
    await setLocale(const Locale('en', 'US'));
  }

  Future<void> setToJapanese() async {
    await setLocale(const Locale('ja', 'JP'));
  }

  Future<void> setToKorean() async {
    await setLocale(const Locale('ko', 'KR'));
  }

  Future<void> setToFrench() async {
    await setLocale(const Locale('fr', 'FR'));
  }

  Future<void> setToSpanish() async {
    await setLocale(const Locale('es', 'ES'));
  }

  // 获取当前语言的显示名称
  String get currentLocaleName {
    final localeKey = '${_locale.languageCode}_${_locale.countryCode}';
    return localeNames[localeKey] ?? 'Unknown';
  }

  // 获取当前语言的标志
  String get currentLocaleFlag {
    final localeKey = '${_locale.languageCode}_${_locale.countryCode}';
    return localeFlags[localeKey] ?? '🌐';
  }

  // 语言判断方法
  bool get isChinese => _locale.languageCode == 'zh';
  bool get isEnglish => _locale.languageCode == 'en';
  bool get isJapanese => _locale.languageCode == 'ja';
  bool get isKorean => _locale.languageCode == 'ko';
  bool get isFrench => _locale.languageCode == 'fr';
  bool get isSpanish => _locale.languageCode == 'es';

  // 获取语言代码
  String get languageCode => _locale.languageCode;

  // 获取国家代码
  String get countryCode => _locale.countryCode ?? '';

  // 获取完整的语言标识符
  String get localeIdentifier => '${_locale.languageCode}_${_locale.countryCode}';

  // 获取AI使用的语言代码（用于传递给后端）
  String get aiLanguageCode {
    switch (_locale.languageCode) {
      case 'zh': return 'zh-CN';
      case 'en': return 'en-US';
      case 'ja': return 'ja-JP';
      case 'ko': return 'ko-KR';
      case 'fr': return 'fr-FR';
      case 'es': return 'es-ES';
      default: return 'en-US'; // 默认英文，针对海外用户
    }
  }

  // 获取AI系统提示的语言名称
  String get aiLanguageName {
    switch (_locale.languageCode) {
      case 'zh': return '中文';
      case 'en': return 'English';
      case 'ja': return '日本語';
      case 'ko': return '한국어';
      case 'fr': return 'Français';
      case 'es': return 'Español';
      default: return '中文';
    }
  }
} 