import '../models/tarot_card.dart';
import 'dart:math';

/// English Tarot Cards Data
class TarotCardsDataEn {
  // Major Arcana cards (22 cards)
  static final List<TarotCard> majorArcana = [
    TarotCard(
      id: '0',
      name: 'The Fool',
      description: 'The Fool - New beginnings, innocence, spontaneity',
      meaning: 'A new journey begins with optimism and trust. Represents infinite possibilities and new beginnings.',
      keywords: ['new beginnings', 'innocence', 'spontaneity', 'adventure', 'trust'],
      imageUrl: 'assets/images/thefool.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '1',
      name: 'The Magician',
      description: 'The Magician - Manifestation, resourcefulness, power',
      meaning: 'You have the power to manifest your desires. Perfect combination of skills, talent, and willpower.',
      keywords: ['manifestation', 'skills', 'willpower', 'creation', 'focus'],
      imageUrl: 'assets/images/themagician.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '2',
      name: 'The High Priestess',
      description: 'The High Priestess - Intuition, divine feminine, subconscious',
      meaning: 'Listen to your inner voice, trust your intuition and inner wisdom.',
      keywords: ['intuition', 'wisdom', 'mystery', 'subconscious', 'inner knowledge'],
      imageUrl: 'assets/images/thehighpriestess.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '3',
      name: 'The Empress',
      description: 'The Empress - Feminine power, beauty, nature',
      meaning: 'Symbol of fertility, creativity, and maternal energy.',
      keywords: ['fertility', 'creativity', 'motherhood', 'nature', 'beauty'],
      imageUrl: 'assets/images/theempress.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '4',
      name: 'The Emperor',
      description: 'The Emperor - Authority, structure, control',
      meaning: 'Stable foundation, authority and leadership. Establish order and structure.',
      keywords: ['authority', 'leadership', 'structure', 'stability', 'control'],
      imageUrl: 'assets/images/theemperor.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '5',
      name: 'The Hierophant',
      description: 'The Hierophant - Tradition, spiritual guidance, education',
      meaning: 'Seek spiritual guidance and traditional wisdom. Time for learning and teaching.',
      keywords: ['tradition', 'spiritual guidance', 'education', 'wisdom', 'faith'],
      imageUrl: 'assets/images/thehierophant.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '6',
      name: 'The Lovers',
      description: 'The Lovers - Love, harmony, relationships',
      meaning: 'Deep connection, harmonious relationships, and important choices.',
      keywords: ['love', 'relationships', 'choices', 'harmony', 'connection'],
      imageUrl: 'assets/images/TheLovers.jpg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '7',
      name: 'The Chariot',
      description: 'The Chariot - Willpower, determination, victory',
      meaning: 'Overcome obstacles and achieve victory through focus and determination.',
      keywords: ['willpower', 'determination', 'victory', 'control', 'progress'],
      imageUrl: 'assets/images/thechariot.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '8',
      name: 'Strength',
      description: 'Strength - Inner strength, courage, patience',
      meaning: 'Inner strength and courage, gently overcoming challenges.',
      keywords: ['inner strength', 'courage', 'patience', 'gentleness', 'resilience'],
      imageUrl: 'assets/images/thestrength.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '9',
      name: 'The Hermit',
      description: 'The Hermit - Introspection, seeking truth, inner guidance',
      meaning: 'Time for inner exploration and seeking truth. Solitude and reflection.',
      keywords: ['introspection', 'seeking truth', 'solitude', 'reflection', 'wisdom'],
      imageUrl: 'assets/images/thehermit.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '10',
      name: 'Wheel of Fortune',
      description: 'Wheel of Fortune - Destiny, luck, life cycles',
      meaning: 'Life cycles and changes, good fortune is coming.',
      keywords: ['destiny', 'luck', 'cycles', 'change', 'opportunity'],
      imageUrl: 'assets/images/wheeloffortune.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '11',
      name: 'Justice',
      description: 'Justice - Fairness, truth, law',
      meaning: 'Fairness, truth, and moral balance. Justice will be served.',
      keywords: ['justice', 'fairness', 'truth', 'balance', 'morality'],
      imageUrl: 'assets/images/justice.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '12',
      name: 'The Hanged Man',
      description: 'The Hanged Man - Suspension, letting go, sacrifice',
      meaning: 'Time for pause and reflection, gaining new perspective through letting go.',
      keywords: ['suspension', 'letting go', 'sacrifice', 'new perspective', 'waiting'],
      imageUrl: 'assets/images/thehangedman.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '13',
      name: 'Death',
      description: 'Death - Endings, transformation, rebirth',
      meaning: 'Major transformation and new beginnings. Old endings, new beginnings.',
      keywords: ['transformation', 'endings', 'rebirth', 'new beginnings', 'release'],
      imageUrl: 'assets/images/death.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '14',
      name: 'Temperance',
      description: 'Temperance - Balance, patience, moderation',
      meaning: 'Balance and harmony, patiently blending opposing forces.',
      keywords: ['balance', 'patience', 'harmony', 'moderation', 'blending'],
      imageUrl: 'assets/images/temperance.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '15',
      name: 'The Devil',
      description: 'The Devil - Bondage, addiction, materialism',
      meaning: 'Bound by material desires or unhealthy patterns.',
      keywords: ['bondage', 'addiction', 'materialism', 'temptation', 'restriction'],
      imageUrl: 'assets/images/thedevil.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '16',
      name: 'The Tower',
      description: 'The Tower - Sudden change, upheaval, revelation',
      meaning: 'Sudden changes and revelations, collapse of old structures.',
      keywords: ['sudden change', 'upheaval', 'revelation', 'collapse', 'awakening'],
      imageUrl: 'assets/images/thetower.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '17',
      name: 'The Star',
      description: 'The Star - Hope, faith, spiritual guidance',
      meaning: 'Hope, healing, and spiritual guidance. Light after darkness.',
      keywords: ['hope', 'faith', 'healing', 'guidance', 'inspiration'],
      imageUrl: 'assets/images/thestar.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '18',
      name: 'The Moon',
      description: 'The Moon - Illusion, fear, subconscious',
      meaning: 'Illusions and fears, need to trust intuition through uncertainty.',
      keywords: ['illusion', 'fear', 'subconscious', 'intuition', 'uncertainty'],
      imageUrl: 'assets/images/themoon.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '19',
      name: 'The Sun',
      description: 'The Sun - Joy, success, vitality',
      meaning: 'Joy, success, and positive energy. Celebration of life.',
      keywords: ['joy', 'success', 'vitality', 'positive', 'celebration'],
      imageUrl: 'assets/images/thesun.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '20',
      name: 'Judgement',
      description: 'Judgement - Rebirth, inner calling, forgiveness',
      meaning: 'Spiritual awakening and rebirth, listening to inner calling.',
      keywords: ['rebirth', 'awakening', 'calling', 'forgiveness', 'renewal'],
      imageUrl: 'assets/images/judgement.jpeg',
      isMajorArcana: true,
    ),
    TarotCard(
      id: '21',
      name: 'The World',
      description: 'The World - Completion, achievement, journey\'s end',
      meaning: 'Completion and achievement, the end of an important cycle.',
      keywords: ['completion', 'achievement', 'fulfillment', 'success', 'journey\'s end'],
      imageUrl: 'assets/images/theworld.jpeg',
      isMajorArcana: true,
    ),
  ];

  // Minor Arcana - Cups (14 cards)
  static final List<TarotCard> cups = [
    TarotCard(
      id: 'ace_cups',
      name: 'Ace of Cups',
      description: 'Ace of Cups - New emotional beginning',
      meaning: 'Beginning of new love, emotional awakening, and creativity.',
      keywords: ['new love', 'emotional awakening', 'creativity', 'intuition', 'spiritual growth'],
      imageUrl: 'assets/images/aceofcups.jpeg',
      isMajorArcana: false,
    ),
    TarotCard(
      id: 'two_cups',
      name: 'Two of Cups',
      description: 'Two of Cups - Partnership, unity',
      meaning: 'Harmonious relationships, partnerships, and mutual attraction.',
      keywords: ['partnership', 'unity', 'harmony', 'mutual attraction', 'cooperation'],
      imageUrl: 'assets/images/twoofcups.jpeg',
      isMajorArcana: false,
    ),
    // ... continue with other cups cards
  ];

  // Minor Arcana - Wands (14 cards)
  static final List<TarotCard> wands = [
    TarotCard(
      id: 'ace_wands',
      name: 'Ace of Wands',
      description: 'Ace of Wands - New beginnings, creativity, inspiration',
      meaning: 'Beginning of new projects, burst of creativity, and strong inspiration.',
      keywords: ['new beginnings', 'creativity', 'inspiration', 'energy', 'potential'],
      imageUrl: 'assets/images/aceofwands.jpeg',
      isMajorArcana: false,
    ),
    // ... continue with other wands cards
  ];

  // Minor Arcana - Swords (14 cards)
  static final List<TarotCard> swords = [
    TarotCard(
      id: 'ace_swords',
      name: 'Ace of Swords',
      description: 'Ace of Swords - New ideas, mental clarity, breakthrough',
      meaning: 'Birth of new ideas, mental clarity, and major breakthrough.',
      keywords: ['new ideas', 'mental clarity', 'breakthrough', 'truth', 'insight'],
      imageUrl: 'assets/images/aceofswords.jpeg',
      isMajorArcana: false,
    ),
    // ... continue with other swords cards
  ];

  // Minor Arcana - Pentacles (14 cards)
  static final List<TarotCard> pentacles = [
    TarotCard(
      id: 'ace_pentacles',
      name: 'Ace of Pentacles',
      description: 'Ace of Pentacles - New opportunity, prosperity, material beginning',
      meaning: 'New material opportunities, beginning of prosperity and wealth.',
      keywords: ['new opportunity', 'prosperity', 'material beginning', 'wealth', 'potential'],
      imageUrl: 'assets/images/aceofpentacles.jpeg',
      isMajorArcana: false,
    ),
    // ... continue with other pentacles cards
  ];

  // Get all tarot cards
  static List<TarotCard> get allCards => [...majorArcana, ...cups, ...wands, ...swords, ...pentacles];

  // Get card by ID
  static TarotCard? getCardById(String id) {
    try {
      return allCards.firstWhere((card) => card.id == id);
    } catch (e) {
      return null;
    }
  }

  // Random cards with orientation
  static List<TarotCard> getRandomCards(int count) {
    final random = Random();
    final shuffled = List<TarotCard>.from(allCards)..shuffle(random);
    
    // Randomly assign upright/reversed for each card
    return shuffled.take(count).map((card) {
      final isReversed = random.nextBool(); // 50% chance of reversed
      return card.copyWith(isReversed: isReversed);
    }).toList();
  }

  // Generate random card with orientation
  static TarotCard getRandomCardWithOrientation() {
    final random = Random();
    final card = allCards[random.nextInt(allCards.length)];
    final isReversed = random.nextBool(); // 50% chance of reversed
    return card.copyWith(isReversed: isReversed);
  }

  // Get reversed meaning for English cards
  static String getReversedMeaning(TarotCard card) {
    final reversedMeanings = <String, String>{
      // Major Arcana reversed meanings
      'The Fool': 'Recklessness, lack of planning, poor decisions, unrealistic behavior',
      'The Magician': 'Manipulation, abuse of power, lack of skill, poor focus',
      'The High Priestess': 'Ignoring intuition, lack of inner wisdom, secrets revealed, emotional confusion',
      'The Empress': 'Lack of creativity, over-dependence, emotional imbalance, maternal issues',
      'The Emperor': 'Tyranny, lack of control, authority questioned, structural collapse',
      'The Hierophant': 'Rebellion against tradition, independent thinking, questioning authority, breaking conventions',
      'The Lovers': 'Relationship problems, wrong choices, disharmony, separation',
      'The Chariot': 'Lack of direction, loss of control, weak will, defeat',
      'Strength': 'Weakness, lack of patience, inner fear, loss of confidence',
      'The Hermit': 'Isolation, refusing guidance, lost direction, stubbornness',
      'Wheel of Fortune': 'Bad luck, loss of control, vicious cycles, poor timing',
      'Justice': 'Injustice, bias, avoiding responsibility, lack of balance',
      'The Hanged Man': 'Resistance to change, meaningless sacrifice, avoiding reality, impatience',
      'Death': 'Resistance to change, fear of transformation, stagnation, refusing growth',
      'Temperance': 'Imbalance, lack of patience, extremes, loss of harmony',
      'The Devil': 'Breaking free from bondage, regaining freedom, overcoming temptation, spiritual liberation',
      'The Tower': 'Avoiding disaster, internal change, gradual transformation, resisting change',
      'The Star': 'Loss of hope, lack of confidence, spiritual confusion, unclear goals',
      'The Moon': 'Overcoming fears, truth revealed, clearing illusions, psychological cleansing',
      'The Sun': 'Over-confidence, false happiness, delayed success, lack of energy',
      'Judgement': 'Self-doubt, refusing awakening, poor judgment, failure to grow',
      'The World': 'Incomplete, lack of achievement, unmet goals, interrupted journey',
      
      // Example Minor Arcana reversed meanings (abbreviated for space)
      'Ace of Cups': 'Emotional blockage, lack of love, creative blocks, spiritual depletion',
      'Ace of Wands': 'Lack of motivation, creative blocks, delayed plans, low energy',
      'Ace of Swords': 'Mental confusion, lack of clarity, poor communication, creative blocks',
      'Ace of Pentacles': 'Missed opportunities, financial problems, lack of practicality, unclear goals',
    };

    return reversedMeanings[card.name] ?? 'Need to re-examine this area, seeking new balance and understanding';
  }

  // Get full meaning including upright/reversed
  static String getFullMeaning(TarotCard card) {
    if (card.isReversed) {
      return getReversedMeaning(card);
    } else {
      return card.meaning;
    }
  }

  // Get full keywords including upright/reversed
  static List<String> getFullKeywords(TarotCard card) {
    if (card.isReversed) {
      // Reversed keywords mapping
      final reversedKeywords = <String, List<String>>{
        'The Fool': ['reckless', 'impulsive', 'careless', 'poor planning'],
        'The Magician': ['manipulation', 'abuse of power', 'poor focus', 'lack of skill'],
        'The High Priestess': ['ignoring intuition', 'emotional confusion', 'lack of wisdom', 'secrets exposed'],
        'The Empress': ['lack of creativity', 'emotional imbalance', 'over-dependence', 'maternal issues'],
        'The Emperor': ['tyranny', 'lack of control', 'questioned authority', 'structural collapse'],
        // Continue adding more...
      };
      
      return reversedKeywords[card.name] ?? ['blocked', 'difficult', 'challenging', 'reflection'];
    } else {
      return card.keywords;
    }
  }
} 