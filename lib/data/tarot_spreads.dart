// 塔罗牌阵数据文件
// 这个文件导出所有可用的塔罗牌阵配置

import '../models/tarot_spread.dart';

// 导出所有牌阵数据，方便其他文件使用
final List<TarotSpread> allTarotSpreads = TarotSpread.allSpreads;

// 常用牌阵的快捷访问
final TarotSpread singleCardSpread = TarotSpread.getSpreadByName('单张牌阵')!;
final TarotSpread threeCardSpread = TarotSpread.getSpreadByName('三张牌阵（经典）')!;
final TarotSpread adviceSpread = TarotSpread.getSpreadByName('建议牌阵')!;
final TarotSpread choiceSpread = TarotSpread.getSpreadByName('二选一牌阵')!;
final TarotSpread loveSpread = TarotSpread.getSpreadByName('爱情关系牌阵')!;
final TarotSpread sixPointStarSpread = TarotSpread.getSpreadByName('六芒星牌阵')!;
final TarotSpread celticCrossSpread = TarotSpread.getSpreadByName('凯尔特十字牌阵')!;
final TarotSpread monthlySpread = TarotSpread.getSpreadByName('每月运势牌阵')!;
final TarotSpread weeklySpread = TarotSpread.getSpreadByName('七日运势牌阵')!;
final TarotSpread careerSpread = TarotSpread.getSpreadByName('事业成长牌阵')!;
final TarotSpread healingSpread = TarotSpread.getSpreadByName('心灵疗愈牌阵')!;
final TarotSpread newMoonSpread = TarotSpread.getSpreadByName('新月许愿牌阵')!;
final TarotSpread yearlySpread = TarotSpread.getSpreadByName('全年运势牌阵')!;

// 根据专题获取推荐牌阵的辅助函数
List<TarotSpread> getRecommendedSpreadsForTopic(String topic) {
  return TarotSpread.getSpreadsForTopic(topic);
}

// 根据卡牌数量筛选牌阵
List<TarotSpread> getSpreadsByCardCount(int cardCount) {
  return allTarotSpreads.where((spread) => spread.cardCount == cardCount).toList();
}

// 获取简单牌阵（1-3张卡牌）
List<TarotSpread> getSimpleSpreads() {
  return allTarotSpreads.where((spread) => spread.cardCount <= 3).toList();
}

// 获取中等复杂度牌阵（4-7张卡牌）
List<TarotSpread> getMediumSpreads() {
  return allTarotSpreads.where((spread) => spread.cardCount >= 4 && spread.cardCount <= 7).toList();
}

// 获取复杂牌阵（8张以上卡牌）
List<TarotSpread> getComplexSpreads() {
  return allTarotSpreads.where((spread) => spread.cardCount >= 8).toList();
}
