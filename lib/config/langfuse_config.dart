class LangfuseConfig {
  // 生产环境配置
  static const String prodBaseUrl = 'https://cloud.langfuse.com';
  static const String prodPublicKey = String.fromEnvironment('LANGFUSE_PUBLIC_KEY', defaultValue: '');
  static const String prodSecretKey = String.fromEnvironment('LANGFUSE_SECRET_KEY', defaultValue: '');
  
  // 开发环境配置（可选，自托管）
  static const String devBaseUrl = 'http://localhost:3000';
  static const String devPublicKey = String.fromEnvironment('LANGFUSE_DEV_PUBLIC_KEY', defaultValue: '');
  static const String devSecretKey = String.fromEnvironment('LANGFUSE_DEV_SECRET_KEY', defaultValue: '');
  
  // 当前环境配置
  static const bool isProduction = bool.fromEnvironment('dart.vm.product', defaultValue: false);
  
  static String get baseUrl => isProduction ? prodBaseUrl : (devBaseUrl.isNotEmpty ? devBaseUrl : prodBaseUrl);
  static String get publicKey => isProduction ? prodPublicKey : (devPublicKey.isNotEmpty ? devPublicKey : prodPublicKey);
  static String get secretKey => isProduction ? prodSecretKey : (devSecretKey.isNotEmpty ? devSecretKey : prodSecretKey);
  
  // 功能开关
  static const bool enableTracking = bool.fromEnvironment('ENABLE_LANGFUSE_TRACKING', defaultValue: true);
  static const bool enableBatchMode = bool.fromEnvironment('LANGFUSE_BATCH_MODE', defaultValue: true);
  static const int batchSize = int.fromEnvironment('LANGFUSE_BATCH_SIZE', defaultValue: 10);
  static const int flushIntervalSeconds = int.fromEnvironment('LANGFUSE_FLUSH_INTERVAL', defaultValue: 30);
  
  // 验证配置
  static bool get isConfigured => publicKey.isNotEmpty && secretKey.isNotEmpty;
  
  static void printConfig() {
    print('📊 Langfuse配置状态:');
    print('   - BaseURL: $baseUrl');
    print('   - 已配置: $isConfigured');
    print('   - 启用追踪: $enableTracking');
    print('   - 批处理模式: $enableBatchMode');
    print('   - 生产环境: $isProduction');
  }
} 