/// Supabase配置文件
/// 包含Supabase项目的URL和匿名密钥
class SupabaseConfig {
  // 🔧 Supabase项目配置 (已更新)
  static const String supabaseUrl = 'https://ktqlxbcauxomczubqasp.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0cWx4YmNhdXhvbWN6dWJxYXNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Mjc5OTksImV4cCI6MjA2MTQwMzk5OX0.BKL7B2Mbpl1sOsPI5QsX6EmbmHZu_RWPSh8knHkUEno';
  
  // 🗄️ 数据库表名
  static const String usersTable = 'users';
  static const String tarotReadingsTable = 'tarot_readings';
  static const String dailyTarotTable = 'daily_tarot';
  static const String userPreferencesTable = 'user_preferences';
  
  // 🔐 认证配置
  static const String redirectUrl = 'com.hailie.aitarotreading://login-callback/';
  
  // 📱 Apple登录配置
  static const String appleClientId = 'com.hailie.aitarotreading';
  static const String appleRedirectUri = 'com.hailie.aitarotreading://login-callback/';
}

/// 环境配置
/// 用于区分开发、测试和生产环境
enum Environment {
  development,
  staging,
  production,
}

class EnvironmentConfig {
  static const Environment currentEnvironment = Environment.development;
  
  static String get supabaseUrl {
    switch (currentEnvironment) {
      case Environment.development:
        return 'YOUR_DEV_SUPABASE_URL';
      case Environment.staging:
        return 'YOUR_STAGING_SUPABASE_URL';
      case Environment.production:
        return 'YOUR_PROD_SUPABASE_URL';
    }
  }
  
  static String get supabaseAnonKey {
    switch (currentEnvironment) {
      case Environment.development:
        return 'YOUR_DEV_SUPABASE_ANON_KEY';
      case Environment.staging:
        return 'YOUR_STAGING_SUPABASE_ANON_KEY';
      case Environment.production:
        return 'YOUR_PROD_SUPABASE_ANON_KEY';
    }
  }
  
  static bool get isProduction => currentEnvironment == Environment.production;
  static bool get isDevelopment => currentEnvironment == Environment.development;
}
