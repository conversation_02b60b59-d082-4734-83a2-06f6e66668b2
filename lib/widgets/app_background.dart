import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/app_background_service.dart';
import 'dart:io';

class AppBackground extends StatelessWidget {
  final Widget child;
  
  const AppBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AppBackgroundService>(
      builder: (context, appBackgroundService, _) {
        return Container(
          decoration: BoxDecoration(
            image: DecorationImage(
              image: _getBackgroundImageProvider(appBackgroundService),
              fit: BoxFit.cover,
            ),
          ),
          child: child,
        );
      },
    );
  }

  ImageProvider _getBackgroundImageProvider(AppBackgroundService service) {
    if (service.isUsingCustomBackground && service.customBackgroundPath != null) {
      // 使用自定义背景
      return FileImage(File(service.customBackgroundPath!));
    } else {
      // 使用默认背景 tarot_mascot.png
      return const AssetImage('assets/images/tarot_mascot.png');
    }
  }
} 