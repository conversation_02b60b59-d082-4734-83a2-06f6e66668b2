import 'dart:math';
import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 塔罗应用吉祥物组件
/// 基于提供的3D角色形象设计
class TarotMascot extends StatefulWidget {
  final String message;
  final bool showMessage;
  final VoidCallback? onTap;
  final double size;

  const TarotMascot({
    super.key,
    this.message = '欢迎来到神秘的塔罗世界！',
    this.showMessage = false,
    this.onTap,
    this.size = 120,
  });

  @override
  State<TarotMascot> createState() => _TarotMascotState();
}

class _TarotMascotState extends State<TarotMascot>
    with TickerProviderStateMixin {
  late AnimationController _floatController;
  late AnimationController _sparkleController;
  bool _showBubble = false;

  @override
  void initState() {
    super.initState();
    _floatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    
    _sparkleController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    if (widget.showMessage) {
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          setState(() {
            _showBubble = true;
          });
        }
      });
    }
  }

  @override
  void dispose() {
    _floatController.dispose();
    _sparkleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.onTap?.call();
        setState(() {
          _showBubble = !_showBubble;
        });
      },
      child: SizedBox(
        width: widget.size + 60,
        height: widget.size + 80,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // 魔法光环效果
            _buildMagicAura(),
            
            // 主角色
            AnimatedBuilder(
              animation: _floatController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(0, _floatController.value * 8 - 4),
                  child: _buildMascotCharacter(),
                );
              },
            ),
            
            // 对话气泡
            if (_showBubble) _buildSpeechBubble(),
            
            // 魔法粒子效果
            _buildMagicParticles(),
          ],
        ),
      ),
    );
  }

  Widget _buildMagicAura() {
    return AnimatedBuilder(
      animation: _sparkleController,
      builder: (context, child) {
        return Container(
          width: widget.size + 40,
          height: widget.size + 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: RadialGradient(
              colors: [
                FigmaTheme.primaryPink.withValues(alpha: 0.3),
                FigmaTheme.accentPurple.withValues(alpha: 0.2),
                Colors.transparent,
              ],
              stops: const [0.0, 0.7, 1.0],
            ),
          ),
        ).animate(onPlay: (controller) => controller.repeat())
            .scale(
              begin: const Offset(0.8, 0.8),
              end: const Offset(1.2, 1.2),
              duration: 2.seconds,
            )
            .then()
            .scale(
              begin: const Offset(1.2, 1.2),
              end: const Offset(0.8, 0.8),
              duration: 2.seconds,
            );
      },
    );
  }

  Widget _buildMascotCharacter() {
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFE8F4FD), // 浅蓝色（天空）
            Color(0xFFF8E8FF), // 浅紫色
            Color(0xFFFFE8F8), // 浅粉色
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: FigmaTheme.primaryPink.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 角色主体（简化版本）
          _buildCharacterBody(),
          
          // 塔罗牌
          Positioned(
            right: 15,
            top: 25,
            child: _buildTarotCard(),
          ),
          
          // 水晶球
          Positioned(
            left: 15,
            bottom: 25,
            child: _buildCrystalBall(),
          ),
        ],
      ),
    );
  }

  Widget _buildCharacterBody() {
    return Container(
      width: widget.size * 0.7,
      height: widget.size * 0.7,
      decoration: const BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFFFFDBB5), // 肤色
            Color(0xFFFFCBA4),
          ],
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 头发
          Positioned(
            top: 5,
            child: Container(
              width: widget.size * 0.6,
              height: widget.size * 0.4,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(widget.size * 0.3),
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFFFFD700), // 金色头发
                    Color(0xFFFFA500),
                  ],
                ),
              ),
            ),
          ),
          
          // 眼睛
          Positioned(
            top: widget.size * 0.25,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildEye(),
                SizedBox(width: widget.size * 0.1),
                _buildEye(),
              ],
            ),
          ),
          
          // 微笑
          Positioned(
            top: widget.size * 0.4,
            child: Container(
              width: widget.size * 0.15,
              height: widget.size * 0.08,
              decoration: BoxDecoration(
                color: const Color(0xFFFF6B9D),
                borderRadius: BorderRadius.circular(widget.size * 0.04),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEye() {
    return Container(
      width: widget.size * 0.08,
      height: widget.size * 0.08,
      decoration: const BoxDecoration(
        color: Color(0xFF4A90E2),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Container(
          width: widget.size * 0.04,
          height: widget.size * 0.04,
          decoration: const BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
          ),
        ),
      ),
    );
  }

  Widget _buildTarotCard() {
    return Transform.rotate(
      angle: 0.2,
      child: Container(
        width: 20,
        height: 30,
        decoration: BoxDecoration(
          color: FigmaTheme.primaryPink,
          borderRadius: BorderRadius.circular(3),
          border: Border.all(color: FigmaTheme.accentPurple, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(1, 2),
            ),
          ],
        ),
        child: const Center(
          child: Icon(
            Icons.auto_awesome,
            size: 12,
            color: Colors.white,
          ),
        ),
      ),
    ).animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 3.seconds);
  }

  Widget _buildCrystalBall() {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            Colors.white.withValues(alpha: 0.9),
            FigmaTheme.primaryPink.withValues(alpha: 0.3),
            FigmaTheme.primaryPink.withValues(alpha: 0.6),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: FigmaTheme.primaryPink.withValues(alpha: 0.4),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ),
    ).animate(onPlay: (controller) => controller.repeat())
        .scale(
          begin: const Offset(0.8, 0.8),
          end: const Offset(1.2, 1.2),
          duration: 2.seconds,
        )
        .then()
        .scale(
          begin: const Offset(1.2, 1.2),
          end: const Offset(0.8, 0.8),
          duration: 2.seconds,
        );
  }

  Widget _buildSpeechBubble() {
    return Positioned(
      top: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        constraints: BoxConstraints(maxWidth: widget.size + 40),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Text(
          widget.message,
          style: FigmaTheme.bodySmall.copyWith(
            color: FigmaTheme.textPrimary,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    ).animate()
        .fadeIn(duration: 300.ms)
        .scale(begin: const Offset(0.8, 0.8), duration: 300.ms);
  }

  Widget _buildMagicParticles() {
    return AnimatedBuilder(
      animation: _sparkleController,
      builder: (context, child) {
        return Stack(
          children: List.generate(6, (index) {
            final angle = (index * 60.0) * (3.14159 / 180);
            final radius = 40 + (_sparkleController.value * 20);
            final x = radius * cos(angle);
            final y = radius * sin(angle);
            
            return Positioned(
              left: widget.size / 2 + x,
              top: widget.size / 2 + y,
              child: Container(
                width: 4,
                height: 4,
                decoration: BoxDecoration(
                  color: [
                    FigmaTheme.primaryPink,
                    FigmaTheme.accentPurple,
                    FigmaTheme.secondaryBlue,
                  ][index % 3].withValues(alpha: 0.8),
                  shape: BoxShape.circle,
                ),
              ).animate(onPlay: (controller) => controller.repeat())
                  .scale(
                    begin: const Offset(0.5, 0.5),
                    end: const Offset(1.5, 1.5),
                    duration: 1.seconds,
                  )
                  .then()
                  .fadeOut(duration: 0.5.seconds),
            );
          }),
        );
      },
    );
  }
}

/// 预定义的吉祥物消息
class MascotMessages {
  static String welcome(LanguageManager languageManager) => languageManager.translate('welcome_tarot_world');
  static String selectCards(LanguageManager languageManager) => languageManager.translate('select_spread_type');
  static String drawCards(LanguageManager languageManager) => languageManager.translate('draw_mysterious_cards');
  static String reading(LanguageManager languageManager) => languageManager.translate('cards_revealing_answers');
  static String dailyTarot(LanguageManager languageManager) => languageManager.translate('how_is_today_fortune');
  static String history(LanguageManager languageManager) => languageManager.translate('review_past_guidance');
  static String profile(LanguageManager languageManager) => languageManager.translate('understand_tarot_journey');
}
