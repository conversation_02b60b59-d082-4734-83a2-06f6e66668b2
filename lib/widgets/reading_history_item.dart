import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:intl/intl.dart';
import 'package:ai_tarot_reading/theme/app_theme.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:provider/provider.dart';

class ReadingHistoryItem extends StatefulWidget {
  final TarotReading reading;
  final Function(int? accuracy, int? usefulness, int? satisfaction, String? feedback)? onRate;

  const ReadingHistoryItem({
    super.key,
    required this.reading,
    this.onRate,
  });

  @override
  State<ReadingHistoryItem> createState() => _ReadingHistoryItemState();
}

class _ReadingHistoryItemState extends State<ReadingHistoryItem> {
  // 评分状态
  int? _accuracy;
  int? _usefulness; 
  int? _satisfaction;
  final TextEditingController _feedbackController = TextEditingController();
  bool _isRatingMode = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    // 初始化当前评分
    _accuracy = widget.reading.accuracy;
    _usefulness = widget.reading.usefulness;
    _satisfaction = widget.reading.satisfaction;
    _feedbackController.text = widget.reading.feedback ?? '';
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(0), // 外层容器已有margin
      decoration: BoxDecoration(
        color: Colors.transparent, // 透明背景
        borderRadius: BorderRadius.circular(16),
      ),
      child: ExpansionTile(
        backgroundColor: Colors.transparent,
        collapsedBackgroundColor: Colors.transparent,
        title: Text(
          widget.reading.question,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Colors.black, // 黑色文字
          ),
        ),
        subtitle: Text(
          DateFormat('MMM d, yyyy').format(widget.reading.date),
          style: TextStyle(
            color: Colors.black.withOpacity(0.8), // 半透明白色
          ),
        ),
        iconColor: Colors.black,
        collapsedIconColor: Colors.black.withOpacity(0.8),
        leading: _buildLeadingIcon(),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 📋 完整对话流程显示
                _buildCompleteConversation(),
                const SizedBox(height: 24),
                
                // 评分区域
                _buildRatingSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRatingSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        // 倒数第三层样式：更透明的毛玻璃效果
        color: Colors.black.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.black.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    languageManager.translate('rate_this_reading'),
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Colors.black,
                    ),
                  ),
                  if (!_isRatingMode && _hasAnyRating())
                    TextButton(
                      onPressed: () {
                        setState(() {
                          _isRatingMode = true;
                        });
                      },
                      child: Text(languageManager.translate('edit_rating')),
                    ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),
          
          if (_isRatingMode || !_hasAnyRating()) ...[
            // 评分控件
            Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Column(
                  children: [
                    _buildRatingRow(languageManager.translate('accuracy'), _accuracy, (rating) {
                      setState(() {
                        _accuracy = rating.toInt();
                        _hasChanges = true;
                      });
                    }),
                    const SizedBox(height: 12),
                    _buildRatingRow(languageManager.translate('usefulness'), _usefulness, (rating) {
                      setState(() {
                        _usefulness = rating.toInt();
                        _hasChanges = true;
                      });
                    }),
                    const SizedBox(height: 12),
                    _buildRatingRow(languageManager.translate('satisfaction'), _satisfaction, (rating) {
                      setState(() {
                        _satisfaction = rating.toInt();
                        _hasChanges = true;
                      });
                    }),
                  ],
                );
              },
            ),
            const SizedBox(height: 16),
            
            // 评论输入框
            _buildFeedbackField(),
            const SizedBox(height: 16),
            
            // 提交按钮
            _buildActionButtons(),
          ] else ...[
            // 显示已有评分
            _buildRatingDisplay(),
          ],
        ],
      ),
    );
  }

  Widget _buildRatingRow(String label, int? currentRating, Function(double) onRatingUpdate) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        const SizedBox(width: 16),
        RatingBar.builder(
          initialRating: (currentRating ?? 0).toDouble(),
          minRating: 1,
          direction: Axis.horizontal,
          allowHalfRating: false,
          itemCount: 5,
          itemSize: 20,
          itemPadding: const EdgeInsets.symmetric(horizontal: 2.0),
          itemBuilder: (context, _) => const Icon(
            Icons.star,
            color: Colors.amber,
          ),
          onRatingUpdate: onRatingUpdate,
        ),
      ],
    );
  }

  Widget _buildFeedbackField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Text(
              languageManager.translate('feedback'),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            );
          },
        ),
        const SizedBox(height: 8),
        TextField(
          controller: _feedbackController,
          maxLines: 3,
          onChanged: (value) {
            setState(() {
              _hasChanges = true;
            });
          },
          decoration: InputDecoration(
            hintText: Provider.of<LanguageManager>(context).translate('share_experience_feedback'),
            hintStyle: TextStyle(
              color: Colors.black.withOpacity(0.6),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(
                color: Colors.black.withOpacity(0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(
                color: Colors.black,
                width: 2,
              ),
            ),
            contentPadding: const EdgeInsets.all(12),
          ),
          style: const TextStyle(
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        if (_isRatingMode) ...[
          TextButton(
            onPressed: () {
              setState(() {
                _isRatingMode = false;
                _hasChanges = false;
                // 重置为原始值
                _accuracy = widget.reading.accuracy;
                _usefulness = widget.reading.usefulness;
                _satisfaction = widget.reading.satisfaction;
                _feedbackController.text = widget.reading.feedback ?? '';
              });
            },
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(
                  languageManager.translate('cancel'),
                  style: const TextStyle(color: Colors.black),
                );
              },
            ),
          ),
          const SizedBox(width: 8),
          ElevatedButton(
            onPressed: _hasChanges ? _submitRating : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                return Text(languageManager.translate('save_rating'));
              },
            ),
          ),
        ] else if (!_hasAnyRating()) ...[
          ElevatedButton(
            onPressed: () {
              setState(() {
                _isRatingMode = true;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.black,
              foregroundColor: Colors.white,
            ),
            child: const Text('添加评价'),
          ),
        ],
      ],
    );
  }

  Widget _buildRatingDisplay() {
    if (!_hasAnyRating()) return const SizedBox();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_accuracy != null) _buildRatingDisplayRow(languageManager.translate('accuracy'), _accuracy!),
                if (_usefulness != null) _buildRatingDisplayRow(languageManager.translate('usefulness'), _usefulness!),
                if (_satisfaction != null) _buildRatingDisplayRow(languageManager.translate('satisfaction'), _satisfaction!),
              ],
            );
          },
        ),
        if (widget.reading.feedback?.isNotEmpty == true) ...[
          const SizedBox(height: 12),
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                languageManager.translate('feedback'),
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              );
            },
          ),
          const SizedBox(height: 4),
          Text(
            widget.reading.feedback!,
            style: TextStyle(
              fontSize: 14,
              color: Colors.black.withOpacity(0.8),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildRatingDisplayRow(String label, int rating) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.black,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Row(
            children: List.generate(5, (index) {
              return Icon(
                index < rating ? Icons.star : Icons.star_border,
                color: Colors.amber,
                size: 16,
              );
            }),
          ),
          const SizedBox(width: 8),
          Text(
            '$rating/5',
            style: TextStyle(
              fontSize: 12,
              color: Colors.black.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  bool _hasAnyRating() {
    return _accuracy != null && _usefulness != null && _satisfaction != null;
  }

  void _submitRating() {
    if (widget.onRate != null) {
      widget.onRate!(
        _accuracy,
        _usefulness,
        _satisfaction,
        _feedbackController.text.trim().isEmpty ? null : _feedbackController.text.trim(),
      );
    }
    
    setState(() {
      _isRatingMode = false;
      _hasChanges = false;
    });
    
    final languageManager = Provider.of<LanguageManager>(context, listen: false);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(languageManager.translate('rating_submitted')),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildLeadingIcon() {
    IconData icon;
    Color color;
    
    switch (widget.reading.spreadType) {
      case SpreadType.single:
        icon = Icons.filter_1;
        color = Colors.blue;
        break;
      case SpreadType.three:
        icon = Icons.filter_3;
        color = Colors.green;
        break;
      case SpreadType.celtic:
        icon = Icons.grid_on;
        color = Colors.purple;
        break;
      default:
        icon = Icons.credit_card;
        color = Colors.grey;
    }
    
    return CircleAvatar(
      backgroundColor: color.withOpacity(0.2),
      child: Icon(
        icon,
        color: color,
      ),
    );
  }

  Widget _buildCardsList() {
    print('🃏 历史记录卡牌数量: ${widget.reading.cards.length}');
    for (int i = 0; i < widget.reading.cards.length; i++) {
      final card = widget.reading.cards[i];
      print('   卡牌${i+1}: ${card.name} - ${card.imageUrl} - 逆位:${card.isReversed}');
    }

    if (widget.reading.cards.isEmpty) {
      return Container(
        height: 100,
        child: const Center(
          child: Text(
            '🃏 暂无卡牌信息',
            style: TextStyle(color: Colors.grey),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: const Color(0xFF667eea).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFF667eea).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.style,
                color: Color(0xFF667eea),
                size: 16,
              ),
              const SizedBox(width: 8),
              const Text(
                '🃏 抽取的卡牌',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Color(0xFF667eea),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: widget.reading.cards.map((card) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: card.isReversed
                    ? Colors.red.withValues(alpha: 0.1)
                    : Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: card.isReversed
                      ? Colors.red.withValues(alpha: 0.3)
                      : Colors.green.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  '${card.name}${card.isReversed ? " (逆位)" : ""}',
                  style: TextStyle(
                    fontSize: 12,
                    color: card.isReversed ? Colors.red[700] : Colors.green[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  // 📋 构建完整对话流程
  Widget _buildCompleteConversation() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 🃏 抽取的卡牌
        _buildCardsList(),
        const SizedBox(height: 16),

        // 🤖 初始AI解读
        if (widget.reading.interpretation.isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFF667eea).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: const Color(0xFF667eea).withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 移除标题，直接显示解读内容
                Text(
                  widget.reading.interpretation,
                  style: TextStyle(
                    color: Colors.black.withOpacity(0.9),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
        ],

        // 💬 追问对话
        if (widget.reading.followUpQuestions.isNotEmpty) ...[
          const Text(
            '💬 追问对话',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          ...widget.reading.followUpQuestions.asMap().entries.map((entry) {
            final index = entry.key;
            final question = entry.value;
            return Padding(
              padding: const EdgeInsets.only(bottom: 12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 👤 用户问题
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text('👤 ', style: TextStyle(fontSize: 14)),
                        Expanded(
                          child: Text(
                            question,
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.black,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  // 🤖 AI回复
                  if (index < widget.reading.followUpResponses.length)
                    Container(
                      padding: const EdgeInsets.all(10),
                      decoration: BoxDecoration(
                        color: const Color(0xFF667eea).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text('🤖 ', style: TextStyle(fontSize: 14)),
                          Expanded(
                            child: Text(
                              widget.reading.followUpResponses[index],
                              style: TextStyle(
                                color: Colors.black.withOpacity(0.9),
                                fontSize: 14,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ],
      ],
    );
  }
}
