import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/screens/topic_question_screen.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';

class RollingTopicGallery extends StatefulWidget {
  final bool autoplay;
  final bool pauseOnHover;
  
  const RollingTopicGallery({
    super.key,
    this.autoplay = true,
    this.pauseOnHover = true,
  });

  @override
  State<RollingTopicGallery> createState() => _RollingTopicGalleryState();
}

class _RollingTopicGalleryState extends State<RollingTopicGallery>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;
  
  double _currentRotation = 0;
  bool _isDragging = false;
  bool _isHovering = false;

  final List<TopicCard> _topics = [
    TopicCard(
      titleKey: 'quick_decision',
      subtitleKey: 'quick_decision_subtitle',
      internalName: '快速决策',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.help_outline,
      subTopics: [
        SubTopic(
          titleKey: 'yes_or_no',
          subtitleKey: 'yes_or_no_subtitle',
          internalName: 'Yes or No',
          icon: Icons.help_outline,
        ),
        SubTopic(
          titleKey: 'two_choice_decision',
          subtitleKey: 'two_choice_subtitle',
          internalName: '二选一抉择',
          icon: Icons.compare_arrows,
        ),
        SubTopic(
          titleKey: 'three_choice_decision',
          subtitleKey: 'three_choice_subtitle',
          internalName: '三选一抉择',
          icon: Icons.alt_route,
        ),
      ],
    ),
    TopicCard(
      titleKey: 'emotional_relationship',
      subtitleKey: 'emotional_relationship_subtitle',
      internalName: '情感关系',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.favorite,
      subTopics: [
        SubTopic(
          titleKey: 'true_love_timing',
          subtitleKey: 'true_love_timing_subtitle',
          internalName: '真爱时机',
          icon: Icons.favorite,
        ),
        SubTopic(
          titleKey: 'breakup_reconciliation',
          subtitleKey: 'breakup_reconciliation_subtitle',
          internalName: '分手复合',
          icon: Icons.healing,
        ),
        SubTopic(
          titleKey: 'secret_crush',
          subtitleKey: 'secret_crush_subtitle',
          internalName: '暗恋心意',
          icon: Icons.favorite_border,
        ),
      ],
    ),
    TopicCard(
      titleKey: 'career_studies',
      subtitleKey: 'career_studies_subtitle',
      internalName: '事业学业',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.trending_up,
      subTopics: [
        SubTopic(
          titleKey: 'career_development',
          subtitleKey: 'career_development_subtitle',
          internalName: '事业发展',
          icon: Icons.trending_up,
        ),
        SubTopic(
          titleKey: 'job_change',
          subtitleKey: 'job_change_subtitle',
          internalName: '跳槽转职',
          icon: Icons.work_outline,
        ),
        SubTopic(
          titleKey: 'exam_fortune',
          subtitleKey: 'exam_fortune_subtitle',
          internalName: '考试运势',
          icon: Icons.school,
        ),
      ],
    ),
    TopicCard(
      titleKey: 'inner_exploration',
      subtitleKey: 'inner_exploration_subtitle',
      internalName: '内在探索',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.psychology,
      subTopics: [
        SubTopic(
          titleKey: 'self_awareness',
          subtitleKey: 'self_awareness_subtitle',
          internalName: '自我认知',
          icon: Icons.psychology,
        ),
      ],
    ),
    TopicCard(
      titleKey: 'healing_advice',
      subtitleKey: 'healing_advice_subtitle',
      internalName: '疗愈建议',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.spa,
      subTopics: [
        SubTopic(
          titleKey: 'emotional_healing',
          subtitleKey: 'emotional_healing_subtitle',
          internalName: '情绪疗愈',
          icon: Icons.spa,
        ),
      ],
    ),
    TopicCard(
      titleKey: 'pet_topics',
      subtitleKey: 'pet_topics_subtitle',
      internalName: '宠物专题',
      gradient: [Colors.white, Colors.grey.shade50],
      icon: Icons.pets,
      subTopics: [
        SubTopic(
          titleKey: 'pet_emotions',
          subtitleKey: 'pet_emotions_subtitle',
          internalName: '宠物情绪',
          icon: Icons.pets,
        ),
        SubTopic(
          titleKey: 'pet_compatibility',
          subtitleKey: 'pet_compatibility_subtitle',
          internalName: '宠物缘分',
          icon: Icons.favorite,
        ),
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _rotationAnimation.addListener(() {
      setState(() {
        _currentRotation = _rotationAnimation.value;
      });
    });

    if (widget.autoplay) {
      _startAutoRotation();
    }
  }

  void _startAutoRotation() {
    if (!_isDragging && (!_isHovering || !widget.pauseOnHover)) {
      _rotationController.repeat();
    }
  }

  void _stopAutoRotation() {
    _rotationController.stop();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final cylinderRadius = math.min(screenWidth * 0.7, 300.0);
    final cardWidth = math.min(screenWidth * 0.75, 320.0);
    final cardHeight = math.min(screenHeight * 0.6, 450.0);

    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Container(
          height: screenHeight * 0.8,
          width: double.infinity,
          child: MouseRegion(
            onEnter: (_) {
              setState(() => _isHovering = true);
              if (widget.pauseOnHover) _stopAutoRotation();
            },
            onExit: (_) {
              setState(() => _isHovering = false);
              if (widget.autoplay) _startAutoRotation();
            },
            child: GestureDetector(
              onPanStart: (_) {
                setState(() => _isDragging = true);
                _stopAutoRotation();
              },
              onPanUpdate: (details) {
                setState(() {
                  _currentRotation += details.delta.dx * 0.008;
                });
              },
              onPanEnd: (_) {
                setState(() => _isDragging = false);
                if (widget.autoplay) _startAutoRotation();
              },
              child: Stack(
                children: [
                  // 左侧渐变遮罩
                  Positioned(
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: 60,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            const Color(0xFFF8F9FA),
                            const Color(0xFFF8F9FA).withOpacity(0),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 右侧渐变遮罩
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    width: 60,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerRight,
                          end: Alignment.centerLeft,
                          colors: [
                            const Color(0xFFF8F9FA),
                            const Color(0xFFF8F9FA).withOpacity(0),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 3D圆柱画廊
                  Center(
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.0008), // 透视效果
                      child: SizedBox(
                        width: screenWidth,
                        height: cardHeight,
                        child: Stack(
                          children: _topics.asMap().entries.map((entry) {
                            final index = entry.key;
                            final topic = entry.value;
                            final angle = (2 * math.pi / _topics.length) * index + _currentRotation;

                            final x = cylinderRadius * math.cos(angle);
                            final z = cylinderRadius * math.sin(angle);

                            // 计算卡片的可见性和缩放 - 修改为正中间时最近
                            // 当z值最小（最接近屏幕）时，卡片应该最清晰
                            final visibility = (-math.sin(angle) + 1) / 2; // 修改为sin，使正中间最清晰
                            final scale = 0.6 + (visibility * 0.4);

                            return Positioned(
                              left: screenWidth / 2 + x - cardWidth / 2,
                              top: (screenHeight * 0.8 - cardHeight) / 2,
                              child: Transform(
                                alignment: Alignment.center,
                                transform: Matrix4.identity()
                                  ..translate(0.0, 0.0, z * 0.5)
                                  ..scale(scale)
                                  ..rotateY(angle * 0.05), // 更轻微的Y轴旋转，使卡片更容易正对屏幕
                                child: Opacity(
                                  opacity: math.max(0.4, visibility),
                                  child: _buildTopicCard(
                                    context,
                                    languageManager,
                                    topic,
                                    cardWidth,
                                    cardHeight,
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopicCard(
    BuildContext context,
    LanguageManager languageManager,
    TopicCard topic,
    double width,
    double height,
  ) {
    return Container(
      width: width,
      height: 420, // 统一高度
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: topic.gradient,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(24),
          child: Column(
            children: [
              // 头部区域
              Container(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // 主图标
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Icon(
                        topic.icon,
                        color: Colors.grey.shade600,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),

                    // 主标题
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageManager.translate(topic.titleKey),
                            style: const TextStyle(
                              color: Colors.black87,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '${topic.subTopics.length} ${languageManager.translate('options_count')}',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // 细分题目列表
              Expanded(
                child: Container(
                  margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.85),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withOpacity(0.9),
                            width: 2,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.15),
                              blurRadius: 20,
                              offset: const Offset(0, 8),
                            ),
                            BoxShadow(
                              color: Colors.white.withOpacity(0.8),
                              blurRadius: 10,
                              offset: const Offset(0, -2),
                            ),
                          ],
                        ),
                        child: ListView.separated(
                          padding: const EdgeInsets.all(12),
                          itemCount: topic.subTopics.length,
                          separatorBuilder: (context, index) => const SizedBox(height: 8),
                          itemBuilder: (context, index) {
                            final subTopic = topic.subTopics[index];
                            return _buildSubTopicItem(context, languageManager, subTopic);
                          },
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubTopicItem(
    BuildContext context,
    LanguageManager languageManager,
    SubTopic subTopic,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _navigateToSubTopic(context, subTopic),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.shade200,
              width: 0.5,
            ),
          ),
          child: Row(
            children: [
              // 小图标
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  subTopic.icon,
                  color: Colors.grey.shade600,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),

              // 文字内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageManager.translate(subTopic.titleKey),
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      languageManager.translate(subTopic.subtitleKey),
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 11,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 箭头图标
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey.shade400,
                size: 14,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToSubTopic(BuildContext context, SubTopic subTopic) {
    final spreads = TarotSpread.getSpreadsForTopic(subTopic.internalName);
    final selectedSpread = spreads.isNotEmpty ? spreads.first : TarotSpread.getSpreadByName('三张牌阵（经典）')!;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicQuestionScreen(
          topicTitle: subTopic.internalName,
          topicSubtitle: Provider.of<LanguageManager>(context, listen: false).translate(subTopic.subtitleKey),
          spreadType: selectedSpread.name,
        ),
      ),
    );
  }
}

class TopicCard {
  final String titleKey;
  final String subtitleKey;
  final String internalName;
  final List<Color> gradient;
  final IconData icon;
  final List<SubTopic> subTopics;

  TopicCard({
    required this.titleKey,
    required this.subtitleKey,
    required this.internalName,
    required this.gradient,
    required this.icon,
    required this.subTopics,
  });
}

class SubTopic {
  final String titleKey;
  final String subtitleKey;
  final String internalName;
  final IconData icon;

  SubTopic({
    required this.titleKey,
    required this.subtitleKey,
    required this.internalName,
    required this.icon,
  });
}
