import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:ai_tarot_reading/screens/topic_question_screen.dart';
import 'package:ai_tarot_reading/models/tarot_spread.dart';

class EnhancedRollingGallery extends StatefulWidget {
  final bool autoplay;
  final bool pauseOnHover;
  final double dragFactor;
  
  const EnhancedRollingGallery({
    super.key,
    this.autoplay = true,
    this.pauseOnHover = true,
    this.dragFactor = 0.05,
  });

  @override
  State<EnhancedRollingGallery> createState() => _EnhancedRollingGalleryState();
}

class _EnhancedRollingGalleryState extends State<EnhancedRollingGallery>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _hoverController;
  late Animation<double> _rotationAnimation;
  
  double _currentRotation = 0;
  double _dragVelocity = 0;
  bool _isDragging = false;
  bool _isHovering = false;
  int _hoveredIndex = -1;

  // 专题数据 - 每个卡片包含主题+多个细分问题
  final List<TopicGalleryItem> _topics = [];

  @override
  void initState() {
    super.initState();
    
    _rotationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    _rotationAnimation.addListener(() {
      if (!_isDragging) {
        setState(() {
          _currentRotation = _rotationAnimation.value;
        });
      }
    });

    _generateTopics();
    
    if (widget.autoplay) {
      _startInfiniteSpin();
    }
  }

  void _generateTopics() {
    _topics.addAll([
      TopicGalleryItem(
        titleKey: 'quick_decision',
        subtitleKey: 'quick_decision_subtitle',
        gradient: [const Color(0xFF8B5CF6), const Color(0xFF3B82F6)],
        icon: Icons.help_outline,
        subQuestions: [
          SubQuestion('yes_or_no', 'yes_or_no_subtitle', 'yes_or_no', Icons.help_outline),
          SubQuestion('two_choice_decision', 'two_choice_subtitle', 'two_choice_decision', Icons.compare_arrows),
          SubQuestion('three_choice_decision', 'three_choice_subtitle', 'three_choice_decision', Icons.alt_route),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'emotional_relationship',
        subtitleKey: 'emotional_relationship_subtitle',
        gradient: [const Color(0xFFEC4899), const Color(0xFFEF4444)],
        icon: Icons.favorite,
        subQuestions: [
          SubQuestion('true_love_timing', 'true_love_timing_subtitle', 'true_love_timing', Icons.favorite),
          SubQuestion('breakup_reconciliation', 'breakup_reconciliation_subtitle', 'breakup_reconciliation', Icons.healing),
          SubQuestion('secret_crush', 'secret_crush_subtitle', 'secret_crush', Icons.favorite_border),
          SubQuestion('third_party_issues', 'third_party_issues_subtitle', 'third_party_issues', Icons.warning_amber),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'career_studies',
        subtitleKey: 'career_studies_subtitle',
        gradient: [const Color(0xFF059669), const Color(0xFF10B981)],
        icon: Icons.trending_up,
        subQuestions: [
          SubQuestion('career_development', 'career_development_subtitle', 'career_development', Icons.trending_up),
          SubQuestion('job_change', 'job_change_subtitle', 'job_change', Icons.work_outline),
          SubQuestion('promotion_raise', 'promotion_raise_subtitle', 'promotion_raise', Icons.arrow_upward),
          SubQuestion('exam_fortune', 'exam_fortune_subtitle', 'exam_fortune', Icons.school),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'inner_exploration',
        subtitleKey: 'inner_exploration_subtitle',
        gradient: [const Color(0xFF7C3AED), const Color(0xFF8B5CF6)],
        icon: Icons.psychology,
        subQuestions: [
          SubQuestion('self_awareness', 'self_awareness_subtitle', 'self_awareness', Icons.psychology),
          SubQuestion('life_purpose', 'life_purpose_subtitle', 'life_purpose', Icons.track_changes),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'healing_advice',
        subtitleKey: 'healing_advice_subtitle',
        gradient: [const Color(0xFF06B6D4), const Color(0xFF8B5CF6)],
        icon: Icons.spa,
        subQuestions: [
          SubQuestion('emotional_healing', 'emotional_healing_subtitle', 'emotional_healing', Icons.spa),
          SubQuestion('stress_relief', 'stress_relief_subtitle', 'stress_relief', Icons.self_improvement),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'life_guidance',
        subtitleKey: 'life_guidance_subtitle',
        gradient: [const Color(0xFFF59E0B), const Color(0xFFEC4899)],
        icon: Icons.lightbulb,
        subQuestions: [
          SubQuestion('daily_inspiration', 'daily_inspiration_subtitle', 'daily_inspiration', Icons.lightbulb),
          SubQuestion('wealth_fortune', 'wealth_fortune_subtitle', 'wealth_fortune', Icons.attach_money),
        ],
      ),
      TopicGalleryItem(
        titleKey: 'pet_topics',
        subtitleKey: 'pet_topics_subtitle',
        gradient: [const Color(0xFFF59E0B), const Color(0xFF10B981)],
        icon: Icons.pets,
        subQuestions: [
          SubQuestion('pet_emotions', 'pet_emotions_subtitle', 'pet_emotions', Icons.pets),
          SubQuestion('pet_compatibility', 'pet_compatibility_subtitle', 'pet_compatibility', Icons.favorite),
          SubQuestion('pet_health', 'pet_health_subtitle', 'pet_health', Icons.health_and_safety),
        ],
      ),
    ]);
  }

  void _startInfiniteSpin() {
    if (!_isDragging && (!_isHovering || !widget.pauseOnHover)) {
      _rotationController.repeat();
    }
  }

  void _stopSpin() {
    _rotationController.stop();
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _hoverController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenWidth <= 640;
    
    final cylinderWidth = isSmallScreen ? 1100.0 : 1800.0;
    final faceCount = _topics.length;
    final faceWidth = (cylinderWidth / faceCount) * 1.5;
    final radius = cylinderWidth / (2 * math.pi);
    final cardHeight = isSmallScreen ? 280.0 : 320.0;

    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        return Container(
          height: 500,
          width: double.infinity,
          child: MouseRegion(
            onEnter: (_) {
              setState(() => _isHovering = true);
              if (widget.pauseOnHover) _stopSpin();
            },
            onExit: (_) {
              setState(() => _isHovering = false);
              if (widget.autoplay) _startInfiniteSpin();
            },
            child: GestureDetector(
              onPanStart: (_) {
                setState(() => _isDragging = true);
                _stopSpin();
              },
              onPanUpdate: (details) {
                setState(() {
                  _currentRotation += details.delta.dx * widget.dragFactor * 0.02;
                  _dragVelocity = details.delta.dx;
                });
              },
              onPanEnd: (details) {
                setState(() => _isDragging = false);
                final finalRotation = _currentRotation + (_dragVelocity * widget.dragFactor * 0.1);
                _currentRotation = finalRotation;
                if (widget.autoplay) _startInfiniteSpin();
              },
              child: Stack(
                children: [
                  // 左侧渐变遮罩
                  Positioned(
                    left: 0,
                    top: 0,
                    bottom: 0,
                    width: 48,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                          colors: [
                            const Color(0xFF060010),
                            const Color(0xFF060010).withOpacity(0),
                          ],
                        ),
                      ),
                    ),
                  ),
                  
                  // 右侧渐变遮罩
                  Positioned(
                    right: 0,
                    top: 0,
                    bottom: 0,
                    width: 48,
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerRight,
                          end: Alignment.centerLeft,
                          colors: [
                            const Color(0xFF060010),
                            const Color(0xFF060010).withOpacity(0),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // 3D圆柱画廊
                  Center(
                    child: Transform(
                      alignment: Alignment.center,
                      transform: Matrix4.identity()
                        ..setEntry(3, 2, 0.001), // 透视效果
                      child: SizedBox(
                        width: cylinderWidth,
                        height: cardHeight,
                        child: Stack(
                          children: _topics.asMap().entries.map((entry) {
                            final index = entry.key;
                            final topic = entry.value;
                            final angle = (2 * math.pi / faceCount) * index + _currentRotation;
                            
                            final x = radius * math.cos(angle);
                            final z = radius * math.sin(angle);
                            
                            // 计算可见性和缩放
                            final visibility = (math.cos(angle) + 1) / 2;
                            final scale = 0.7 + (visibility * 0.3);
                            final isHovered = index == _hoveredIndex;
                            final hoverScale = isHovered ? 1.05 : 1.0;
                            
                            return Positioned(
                              left: screenWidth / 2 + x - faceWidth / 2,
                              top: (500 - cardHeight) / 2,
                              child: Transform(
                                alignment: Alignment.center,
                                transform: Matrix4.identity()
                                  ..translate(0.0, 0.0, z)
                                  ..scale(scale * hoverScale),
                                child: Opacity(
                                  opacity: math.max(0.4, visibility),
                                  child: MouseRegion(
                                    onEnter: (_) => setState(() => _hoveredIndex = index),
                                    onExit: (_) => setState(() => _hoveredIndex = -1),
                                    child: _buildTopicCard(
                                      context,
                                      languageManager,
                                      topic,
                                      faceWidth,
                                      cardHeight,
                                      isHovered,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopicCard(
    BuildContext context,
    LanguageManager languageManager,
    TopicGalleryItem topic,
    double width,
    double height,
    bool isHovered,
  ) {
    return Container(
      width: width,
      height: height,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: topic.gradient,
          ),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 3,
          ),
          boxShadow: [
            BoxShadow(
              color: topic.gradient.first.withOpacity(0.4),
              blurRadius: isHovered ? 25 : 15,
              offset: const Offset(0, 10),
            ),
            if (isHovered)
              BoxShadow(
                color: Colors.white.withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 0),
              ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 主题标题区域
                Row(
                  children: [
                    Container(
                      width: 50,
                      height: 50,
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.25),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      child: Icon(
                        topic.icon,
                        color: Colors.white,
                        size: 28,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            languageManager.translate(topic.titleKey),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Consumer<LanguageManager>(
                            builder: (context, languageManager, child) {
                              return Text(
                                '${topic.subQuestions.length} ${languageManager.translate('options')}',
                                style: TextStyle(
                                  color: Colors.white.withOpacity(0.8),
                                  fontSize: 12,
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // 细分问题列表
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: Colors.white.withOpacity(0.2),
                        width: 1,
                      ),
                    ),
                    child: ListView.separated(
                      padding: const EdgeInsets.all(12),
                      itemCount: topic.subQuestions.length,
                      separatorBuilder: (context, index) => const SizedBox(height: 8),
                      itemBuilder: (context, index) {
                        final subQuestion = topic.subQuestions[index];
                        return _buildSubQuestionItem(context, languageManager, subQuestion);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSubQuestionItem(
    BuildContext context,
    LanguageManager languageManager,
    SubQuestion subQuestion,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _navigateToSubQuestion(context, subQuestion),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.white.withOpacity(0.2),
              width: 0.5,
            ),
          ),
          child: Row(
            children: [
              // 小图标
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  subQuestion.icon,
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),

              // 文字内容
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      languageManager.translate(subQuestion.titleKey),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      languageManager.translate(subQuestion.subtitleKey),
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.8),
                        fontSize: 11,
                        height: 1.2,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),

              // 箭头图标
              Icon(
                Icons.arrow_forward_ios,
                color: Colors.white.withOpacity(0.6),
                size: 14,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToSubQuestion(BuildContext context, SubQuestion subQuestion) {
    final spreads = TarotSpread.getSpreadsForTopic(subQuestion.internalName);
    final selectedSpread = spreads.isNotEmpty ? spreads.first : TarotSpread.getSpreadByName('三张牌阵（经典）')!;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TopicQuestionScreen(
          topicTitle: subQuestion.internalName,
          topicSubtitle: Provider.of<LanguageManager>(context, listen: false).translate(subQuestion.subtitleKey),
          spreadType: selectedSpread.name,
        ),
      ),
    );
  }
}

class TopicGalleryItem {
  final String titleKey;
  final String subtitleKey;
  final List<Color> gradient;
  final IconData icon;
  final List<SubQuestion> subQuestions;

  TopicGalleryItem({
    required this.titleKey,
    required this.subtitleKey,
    required this.gradient,
    required this.icon,
    required this.subQuestions,
  });
}

class SubQuestion {
  final String titleKey;
  final String subtitleKey;
  final String internalName;
  final IconData icon;

  SubQuestion(this.titleKey, this.subtitleKey, this.internalName, this.icon);
}
