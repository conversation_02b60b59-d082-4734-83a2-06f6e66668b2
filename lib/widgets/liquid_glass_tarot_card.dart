import 'package:flutter/material.dart';
import 'dart:ui';
import 'dart:math' as math;
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../models/tarot_card.dart';
import '../utils/tarot_image_manager.dart';
import '../utils/language_manager.dart';
import '../theme/liquid_glass_theme.dart';

/// 🔮 Liquid Glass风格的塔罗卡牌
/// 融合苹果设计语言与少女美学
class LiquidGlassTarotCard extends StatefulWidget {
  final TarotCard card;
  final double width;
  final double height;
  final VoidCallback? onTap;
  final bool showOrientation;
  final bool enableAnimation;
  final bool enableGlow;

  const LiquidGlassTarotCard({
    super.key,
    required this.card,
    this.width = 200,
    this.height = 300,
    this.onTap,
    this.showOrientation = true,
    this.enableAnimation = true,
    this.enableGlow = false,
  });

  @override
  State<LiquidGlassTarotCard> createState() => _LiquidGlassTarotCardState();
}

class _LiquidGlassTarotCardState extends State<LiquidGlassTarotCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _glowController;
  late AnimationController _floatController;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _glowController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _floatController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    if (widget.enableAnimation) {
      _floatController.repeat(reverse: true);
    }

    if (widget.enableGlow) {
      _glowController.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _glowController.dispose();
    _floatController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_hoverController, _glowController, _floatController]),
      builder: (context, child) {
        final hoverScale = 1.0 + (_hoverController.value * 0.05);
        final floatOffset = math.sin(_floatController.value * 2 * math.pi) * 3;
        final glowIntensity = (_glowController.value * 0.3) + 0.1;
        
        return Transform.translate(
          offset: Offset(0, _isPressed ? 2 : floatOffset),
          child: Transform.scale(
            scale: hoverScale,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              onTap: widget.onTap,
              child: Container(
                width: widget.width,
                height: widget.height,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(LiquidGlassTheme.cardBorderRadius),
                  boxShadow: [
                    // 🌟 主要阴影
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                      spreadRadius: 0,
                    ),
                    // ✨ 发光效果
                    if (widget.enableGlow)
                      BoxShadow(
                        color: _getCardAccentColor().withOpacity(glowIntensity),
                        blurRadius: 40,
                        offset: const Offset(0, 0),
                        spreadRadius: 8,
                      ),
                  ],
                ),
                child: Stack(
                  children: [
                    // 🎨 主要卡牌容器
                    _buildMainCard(),
                    
                    // 🔮 正逆位指示器
                    if (widget.showOrientation)
                      _buildOrientationIndicator(),
                    
                    // ✨ 装饰性元素
                    _buildDecorativeElements(),
                    
                    // 🌙 卡牌信息叠加层
                    _buildInfoOverlay(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _hoverController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _hoverController.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _hoverController.reverse();
  }

  Widget _buildMainCard() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(LiquidGlassTheme.cardBorderRadius),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(LiquidGlassTheme.cardBorderRadius),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white.withOpacity(0.4),
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.3),
              ],
              stops: const [0.0, 0.5, 1.0],
            ),
            border: Border.all(
              color: Colors.white.withOpacity(0.3),
              width: 1.5,
            ),
          ),
          child: Stack(
            children: [
              // 🖼️ 卡牌图片
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(LiquidGlassTheme.cardBorderRadius - 1.5),
                  child: Transform(
                    alignment: Alignment.center,
                    transform: Matrix4.identity()
                      ..rotateZ(widget.card.isReversed ? math.pi : 0),
                    child: Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            _getCardAccentColor().withOpacity(0.1),
                          ],
                        ),
                      ),
                      child: TarotImageManager.buildCardImage(
                        cardName: widget.card.name,
                        width: widget.width,
                        height: widget.height,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
              
              // 🌈 内部光泽效果
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(LiquidGlassTheme.cardBorderRadius - 1.5),
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withOpacity(0.3),
                        Colors.transparent,
                        _getCardAccentColor().withOpacity(0.1),
                      ],
                      stops: const [0.0, 0.6, 1.0],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrientationIndicator() {
    if (!widget.card.isReversed) return const SizedBox.shrink();
    
    return Positioned(
      top: 12,
      right: 12,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.red.withOpacity(0.9),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Colors.white.withOpacity(0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.rotate_left,
              color: Colors.white,
              size: 12,
            ),
            const SizedBox(width: 2),
            const Text(
              '逆位',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ).animate(delay: 300.ms).fadeIn(duration: 400.ms).scale(begin: const Offset(0.8, 0.8)),
    );
  }

  Widget _buildDecorativeElements() {
    return Positioned.fill(
      child: Stack(
        children: [
          // 💫 左上角装饰
          Positioned(
            top: 20,
            left: 20,
            child: Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    _getCardAccentColor().withOpacity(0.3),
                    Colors.transparent,
                  ],
                ),
              ),
            ).animate(
              onPlay: (controller) => controller.repeat(reverse: true),
            ).scale(
              begin: const Offset(0.8, 0.8),
              end: const Offset(1.2, 1.2),
              duration: 3000.ms,
            ),
          ),
          
          // ✨ 右下角装饰
          Positioned(
            bottom: 20,
            right: 20,
            child: Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: RadialGradient(
                  colors: [
                    Colors.white.withOpacity(0.6),
                    Colors.transparent,
                  ],
                ),
              ),
            ).animate(
              onPlay: (controller) => controller.repeat(reverse: true),
            ).scale(
              begin: const Offset(0.5, 0.5),
              end: const Offset(1.0, 1.0),
              duration: 2500.ms,
              delay: 1000.ms,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoOverlay() {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: ClipRRect(
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(LiquidGlassTheme.cardBorderRadius - 1.5),
          bottomRight: Radius.circular(LiquidGlassTheme.cardBorderRadius - 1.5),
        ),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  _getCardAccentColor().withOpacity(0.8),
                ],
              ),
            ),
            child: Consumer<LanguageManager>(
              builder: (context, languageManager, child) {
                final translatedName = widget.card.getTranslatedName(languageManager.translate);
                final arcanaType = widget.card.isMajorArcana
                    ? languageManager.translate('major_arcana')
                    : languageManager.translate('minor_arcana');

                return Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      translatedName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: Colors.black54,
                            offset: Offset(0, 1),
                            blurRadius: 3,
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        arcanaType,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Color _getCardAccentColor() {
    if (widget.card.isReversed) {
      return const Color(0xFFE91E63); // 粉红色
    } else if (widget.card.isMajorArcana) {
      return const Color(0xFF9C27B0); // 紫色
    } else {
      return const Color(0xFF2196F3); // 蓝色
    }
  }
}

/// 🌸 迷你塔罗卡牌（用于列表展示）
class MiniLiquidGlassTarotCard extends StatelessWidget {
  final TarotCard card;
  final double size;
  final VoidCallback? onTap;

  const MiniLiquidGlassTarotCard({
    super.key,
    required this.card,
    this.size = 80,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size * 1.4,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Stack(
              children: [
                // 背景图片
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..rotateZ(card.isReversed ? math.pi : 0),
                  child: TarotImageManager.buildCardImage(
                    cardName: card.name,
                    width: size,
                    height: size * 1.4,
                    fit: BoxFit.cover,
                  ),
                ),
                
                // 玻璃覆盖层
                Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withOpacity(0.1),
                        Colors.transparent,
                        Colors.black.withOpacity(0.3),
                      ],
                    ),
                  ),
                ),
                
                // 逆位指示器
                if (card.isReversed)
                  Positioned(
                    top: 4,
                    right: 4,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 1),
                      ),
                      child: const Icon(
                        Icons.rotate_left,
                        color: Colors.white,
                        size: 10,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} 