import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/langfuse_service.dart';

class AIResponseFeedback extends StatefulWidget {
  final String traceId;
  final String generationId;
  final String aiResponse;
  final VoidCallback? onFeedbackSubmitted;

  const AIResponseFeedback({
    super.key,
    required this.traceId,
    required this.generationId,
    required this.aiResponse,
    this.onFeedbackSubmitted,
  });

  @override
  State<AIResponseFeedback> createState() => _AIResponseFeedbackState();
}

class _AIResponseFeedbackState extends State<AIResponseFeedback> {
  double? _helpfulnessRating;
  double? _accuracyRating;
  double? _empathyRating;
  String? _feedbackComment;
  bool _feedbackSubmitted = false;

  @override
  Widget build(BuildContext context) {
    if (_feedbackSubmitted) {
      return Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(top: 8),
        decoration: BoxDecoration(
          color: Colors.green.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.green.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green, size: 16),
            const SizedBox(width: 8),
            Text(
              '感谢您的反馈！',
              style: TextStyle(
                color: Colors.green,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.only(top: 8),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.feedback, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 8),
              Text(
                '这个回复对您有帮助吗？',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          // 有用性评分
          _buildRatingRow(
            '有用性',
            _helpfulnessRating,
            (value) => setState(() => _helpfulnessRating = value),
          ),
          
          // 准确性评分
          _buildRatingRow(
            '准确性',
            _accuracyRating,
            (value) => setState(() => _accuracyRating = value),
          ),
          
          // 共情能力评分
          _buildRatingRow(
            '共情度',
            _empathyRating,
            (value) => setState(() => _empathyRating = value),
          ),
          
          const SizedBox(height: 8),
          
          // 文字反馈
          TextField(
            decoration: InputDecoration(
              hintText: '您还有什么建议吗？（可选）',
              hintStyle: TextStyle(fontSize: 11, color: Colors.grey[500]),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(6),
                borderSide: BorderSide(color: Colors.grey.withOpacity(0.3)),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            ),
            style: const TextStyle(fontSize: 11),
            maxLines: 2,
            onChanged: (value) => _feedbackComment = value,
          ),
          
          const SizedBox(height: 8),
          
          // 提交按钮
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => _submitFeedback(),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  minimumSize: Size.zero,
                ),
                child: Text(
                  '提交反馈',
                  style: TextStyle(fontSize: 11, color: Color(0xFF6B46C1)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRatingRow(String label, double? rating, Function(double) onChanged) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          SizedBox(
            width: 50,
            child: Text(
              label,
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ),
          Expanded(
            child: Row(
              children: List.generate(5, (index) {
                final starValue = index + 1.0;
                return GestureDetector(
                  onTap: () => onChanged(starValue),
                  child: Icon(
                    Icons.star,
                    size: 16,
                    color: rating != null && starValue <= rating
                        ? Colors.amber
                        : Colors.grey[300],
                  ),
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  void _submitFeedback() async {
    if (_helpfulnessRating == null && _accuracyRating == null && _empathyRating == null) {
      // 至少需要一个评分
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('请至少给出一项评分'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
      return;
    }

    try {
      // 提交有用性评分
      if (_helpfulnessRating != null) {
        await LangfuseService.createScore(
          traceId: widget.traceId,
          generationId: widget.generationId,
          name: 'helpfulness',
          value: _helpfulnessRating!,
          comment: _feedbackComment,
          metadata: {
            'response_preview': widget.aiResponse.substring(0, 50),
          },
        );
      }

      // 提交准确性评分
      if (_accuracyRating != null) {
        await LangfuseService.createScore(
          traceId: widget.traceId,
          generationId: widget.generationId,
          name: 'accuracy',
          value: _accuracyRating!,
          comment: _feedbackComment,
          metadata: {
            'response_preview': widget.aiResponse.substring(0, 50),
          },
        );
      }

      // 提交共情能力评分
      if (_empathyRating != null) {
        await LangfuseService.createScore(
          traceId: widget.traceId,
          generationId: widget.generationId,
          name: 'empathy',
          value: _empathyRating!,
          comment: _feedbackComment,
          metadata: {
            'response_preview': widget.aiResponse.substring(0, 50),
          },
        );
      }

      // 计算综合评分
      final scores = [_helpfulnessRating, _accuracyRating, _empathyRating]
          .where((score) => score != null)
          .cast<double>();
      
      if (scores.isNotEmpty) {
        final averageScore = scores.reduce((a, b) => a + b) / scores.length;
        await LangfuseService.createScore(
          traceId: widget.traceId,
          generationId: widget.generationId,
          name: 'overall_quality',
          value: averageScore,
          comment: _feedbackComment,
          metadata: {
            'component_scores': {
              'helpfulness': _helpfulnessRating,
              'accuracy': _accuracyRating,
              'empathy': _empathyRating,
            },
          },
        );
      }

      setState(() {
        _feedbackSubmitted = true;
      });

      widget.onFeedbackSubmitted?.call();

    } catch (e) {
      print('反馈提交失败: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('反馈提交失败，请稍后重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
} 