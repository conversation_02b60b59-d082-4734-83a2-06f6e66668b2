import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_reading.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/theme/app_theme.dart';
import 'package:ai_tarot_reading/services/ai_tarot_service.dart';
import 'package:ai_tarot_reading/services/psychological_guidance_service.dart';
import 'package:ai_tarot_reading/models/psychological_tarot_reading.dart';

class AiReadingView extends StatefulWidget {
  final TarotReading reading;
  final Function({int? accuracy, int? usefulness, int? satisfaction, String? feedback}) onSave;
  final Function(String question, String response) onFollowUpQuestion;

  const AiReadingView({
    super.key,
    required this.reading,
    required this.onSave,
    required this.onFollowUpQuestion,
  });

  @override
  State<AiReadingView> createState() => _AiReadingViewState();
}

class _AiReadingViewState extends State<AiReadingView> {
  final TextEditingController _followUpController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  
  int? _accuracy;
  int? _usefulness;
  int? _satisfaction;
  String? _feedback;
  bool _isTyping = false;

  // 心理引导相关状态
  PsychologicalTarotReading? _psychologicalReading;
  bool _isInitializingGuidance = false;
  bool _showGuidanceInterface = false;

  // 日志显示相关
  bool _showLogs = false;
  List<String> _logs = [];

  void _addLog(String message) {
    setState(() {
      _logs.add('${DateTime.now().toString().substring(11, 19)} $message');
      if (_logs.length > 50) {
        _logs.removeAt(0); // 保持最多50条日志
      }
    });
    print(message); // 同时输出到控制台
  }

  @override
  void initState() {
    super.initState();
    _initializePsychologicalGuidance();

    // 自动保存解读到历史记录
    _addLog('🚀 AiReadingView initState 开始执行');
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _addLog('🔄 AiReadingView 初始化完成，自动保存解读...');
      _addLog('📋 解读内容: ${widget.reading.interpretation.substring(0, 50)}...');
      widget.onSave();
      _addLog('✅ onSave() 调用完成');
    });
  }

  @override
  void dispose() {
    _followUpController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 初始化心理引导功能
  Future<void> _initializePsychologicalGuidance() async {
    setState(() {
      _isInitializingGuidance = true;
    });

    try {
      // 生成心理引导式解读
      final psychReading = await AITarotService.generatePsychologicalReading(
        question: widget.reading.question,
        cards: widget.reading.cards,
        spreadType: widget.reading.spreadType.toString(),
      );

      setState(() {
        _psychologicalReading = psychReading;
        _showGuidanceInterface = true;
        _isInitializingGuidance = false;
      });
    } catch (e) {
      print('❌ 心理引导初始化失败: $e');
      setState(() {
        _isInitializingGuidance = false;
        _showGuidanceInterface = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 🔍 日志按钮 - 放在最顶部
          Center(
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  _showLogs = !_showLogs;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              ),
              child: Text(
                _showLogs ? '🔍 隐藏日志' : '🔍 显示日志',
                style: const TextStyle(fontSize: 16, color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
          ),
          // 日志显示区域
          if (_showLogs) ...[
            const SizedBox(height: 16),
            Container(
              height: 200,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.black87,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange, width: 2),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🔍 实时日志监控',
                    style: TextStyle(
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _logs.length,
                      itemBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 2),
                          child: Text(
                            _logs[index],
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontFamily: 'monospace',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 16),
          _buildQuestionSection(),
          const SizedBox(height: 16),
          _buildCardsSection(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildInterpretationSection(),
          ),
          const SizedBox(height: 16),
          _buildFollowUpSection(),
          const SizedBox(height: 16),
          _buildRatingSection(),
        ],
      ),
    );
  }

  Widget _buildQuestionSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Your Question:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppTheme.secondaryColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          widget.reading.question,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.1, end: 0);
  }

  Widget _buildCardsSection() {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: widget.reading.cards.length,
        itemBuilder: (context, index) {
          final card = widget.reading.cards[index];
          return Padding(
            padding: const EdgeInsets.only(right: 12.0),
            child: Column(
              children: [
                Container(
                  width: 60,
                  height: 90,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                    image: DecorationImage(
                      image: AssetImage(card.getCorrectImageUrl()),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(height: 4),
                SizedBox(
                  width: 70,
                  child: Text(
                    card.name,
                    style: const TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ).animate().fadeIn(
                delay: Duration(milliseconds: 200 * index),
                duration: 300.ms,
              ).slideX(
                begin: 0.2,
                end: 0,
                delay: Duration(milliseconds: 200 * index),
                duration: 300.ms,
              );
        },
      ),
    );
  }

  Widget _buildInterpretationSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          controller: _scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 🔍 日志按钮 - 在解读内容中
              Center(
                child: Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: ElevatedButton.icon(
                    onPressed: () {
                      setState(() {
                        _showLogs = !_showLogs;
                      });
                    },
                    icon: const Icon(Icons.bug_report, color: Colors.white),
                    label: Text(
                      _showLogs ? '隐藏调试日志' : '显示调试日志',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                  ),
                ),
              ),

              // 日志显示区域
              if (_showLogs) ...[
                Container(
                  margin: const EdgeInsets.only(bottom: 16),
                  height: 250,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.black87,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red, width: 2),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '🐛 调试日志 - 实时监控',
                        style: TextStyle(
                          color: Colors.red,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: ListView.builder(
                          itemCount: _logs.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(bottom: 2),
                              child: Text(
                                _logs[index],
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],

              const Text(
                'Tarot Interpretation',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                widget.reading.interpretation,
                style: const TextStyle(fontSize: 16),
              ),
              if (widget.reading.followUpQuestions.isNotEmpty) ...[
                const SizedBox(height: 24),
                const Text(
                  'Follow-up Questions',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
                const SizedBox(height: 12),
                ...List.generate(
                  widget.reading.followUpQuestions.length,
                  (index) => Padding(
                    padding: const EdgeInsets.only(bottom: 16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Q: ${widget.reading.followUpQuestions[index]}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'A: ${widget.reading.followUpResponses[index]}',
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 500.ms, delay: 300.ms);
  }

  Widget _buildFollowUpSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Ask a follow-up question:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _followUpController,
                decoration: InputDecoration(
                  hintText: 'Type your question...',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  filled: true,
                ),
                enabled: !_isTyping,
              ),
            ),
            const SizedBox(width: 8),
            IconButton(
              icon: _isTyping
                  ? const CircularProgressIndicator()
                  : const Icon(Icons.send),
              onPressed: _isTyping
                  ? null
                  : () {
                      if (_followUpController.text.trim().isNotEmpty) {
                        _sendFollowUpQuestion();
                      }
                    },
              color: AppTheme.primaryColor,
            ),
          ],
        ),
      ],
    );
  }

  void _sendFollowUpQuestion() {
    final question = _followUpController.text.trim();
    
    setState(() {
      _isTyping = true;
    });
    
    // Simulate AI response delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // Generate mock response
        final response = _generateMockResponse(question);
        
        // Add to the reading
        widget.onFollowUpQuestion(question, response);
        
        // Clear input and scroll to bottom
        _followUpController.clear();
        
        setState(() {
          _isTyping = false;
        });
        
        // Scroll to bottom after a short delay to ensure the UI has updated
        Future.delayed(const Duration(milliseconds: 300), () {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        });
      }
    });
  }

  String _generateMockResponse(String question) {
    // Simple mock response generator
    if (question.toLowerCase().contains('future')) {
      return "The cards suggest that the future is not fixed, but rather shaped by your choices and actions. Focus on what you can control in the present, and the future will unfold accordingly.";
    } else if (question.toLowerCase().contains('love') || question.toLowerCase().contains('relationship')) {
      return "In matters of the heart, the cards indicate that open communication and authenticity are key. Trust your intuition about what feels right for you.";
    } else if (question.toLowerCase().contains('career') || question.toLowerCase().contains('job') || question.toLowerCase().contains('work')) {
      return "Regarding your career path, the cards suggest that you may find more fulfillment by aligning your work with your personal values and natural talents.";
    } else if (question.toLowerCase().contains('meaning') || question.toLowerCase().contains('symbolism')) {
      return "The symbolism in your cards represents a journey of personal transformation. Each card contains layers of meaning that can be applied to different aspects of your situation.";
    } else {
      return "The cards suggest that you should trust your inner wisdom on this matter. Look for patterns in your life and consider how they might relate to your current situation.";
    }
  }

  Widget _buildRatingSection() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Rate this reading:',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildRatingRow(
              label: 'Accuracy',
              value: _accuracy,
              onRatingUpdate: (rating) {
                setState(() {
                  _accuracy = rating.toInt();
                });
              },
            ),
            const SizedBox(height: 12),
            _buildRatingRow(
              label: 'Usefulness',
              value: _usefulness,
              onRatingUpdate: (rating) {
                setState(() {
                  _usefulness = rating.toInt();
                });
              },
            ),
            const SizedBox(height: 12),
            _buildRatingRow(
              label: 'Satisfaction',
              value: _satisfaction,
              onRatingUpdate: (rating) {
                setState(() {
                  _satisfaction = rating.toInt();
                });
              },
            ),
            const SizedBox(height: 16),
            TextField(
              decoration: InputDecoration(
                hintText: 'Additional feedback (optional)',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
              ),
              maxLines: 3,
              onChanged: (value) {
                setState(() {
                  _feedback = value;
                });
              },
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  widget.onSave(
                    accuracy: _accuracy,
                    usefulness: _usefulness,
                    satisfaction: _satisfaction,
                    feedback: _feedback,
                  );
                },
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12.0),
                  child: Text(
                    'Save Reading',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingRow({
    required String label,
    required int? value,
    required Function(double) onRatingUpdate,
  }) {
    return Row(
      children: [
        SizedBox(
          width: 100,
          child: Text(
            label,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        RatingBar.builder(
          initialRating: value?.toDouble() ?? 0,
          minRating: 0,
          direction: Axis.horizontal,
          allowHalfRating: false,
          itemCount: 5,
          itemSize: 30,
          unratedColor: Colors.grey.withOpacity(0.3),
          itemBuilder: (context, _) => const Icon(
            Icons.star,
            color: AppTheme.accentColor,
          ),
          onRatingUpdate: onRatingUpdate,
        ),
      ],
    );
  }
}
