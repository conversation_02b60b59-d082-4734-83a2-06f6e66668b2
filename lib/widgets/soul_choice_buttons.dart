import 'package:flutter/material.dart';

class SoulChoiceButtons extends StatelessWidget {
  final Function(String) onChoice;
  final String language;

  const SoulChoiceButtons({
    Key? key,
    required this.onChoice,
    required this.language,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      child: Column(
        children: [
          // 主要选择
          Row(
            children: [
              Expanded(
                child: _buildChoiceButton(
                  icon: '💭',
                  title: _getTitle('explore', language),
                  subtitle: _getSubtitle('explore', language),
                  onTap: () => onChoice('explore'),
                  gradient: const LinearGradient(
                    colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildChoiceButton(
                  icon: '✨',
                  title: _getTitle('share', language),
                  subtitle: _getSubtitle('share', language),
                  onTap: () => onChoice('share'),
                  gradient: const LinearGradient(
                    colors: [Color(0xFFEC4899), Color(0xFFF59E0B)],
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // 详细选择（分享支线）
          _buildDetailedChoices(),
        ],
      ),
    );
  }

  Widget _buildChoiceButton({
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required Gradient gradient,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Text(
              icon,
              style: const TextStyle(fontSize: 24),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedChoices() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.white.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _getDetailTitle(language),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              _buildSmallButton('🎉', _getDetailOption('happy', language), () => onChoice('happy')),
              _buildSmallButton('🌱', _getDetailOption('growth', language), () => onChoice('growth')),
              _buildSmallButton('💪', _getDetailOption('effort', language), () => onChoice('effort')),
              _buildSmallButton('🙏', _getDetailOption('grateful', language), () => onChoice('grateful')),
              _buildSmallButton('📝', _getDetailOption('diary', language), () => onChoice('diary')),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSmallButton(String icon, String text, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withOpacity(0.2),
              Colors.white.withOpacity(0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.white.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(icon, style: const TextStyle(fontSize: 14)),
            const SizedBox(width: 6),
            Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getTitle(String type, String language) {
    switch (type) {
      case 'explore':
        switch (language) {
          case 'zh': return '探索内心';
          case 'en': return 'Explore Within';
          case 'ja': return '内面探索';
          default: return '探索内心';
        }
      case 'share':
        switch (language) {
          case 'zh': return '分享美好';
          case 'en': return 'Share Joy';
          case 'ja': return '美しさを共有';
          default: return '分享美好';
        }
      default: return '';
    }
  }

  String _getSubtitle(String type, String language) {
    switch (type) {
      case 'explore':
        switch (language) {
          case 'zh': return '困惑与指引';
          case 'en': return 'Confusion & Guidance';
          case 'ja': return '困惑と導き';
          default: return '困惑与指引';
        }
      case 'share':
        switch (language) {
          case 'zh': return '获得夸夸';
          case 'en': return 'Get Praised';
          case 'ja': return '褒められる';
          default: return '获得夸夸';
        }
      default: return '';
    }
  }

  String _getDetailTitle(String language) {
    switch (language) {
      case 'zh': return '✨ 今天想要分享什么呢？';
      case 'en': return '✨ What would you like to share today?';
      case 'ja': return '✨ 今日は何をシェアしたいですか？';
      default: return '✨ 今天想要分享什么呢？';
    }
  }

  String _getDetailOption(String type, String language) {
    switch (type) {
      case 'happy':
        switch (language) {
          case 'zh': return '开心的事';
          case 'en': return 'Happy moments';
          case 'ja': return '嬉しいこと';
          default: return '开心的事';
        }
      case 'growth':
        switch (language) {
          case 'zh': return '小小成长';
          case 'en': return 'Small growth';
          case 'ja': return '小さな成長';
          default: return '小小成长';
        }
      case 'effort':
        switch (language) {
          case 'zh': return '努力瞬间';
          case 'en': return 'Effort moments';
          case 'ja': return '努力の瞬間';
          default: return '努力瞬间';
        }
      case 'grateful':
        switch (language) {
          case 'zh': return '感恩时刻';
          case 'en': return 'Grateful moments';
          case 'ja': return '感謝の瞬間';
          default: return '感恩时刻';
        }
      case 'diary':
        switch (language) {
          case 'zh': return '查看日记';
          case 'en': return 'View diary';
          case 'ja': return '日記を見る';
          default: return '查看日记';
        }
      default: return '';
    }
  }
}
