import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/daily_tarot.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/services/tarot_data_service.dart';
import 'package:ai_tarot_reading/screens/manifestation_animation_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:intl/intl.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:ai_tarot_reading/utils/tarot_image_manager.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/providers/app_state_provider.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'dart:ui';

class DailyTarotCard extends StatefulWidget {
  final DailyTarot? dailyTarot;
  final DateTime selectedDate;
  final VoidCallback? onDrawCard;
  final VoidCallback? onRedraw;
  final bool isToday;

  const DailyTarotCard({
    super.key,
    required this.dailyTarot,
    required this.selectedDate,
    this.onDrawCard,
    this.onRedraw,
    this.isToday = false,
  });

  @override
  State<DailyTarotCard> createState() => _DailyTarotCardState();
}

class _DailyTarotCardState extends State<DailyTarotCard> {
  late TextEditingController _journalController;
  bool _isJournalEdited = false;

  @override
  void initState() {
    super.initState();
    _journalController = TextEditingController(
      text: widget.dailyTarot?.manifestationJournal ?? '',
    );
    _journalController.addListener(() {
      if (!_isJournalEdited) {
        setState(() {
          _isJournalEdited = true;
        });
      }
    });
  }

  @override
  void dispose() {
    _journalController.dispose();
    super.dispose();
  }

  DailyTarot? get dailyTarot => widget.dailyTarot;

  @override
  Widget build(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    if (dailyTarot == null || !dailyTarot!.isDrawn) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 15, sigmaY: 15),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.3), // 更白的毛玻璃效果
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Colors.white.withOpacity(0.4),
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.4),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.auto_awesome,
                      size: 48,
                      color: Colors.black.withOpacity(0.7),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text(
                    widget.isToday
                        ? languageManager.translate('no_card_drawn_today')
                        : languageManager.translate('no_card_record_today'),
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.black.withOpacity(0.8),
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  if (widget.isToday && widget.onDrawCard != null) ...[
                    const SizedBox(height: 24),
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(24),
                        border: Border.all(
                          color: Colors.white.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(24),
                          onTap: widget.onDrawCard,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                            child: Text(
                              languageManager.translate('draw_today_tarot'),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Colors.black87,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          if (dailyTarot!.card != null) _buildCardSection(context),
          const SizedBox(height: 16),
          if (dailyTarot!.card != null) _buildFortuneSection(context),
          const SizedBox(height: 16),
          if (dailyTarot!.card != null) _buildAdviceSection(context),
        ],
      ),
    );
  }

  Widget _buildCardSection(BuildContext context) {
    print('🎴 DailyTarotCard build - isDrawn: ${dailyTarot!.isDrawn}');
    print('🎴 DailyTarotCard build - card: ${dailyTarot!.card?.name}');
    print('🎴 DailyTarotCard build - dailyTarot?.card: ${dailyTarot!.card?.name}');

    if (!dailyTarot!.isDrawn) {
      print('🎴 显示卡背');
      return GestureDetector(
        onTap: () {
          // Implement the tap logic here
        },
        child: Container(
          width: 200,
          height: 300,
          decoration: BoxDecoration(
            color: Colors.indigo.shade900,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.3),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
            image: const DecorationImage(
              image: AssetImage('images/card_back.png'),
              fit: BoxFit.cover,
            ),
          ),
        ).animate()
            .shimmer(duration: 2.seconds, curve: Curves.easeInOut)
            .then()
            .shake(hz: 2, curve: Curves.easeInOut),
      );
    }

    final displayCard = dailyTarot!.card;
    print('🎴 最终显示的卡牌: ${displayCard?.name}');
    print('🎴 卡牌图片路径: ${displayCard?.imageUrl}');

    if (displayCard == null) {
      print('❌ 没有可显示的卡牌');
      return const Center(
        child: Text(
          'No card available',
          style: TextStyle(fontSize: 18),
        ),
      );
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildCardDisplay(context, displayCard),
          const SizedBox(height: 24),
          if (dailyTarot != null) ...[
            const SizedBox(height: 16),
            // 显化日记部分 - 即使没有显化目标也显示
            _buildManifestationJournal(context, dailyTarot!.manifestationGoal),
          ],
        ],
      ),
    );
  }

  Widget _buildCardDisplay(BuildContext context, TarotCard displayCard) {
    print('🎴 构建卡牌显示: ${displayCard.name}');
    print('🎴 卡牌图片路径: ${displayCard.imageUrl}');

    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            width: 160, // 🎯 减小卡牌宽度，从200改为160
            height: 240, // 🎯 减小卡牌高度，从300改为240
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Stack(
              children: [
                // 卡牌图片（支持正逆位）
                Transform(
                  alignment: Alignment.center,
                  transform: Matrix4.identity()
                    ..rotateZ(displayCard.isReversed ? 3.14159 : 0), // 逆位时旋转180度
                  child: TarotImageManager.buildCardImage(
                    cardName: displayCard.name,
                    width: 160,
                    height: 240,
                    fit: BoxFit.cover,
                  ),
                ),
                
                // 逆位指示器
                if (displayCard.isReversed)
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(8),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Text(
                        Provider.of<LanguageManager>(context).translate('reversed'),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ).animate().fadeIn(duration: 500.ms).scale(
              begin: const Offset(0.9, 0.9),
              end: const Offset(1.0, 1.0),
              duration: 500.ms,
            ),
        const SizedBox(height: 16),
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            final translatedName = displayCard.getTranslatedName(languageManager.translate);
            return Text(
              displayCard.isReversed
                  ? '$translatedName (${languageManager.translate('reversed')})'
                  : '$translatedName (${languageManager.translate('upright_position')})',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: displayCard.isReversed ? Colors.red.shade600 : FigmaTheme.primaryPink,
              ),
              textAlign: TextAlign.center,
            );
          },
        ),
        const SizedBox(height: 8),
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            // 使用语言管理器翻译关键词
            final keywords = TarotDataService.instance.getTranslatedKeywords(displayCard, languageManager);

            return Wrap(
              alignment: WrapAlignment.center,
              spacing: 8,
              children: keywords.map((keyword) {
                return Chip(
                  label: Text(keyword),
                  backgroundColor: displayCard.isReversed
                      ? Colors.red.shade50
                      : FigmaTheme.primaryPink.withValues(alpha: 0.1),
                  side: displayCard.isReversed
                      ? BorderSide(color: Colors.red.shade200, width: 1)
                      : BorderSide.none,
                  labelStyle: TextStyle(
                    color: displayCard.isReversed ? Colors.red.shade700 : null,
                  ),
                );
              }).toList(),
            );
          },
        ),
        if (widget.isToday && widget.onRedraw != null) ...[
          const SizedBox(height: 16),
          Center(
            child: OutlinedButton(
              onPressed: widget.onRedraw,
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Theme.of(context).primaryColor),
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                Provider.of<LanguageManager>(context).translate('redraw'),
                style: TextStyle(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFortuneSection(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.25), // 更白的毛玻璃效果
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.15),
                blurRadius: 15,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.auto_awesome,
                      color: FigmaTheme.accentPurple,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    languageManager.translate('daily_fortune'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                _generateLocalizedFortune(context),
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black.withOpacity(0.8),
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(delay: 300.ms, duration: 500.ms);
  }

  Widget _buildAdviceSection(BuildContext context) {
    final languageManager = Provider.of<LanguageManager>(context);

    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
        child: Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.25), // 更白的毛玻璃效果
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: Colors.white.withOpacity(0.4),
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.white.withOpacity(0.15),
                blurRadius: 15,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.lightbulb,
                      color: FigmaTheme.accentPurple,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    languageManager.translate('daily_advice'),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                _generateLocalizedAdvice(context),
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.black.withOpacity(0.8),
                  height: 1.5,
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(delay: 600.ms, duration: 500.ms);
  }

  Widget _buildManifestationJournal(BuildContext context, ManifestationGoal? goal) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.white.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_stories,
                color: _getPrimaryColor(goal),
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                Provider.of<LanguageManager>(context).translate('manifestation_diary'),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: _getPrimaryColor(goal),
                ),
              ),
              const Spacer(),
              if (_isJournalEdited)
                TextButton.icon(
                  onPressed: _saveJournal,
                  icon: const Icon(Icons.save, size: 16),
                  label: Text(Provider.of<LanguageManager>(context).translate('save')),
                  style: TextButton.styleFrom(
                    foregroundColor: _getPrimaryColor(goal),
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _journalController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: '${Provider.of<LanguageManager>(context).translate('record_manifestation_experience')}\n${Provider.of<LanguageManager>(context).translate('diary_example')}',
              hintStyle: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: Colors.grey[300]!,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: _getPrimaryColor(goal),
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.all(12),
            ),
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF2D3748),
            ),
          ),
          const SizedBox(height: 8),
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                languageManager.translate('track_manifestation_progress'),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  void _saveJournal() async {
    final appState = Provider.of<AppStateProvider>(context, listen: false);
    
    try {
      final journalText = _journalController.text.trim();
      final updatedDailyTarot = dailyTarot!.copyWith(
        manifestationJournal: journalText,
      );
      
      // 更新本地状态
      appState.updateDailyTarot(widget.selectedDate, updatedDailyTarot);
      
      // 保存到Supabase后端
      try {
        final supabaseService = Provider.of<SupabaseDataService>(context, listen: false);
        final dateString = DateFormat('yyyy-MM-dd').format(widget.selectedDate);
        await supabaseService.updateDailyTarotJournal(dateString, journalText);
        print('✅ 显化日记已同步到后端');
      } catch (e) {
        print('⚠️ 后端同步失败，仅保存到本地: $e');
        // 即使后端失败，本地保存仍然成功
      }
      
      setState(() {
        _isJournalEdited = false;
      });
      
      // 显示保存成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              Text(Provider.of<LanguageManager>(context, listen: false).translate('diary_saved_successfully')),
            ],
          ),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } catch (e) {
      // 显示错误提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error, color: Colors.white),
              const SizedBox(width: 8),
              Text('保存失败：$e'),
            ],
          ),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  Color _getPrimaryColor(ManifestationGoal? goal) {
    if (goal == null) {
      return const Color(0xFF8B5CF6); // 默认紫色，当没有显化目标时
    }

    switch (goal) {
      case ManifestationGoal.wealth:
        return const Color(0xFFFF8C00);
      case ManifestationGoal.career:
        return const Color(0xFF1E90FF);
      case ManifestationGoal.beauty:
        return const Color(0xFFFF1493);
      case ManifestationGoal.fame:
        return const Color(0xFF9370DB);
      case ManifestationGoal.love:
        return const Color(0xFFDC143C);
    }
  }

  // 生成本地化的运势文本
  String _generateLocalizedFortune(BuildContext context) {
    if (dailyTarot?.card == null) return '';

    final languageManager = Provider.of<LanguageManager>(context);
    final card = dailyTarot!.card!;

    // 使用翻译后的卡牌名称、含义和关键词
    final translatedName = card.getTranslatedName(languageManager.translate);
    final translatedMeaning = card.getTranslatedMeaning(languageManager.translate);
    final translatedKeywords = card.getTranslatedKeywords(languageManager.translate);

    // 使用语言管理器翻译衔接描述词
    final energySurrounds = languageManager.translate('today_energy_surrounds')
        .replaceAll('{cardName}', translatedName);

    final keywordJoiner = languageManager.currentLanguage.startsWith('en') ? ' and ' : '、';
    final experienceMoments = languageManager.translate('may_experience_moments')
        .replaceAll('{keywords}', translatedKeywords.join(keywordJoiner));

    // 清理文本，确保句号正确
    String cleanedMeaning = translatedMeaning.trim();
    if (!cleanedMeaning.endsWith('.') && !cleanedMeaning.endsWith('。')) {
      cleanedMeaning += languageManager.currentLanguage.startsWith('en') ? '.' : '。';
    }

    String cleanedExperience = experienceMoments.trim();
    if (!cleanedExperience.endsWith('.') && !cleanedExperience.endsWith('。')) {
      cleanedExperience += languageManager.currentLanguage.startsWith('en') ? '.' : '。';
    }

    return "$energySurrounds。 $cleanedMeaning $cleanedExperience";
  }

  // 生成本地化的建议文本
  String _generateLocalizedAdvice(BuildContext context) {
    if (dailyTarot?.card == null) return '';

    final languageManager = Provider.of<LanguageManager>(context);
    final card = dailyTarot!.card!;

    // 使用翻译后的卡牌名称和关键词
    final translatedName = card.getTranslatedName(languageManager.translate);
    final translatedKeywords = card.getTranslatedKeywords(languageManager.translate);

    if (translatedKeywords.isEmpty) return '';

    // 使用语言管理器翻译衔接描述词
    final embraceQualities = languageManager.translate('embrace_qualities_today')
        .replaceAll('{cardName}', translatedName);

    final focusOn = languageManager.translate('focus_on_keyword')
        .replaceAll('{keyword}', translatedKeywords.first);

    final mindfulOpportunities = languageManager.translate('mindful_opportunities_related')
        .replaceAll('{keyword}', translatedKeywords.last);

    // 清理文本，确保标点符号正确
    String cleanedEmbrace = embraceQualities.trim();
    if (!cleanedEmbrace.endsWith('.') && !cleanedEmbrace.endsWith('。')) {
      cleanedEmbrace += languageManager.currentLanguage.startsWith('en') ? '.' : '。';
    }

    String cleanedOpportunities = mindfulOpportunities.trim();
    if (!cleanedOpportunities.endsWith('.') && !cleanedOpportunities.endsWith('。')) {
      cleanedOpportunities += languageManager.currentLanguage.startsWith('en') ? '.' : '。';
    }

    final separator = languageManager.currentLanguage.startsWith('en') ? ' ' : '，';
    return "$cleanedEmbrace $focusOn$separator$cleanedOpportunities";
  }
}
