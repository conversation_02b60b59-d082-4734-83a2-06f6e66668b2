import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

class LiquidChromeBackground extends StatefulWidget {
  final Color baseColor;
  final double speed;
  final double amplitude;
  final double frequencyX;
  final double frequencyY;
  final bool interactive;
  final Widget? child;

  const LiquidChromeBackground({
    super.key,
    this.baseColor = const Color(0xFF1A1A1A),
    this.speed = 0.2,
    this.amplitude = 0.5,
    this.frequencyX = 3.0,
    this.frequencyY = 2.0,
    this.interactive = true,
    this.child,
  });

  @override
  State<LiquidChromeBackground> createState() => _LiquidChromeBackgroundState();
}

class _LiquidChromeBackgroundState extends State<LiquidChromeBackground>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  Offset _mousePosition = const Offset(0.5, 0.5);

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateMousePosition(Offset globalPosition, Size size) {
    if (widget.interactive) {
      setState(() {
        _mousePosition = Offset(
          globalPosition.dx / size.width,
          globalPosition.dy / size.height,
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return GestureDetector(
          onPanUpdate: (details) {
            _updateMousePosition(
              details.globalPosition,
              Size(constraints.maxWidth, constraints.maxHeight),
            );
          },
          onTapDown: (details) {
            _updateMousePosition(
              details.globalPosition,
              Size(constraints.maxWidth, constraints.maxHeight),
            );
          },
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(constraints.maxWidth, constraints.maxHeight),
                painter: LiquidChromePainter(
                  time: _animationController.value * widget.speed,
                  baseColor: widget.baseColor,
                  amplitude: widget.amplitude,
                  frequencyX: widget.frequencyX,
                  frequencyY: widget.frequencyY,
                  mousePosition: _mousePosition,
                ),
                child: widget.child,
              );
            },
          ),
        );
      },
    );
  }
}

class LiquidChromePainter extends CustomPainter {
  final double time;
  final Color baseColor;
  final double amplitude;
  final double frequencyX;
  final double frequencyY;
  final Offset mousePosition;

  LiquidChromePainter({
    required this.time,
    required this.baseColor,
    required this.amplitude,
    required this.frequencyX,
    required this.frequencyY,
    required this.mousePosition,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // 创建液体Chrome效果的渐变
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    
    // 基础渐变
    final gradient = ui.Gradient.radial(
      Offset(size.width * 0.5, size.height * 0.5),
      size.width * 0.8,
      [
        baseColor.withOpacity(0.8),
        baseColor.withOpacity(0.4),
        baseColor.withOpacity(0.1),
        Colors.transparent,
      ],
      [0.0, 0.3, 0.7, 1.0],
    );

    paint.shader = gradient;
    canvas.drawRect(rect, paint);

    // 添加动态波纹效果
    _drawLiquidWaves(canvas, size);
    
    // 添加鼠标交互效果
    if (mousePosition != const Offset(0.5, 0.5)) {
      _drawInteractiveRipples(canvas, size);
    }
  }

  void _drawLiquidWaves(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.overlay;

    // 创建多层波浪效果
    for (int layer = 0; layer < 3; layer++) {
      final path = Path();
      final waveHeight = amplitude * 50 * (1 - layer * 0.3);
      final frequency = frequencyX * (1 + layer * 0.5);
      
      path.moveTo(0, size.height * 0.5);
      
      for (double x = 0; x <= size.width; x += 2) {
        final normalizedX = x / size.width;
        final y = size.height * 0.5 + 
                  math.sin(normalizedX * frequency * 2 * math.pi + time * 2 * math.pi) * waveHeight +
                  math.sin(normalizedX * frequencyY * 2 * math.pi + time * 1.5 * math.pi) * waveHeight * 0.5;
        
        path.lineTo(x, y);
      }
      
      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      // 为每层设置不同的颜色和透明度
      final layerColor = HSLColor.fromColor(baseColor)
          .withLightness((HSLColor.fromColor(baseColor).lightness + layer * 0.1).clamp(0.0, 1.0))
          .toColor()
          .withOpacity(0.3 - layer * 0.1);
      
      paint.color = layerColor;
      canvas.drawPath(path, paint);
    }
  }

  void _drawInteractiveRipples(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..color = baseColor.withOpacity(0.3);

    final center = Offset(
      mousePosition.dx * size.width,
      mousePosition.dy * size.height,
    );

    // 绘制多个同心圆涟漪
    for (int i = 0; i < 3; i++) {
      final radius = (time * 100 + i * 30) % 150;
      final opacity = (1.0 - radius / 150) * 0.5;
      
      paint.color = baseColor.withOpacity(opacity);
      canvas.drawCircle(center, radius, paint);
    }
  }

  @override
  bool shouldRepaint(LiquidChromePainter oldDelegate) {
    return oldDelegate.time != time ||
           oldDelegate.mousePosition != mousePosition ||
           oldDelegate.baseColor != baseColor;
  }
}
