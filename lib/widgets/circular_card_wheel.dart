import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/theme/figma_theme.dart';
import 'package:ai_tarot_reading/screens/manual_card_selection_screen.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';

/// 78张牌圆轮选择组件，1:1模仿参考设计
class CircularCardWheel extends StatefulWidget {
  final Function(TarotCard) onCardSelected;
  final String title;
  final String subtitle;

  const CircularCardWheel({
    super.key,
    required this.onCardSelected,
    this.title = '滑动\n浏览全部卡片',
    this.subtitle = '在心中默念牌意\n双指放大牌轮\n点击选牌',
  });

  @override
  State<CircularCardWheel> createState() => _CircularCardWheelState();
}

class _CircularCardWheelState extends State<CircularCardWheel>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;

  double _currentRotation = 0.0; // 当前旋转角度
  double _currentScale = 1.0; // 当前缩放比例
  final double _lastPanDelta = 0.0;

  // 78张牌的数据 - 随机分布
  List<TarotCard> shuffledCards = [];

  // 卡牌位置映射 - 编号到卡牌的映射
  Map<int, TarotCard> cardPositionMap = {};
  
  // 圆轮参数
  static const double _baseRadius = 180.0; // 基础半径
  static const double _cardWidth = 45.0; // 卡牌宽度
  static const double _cardHeight = 67.5; // 卡牌高度
  static const double _minScale = 0.8; // 最小缩放
  static const double _maxScale = 2.5; // 最大缩放

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    // 初始化随机分布的卡牌
    _initializeShuffledCards();
  }

  void _initializeShuffledCards() {
    // 获取所有78张牌
    final allCards = TarotCardsData.allCards;

    // 创建随机分布
    shuffledCards = List<TarotCard>.from(allCards);
    shuffledCards.shuffle();

    // 创建位置映射 - 编号1-78对应随机分布的卡牌
    cardPositionMap.clear();
    for (int i = 0; i < shuffledCards.length; i++) {
      cardPositionMap[i + 1] = shuffledCards[i]; // 编号从1开始
    }
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment(-1.0, -1.0),
            end: Alignment(1.0, 1.0),
            colors: [
              Color(0xFF87CEEB),
              Color(0xFFE6E6FA),
              Color(0xFFF8BBD9),
              Color(0xFFE6E6FA),
              Color(0xFF87CEEB),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // 顶部导航栏
              _buildTopBar(),
              
              // 主要内容区域
              Expanded(
                child: Stack(
                  children: [
                    // 左侧文字区域
                    _buildLeftTextArea(),
                    
                    // 圆轮卡牌区域
                    _buildCardWheelArea(),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(
              Icons.arrow_back_ios,
              color: FigmaTheme.textPrimary,
              size: 24,
            ),
          ),
          const Spacer(),
          // 洗牌按钮
          Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: IconButton(
              onPressed: () {
                setState(() {
                  _initializeShuffledCards();
                });
              },
              icon: const Icon(
                Icons.shuffle,
                color: FigmaTheme.primaryPink,
                size: 24,
              ),
              tooltip: '重新洗牌',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLeftTextArea() {
    return Positioned(
      left: 20,
      top: 60,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w700,
              color: FigmaTheme.textPrimary,
              height: 1.2,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            widget.subtitle,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: FigmaTheme.textSecondary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardWheelArea() {
    return Positioned.fill(
      child: GestureDetector(
        onPanUpdate: _handlePanUpdate,
        onScaleUpdate: _handleScaleUpdate,
        onTapDown: _handleTapDown,
        child: Stack(
          children: [
            // 使用 CustomPaint 绘制卡牌轮
            CustomPaint(
              painter: CardWheelPainter(
                cards: shuffledCards,
                cardPositionMap: cardPositionMap,
                rotation: _currentRotation,
                scale: _currentScale,
                onCardTap: _handleCardTap,
              ),
              child: Container(),
            ),
            // 叠加真实的卡背图片 Widget
            ..._buildCardBackWidgets(),
          ],
        ),
      ),
    );
  }

  // 构建卡背图片 Widget 列表
  List<Widget> _buildCardBackWidgets() {
    final List<Widget> cardWidgets = [];
    final screenSize = MediaQuery.of(context).size;
    final center = Offset(screenSize.width * 0.65, screenSize.height * 0.5);
    final radius = _baseRadius * _currentScale;

    for (int i = 0; i < shuffledCards.length; i++) {
      final angle = (i * 2 * math.pi / shuffledCards.length) + _currentRotation;

      // 只显示前半圆的卡牌（180度范围）
      if (angle >= -math.pi / 2 && angle <= math.pi / 2) {
        final x = center.dx + radius * math.cos(angle);
        final y = center.dy + radius * math.sin(angle);

        cardWidgets.add(
          Positioned(
            left: x - (_cardWidth * _currentScale / 2),
            top: y - (_cardHeight * _currentScale / 2),
            child: Transform.rotate(
              angle: angle + math.pi / 2,
              child: _buildCardBackWidget(shuffledCards[i], i),
            ),
          ),
        );
      }
    }

    return cardWidgets;
  }

  // 构建单个卡背 Widget，支持自适应显示
  Widget _buildCardBackWidget(TarotCard card, int index) {
    final cardWidth = _cardWidth * _currentScale;
    final cardHeight = _cardHeight * _currentScale;

    return GestureDetector(
      onTap: () => _handleCardTap(card),
      child: Container(
        width: cardWidth,
        height: cardHeight,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(2, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // 卡背图片 - 自适应显示，允许变形或截断
              Positioned.fill(
                child: Image.asset(
                  'assets/images/card_back.png',
                  fit: BoxFit.cover, // 覆盖整个容器，允许截断
                  errorBuilder: (context, error, stackTrace) {
                    // 如果图片加载失败，显示渐变背景
                    return Container(
                      decoration: const BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Color(0xFF8B5CF6),
                            Color(0xFF7C3AED),
                            Color(0xFF6D28D9),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              // 半透明遮罩
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),

              // 位置编号
              Center(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${index + 1}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: math.max(10.0, 14.0 * _currentScale),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              // 缩放时的提示文字
              if (_currentScale > 1.5)
                Positioned(
                  bottom: 8,
                  left: 0,
                  right: 0,
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.7),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '点击选择',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: math.max(6.0, 8.0 * _currentScale),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _handlePanUpdate(DragUpdateDetails details) {
    setState(() {
      // 垂直滑动控制旋转
      final delta = details.delta.dy;
      _currentRotation += delta * 0.01; // 调整灵敏度
      
      // 限制旋转范围，确保只显示180度
      _currentRotation = _currentRotation.clamp(-math.pi / 2, math.pi / 2);
    });
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      _currentScale = (details.scale * _currentScale).clamp(_minScale, _maxScale);
    });
  }

  void _handleTapDown(TapDownDetails details) {
    // 检测点击的卡牌
    final RenderBox renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);

    // 这里需要与CardWheelPainter配合检测点击的卡牌
    // 暂时简化处理，后续可以优化
    _detectCardTap(localPosition);
  }

  void _detectCardTap(Offset position) {
    final center = Offset(
      MediaQuery.of(context).size.width * 0.65,
      MediaQuery.of(context).size.height * 0.5,
    );
    final radius = _baseRadius * _currentScale;

    // 计算点击位置相对于圆心的角度
    final dx = position.dx - center.dx;
    final dy = position.dy - center.dy;
    final distance = math.sqrt(dx * dx + dy * dy);

    // 检查是否在卡牌区域内
    if (distance >= radius - 50 && distance <= radius + 50) {
      final angle = math.atan2(dy, dx);
      final normalizedAngle = angle + math.pi / 2; // 调整角度基准

      // 计算对应的卡牌索引
      final totalCards = shuffledCards.length;
      final angleStep = (2 * math.pi) / totalCards;
      final cardIndex = ((normalizedAngle + _currentRotation) / angleStep).round() % totalCards;

      if (cardIndex >= 0 && cardIndex < shuffledCards.length) {
        // 根据位置获取对应的卡牌
        final positionNumber = cardIndex + 1; // 编号从1开始
        final selectedCard = cardPositionMap[positionNumber];
        if (selectedCard != null) {
          _handleCardTap(selectedCard);
        }
      }
    }
  }

  void _handleCardTap(TarotCard card) {
    // 添加点击反馈
    _scaleController.forward().then((_) {
      _scaleController.reverse();
    });

    // 随机分配正逆位（50%概率）
    final random = math.Random();
    final isReversed = random.nextBool();
    final cardWithOrientation = card.copyWith(isReversed: isReversed);

    widget.onCardSelected(cardWithOrientation);
  }
}

/// 自定义绘制器，绘制圆轮卡牌
class CardWheelPainter extends CustomPainter {
  final List<TarotCard> cards;
  final Map<int, TarotCard> cardPositionMap;
  final double rotation;
  final double scale;
  final Function(TarotCard) onCardTap;
  final List<Offset> cardPositions = []; // 存储卡牌位置用于点击检测

  CardWheelPainter({
    required this.cards,
    required this.cardPositionMap,
    required this.rotation,
    required this.scale,
    required this.onCardTap,
  });

  @override
  void paint(Canvas canvas, Size size) {
    cardPositions.clear();

    final center = Offset(size.width * 0.65, size.height * 0.5); // 稍微偏右放置
    final radius = _CircularCardWheelState._baseRadius * scale;

    // 78张牌分布在完整的圆周上，但只显示180度
    final totalCards = cards.length;
    final angleStep = (2 * math.pi) / totalCards; // 完整圆周分布

    for (int i = 0; i < totalCards; i++) {
      // 计算每张牌在完整圆周上的角度
      final cardAngle = (angleStep * i) - math.pi / 2; // 从顶部开始

      // 检查是否在可见范围内（考虑旋转偏移）
      final adjustedAngle = cardAngle - rotation;
      if (adjustedAngle >= -math.pi / 2 - 0.3 && adjustedAngle <= math.pi / 2 + 0.3) {
        final x = center.dx + radius * math.cos(cardAngle);
        final y = center.dy + radius * math.sin(cardAngle);

        final cardPosition = Offset(x, y);
        cardPositions.add(cardPosition);

        _drawCard(canvas, cards[i], cardPosition, cardAngle, i);
      } else {
        cardPositions.add(Offset.zero); // 不可见的卡牌位置标记为零
      }
    }
  }

  void _drawCard(Canvas canvas, TarotCard card, Offset position, double angle, int index) {
    // 卡牌尺寸
    final cardWidth = _CircularCardWheelState._cardWidth * scale;
    final cardHeight = _CircularCardWheelState._cardHeight * scale;

    // 保存画布状态
    canvas.save();

    // 移动到卡牌位置并旋转
    canvas.translate(position.dx, position.dy);
    canvas.rotate(angle + math.pi / 2); // 让卡牌朝向圆心

    // 绘制卡牌阴影
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 4);

    final shadowRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: const Offset(2, 2),
        width: cardWidth,
        height: cardHeight,
      ),
      const Radius.circular(8),
    );
    canvas.drawRRect(shadowRect, shadowPaint);

    // 绘制卡牌背景（统一显示卡背，不显示真实牌面）
    final cardRect = RRect.fromRectAndRadius(
      Rect.fromCenter(
        center: Offset.zero,
        width: cardWidth,
        height: cardHeight,
      ),
      const Radius.circular(8),
    );

    // 绘制卡背图片（自适应圆形容器）
    _drawCardBackImage(canvas, cardRect, cardWidth, cardHeight);

    // 绘制卡牌边框
    final borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;

    canvas.drawRRect(cardRect, borderPaint);

    // 绘制位置编号（在卡牌中心）
    final positionNumber = index + 1;
    final fontSize = math.max(10.0, 14.0 * scale);
    final textPainter = TextPainter(
      text: TextSpan(
        text: '$positionNumber',
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          shadows: [
            Shadow(
              color: Colors.black.withValues(alpha: 0.8),
              offset: const Offset(1, 1),
              blurRadius: 3,
            ),
          ],
        ),
      ),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(-textPainter.width / 2, -textPainter.height / 2),
    );

    // 在缩放时显示提示文字（而不是真实牌名）
    if (scale > 1.5) {
      final hintFontSize = math.max(6.0, 8.0 * scale);
      final hintPainter = TextPainter(
        text: TextSpan(
          text: 'Tap to Select', // TODO: 需要接入语言管理器
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: hintFontSize,
            fontWeight: FontWeight.w500,
            shadows: [
              Shadow(
                color: Colors.black.withValues(alpha: 0.8),
                offset: const Offset(1, 1),
                blurRadius: 2,
              ),
            ],
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: TextAlign.center,
      );

      hintPainter.layout(maxWidth: cardWidth - 8);
      hintPainter.paint(
        canvas,
        Offset(-hintPainter.width / 2, cardHeight / 2 - hintPainter.height - 8),
      );
    }

    // 恢复画布状态
    canvas.restore();
  }

  // 绘制卡背图片，支持自适应显示
  void _drawCardBackImage(Canvas canvas, RRect cardRect, double cardWidth, double cardHeight) {
    // 先绘制渐变背景作为后备
    final gradientPaint = Paint()
      ..shader = const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF8B5CF6),
          Color(0xFF7C3AED),
          Color(0xFF6D28D9),
        ],
      ).createShader(Rect.fromCenter(
        center: Offset.zero,
        width: cardWidth,
        height: cardHeight,
      ));

    canvas.drawRRect(cardRect, gradientPaint);

    // TODO: 这里可以添加真实的卡背图片绘制
    // 由于 CustomPainter 中直接绘制图片比较复杂，
    // 我们保持当前的渐变背景，但添加一些装饰元素

    // 添加装饰性的圆形图案
    final decorPaint = Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..style = PaintingStyle.fill;

    // 绘制中心装饰圆
    canvas.drawCircle(Offset.zero, cardWidth * 0.15, decorPaint);

    // 绘制装饰性边框圆
    final borderDecorPaint = Paint()
      ..color = Colors.white.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    canvas.drawCircle(Offset.zero, cardWidth * 0.25, borderDecorPaint);
    canvas.drawCircle(Offset.zero, cardWidth * 0.35, borderDecorPaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  @override
  bool hitTest(Offset position) => true;
}
