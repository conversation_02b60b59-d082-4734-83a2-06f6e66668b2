import 'package:flutter/material.dart';
import '../models/soul_message.dart';
import 'soul_choice_buttons.dart';

class SoulChatBubble extends StatelessWidget {
  final SoulMessage message;
  final VoidCallback? onTarotRequest;

  const SoulChatBubble({
    Key? key,
    required this.message,
    this.onTarotRequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: message.isUser 
          ? MainAxisAlignment.end 
          : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildAvatar(),
          if (!message.isUser) const SizedBox(width: 12),
          Flexible(
            child: Column(
              crossAxisAlignment: message.isUser 
                ? CrossAxisAlignment.end 
                : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context),
                if (message.messageType == SoulMessageType.welcome)
                  _buildChoiceButtons(context),
              ],
            ),
          ),
          if (message.isUser) const SizedBox(width: 12),
          if (message.isUser) _buildUserAvatar(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: _getEnergyGradient(message.energyLevel),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: _getEnergyColor(message.energyLevel).withOpacity(0.3),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: const Icon(
        Icons.auto_awesome,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: message.isUser 
          ? const LinearGradient(
              colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
            )
          : LinearGradient(
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
            ),
        borderRadius: BorderRadius.circular(20),
        border: message.isUser 
          ? null 
          : Border.all(
              color: _getEnergyColor(message.energyLevel).withOpacity(0.3),
              width: 1,
            ),
        boxShadow: [
          BoxShadow(
            color: message.isUser 
              ? Colors.purple.withOpacity(0.3)
              : _getEnergyColor(message.energyLevel).withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.messageType != SoulMessageType.normal)
            _buildMessageTypeIndicator(),
          Text(
            message.content,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              height: 1.5,
              fontWeight: message.isUser 
                ? FontWeight.w500 
                : FontWeight.w400,
            ),
          ),
          if (message.tarotCards != null && message.tarotCards!.isNotEmpty)
            _buildTarotCards(),
        ],
      ),
    );
  }

  Widget _buildMessageTypeIndicator() {
    String indicator;
    switch (message.messageType) {
      case SoulMessageType.welcome:
        indicator = '🌌 高我连接';
        break;
      case SoulMessageType.tarotRequest:
        indicator = '🔮 塔罗引导';
        break;
      case SoulMessageType.tarotReading:
        indicator = '✨ 灵魂解读';
        break;
      case SoulMessageType.insight:
        indicator = '💎 深度洞察';
        break;
      case SoulMessageType.healing:
        indicator = '💝 疗愈夸夸';
        break;
      case SoulMessageType.guidance:
        indicator = '🌟 智慧指引';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getEnergyColor(message.energyLevel).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        indicator,
        style: TextStyle(
          color: _getEnergyColor(message.energyLevel),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTarotCards() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔮 选中的塔罗牌',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: message.tarotCards!.map((card) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                card,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildChoiceButtons(BuildContext context) {
    return SoulChoiceButtons(
      onChoice: (choice) {
        // 处理选择
        _handleChoice(context, choice);
      },
      language: 'zh', // 这里应该从LanguageManager获取
    );
  }

  void _handleChoice(BuildContext context, String choice) {
    // 根据选择类型处理
    switch (choice) {
      case 'explore':
        // 进入探索模式，可以触发塔罗请求
        if (onTarotRequest != null) {
          onTarotRequest!();
        }
        break;
      case 'share':
      case 'happy':
      case 'growth':
      case 'effort':
      case 'grateful':
        // 进入夸夸模式
        _triggerPraiseMode(context, choice);
        break;
      case 'diary':
        // 查看日记
        _viewDiary(context);
        break;
    }
  }

  void _triggerPraiseMode(BuildContext context, String type) {
    // 这里可以发送特定的消息来触发夸夸模式
    // 实际实现中需要通过回调或状态管理来处理
  }

  void _viewDiary(BuildContext context) {
    // 导航到日记页面或显示日记内容
    // 实际实现中需要集成日记功能
  }

  Color _getEnergyColor(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const Color(0xFFFFD700); // 金色
      case EnergyLevel.mystical:
        return const Color(0xFF9333EA); // 紫色
      case EnergyLevel.healing:
        return const Color(0xFF10B981); // 绿色
      case EnergyLevel.wisdom:
        return const Color(0xFF3B82F6); // 蓝色
      case EnergyLevel.love:
        return const Color(0xFFEC4899); // 粉色
      case EnergyLevel.neutral:
        return const Color(0xFFE5E7EB); // 白色
    }
  }

  Gradient _getEnergyGradient(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
        );
      case EnergyLevel.mystical:
        return const LinearGradient(
          colors: [Color(0xFF9333EA), Color(0xFF6B46C1)],
        );
      case EnergyLevel.healing:
        return const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        );
      case EnergyLevel.wisdom:
        return const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
        );
      case EnergyLevel.love:
        return const LinearGradient(
          colors: [Color(0xFFEC4899), Color(0xFFDB2777)],
        );
      case EnergyLevel.neutral:
        return const LinearGradient(
          colors: [Color(0xFFE5E7EB), Color(0xFFD1D5DB)],
        );
    }
  }
}
