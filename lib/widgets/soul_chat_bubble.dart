import 'package:flutter/material.dart';
import '../models/soul_message.dart';
import '../services/diary_service.dart';
import 'soul_choice_buttons.dart';

class SoulChatBubble extends StatelessWidget {
  final SoulMessage message;
  final VoidCallback? onTarotRequest;

  const SoulChatBubble({
    Key? key,
    required this.message,
    this.onTarotRequest,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: message.isUser 
          ? MainAxisAlignment.end 
          : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isUser) _buildAvatar(),
          if (!message.isUser) const SizedBox(width: 12),
          Flexible(
            child: Column(
              crossAxisAlignment: message.isUser 
                ? CrossAxisAlignment.end 
                : CrossAxisAlignment.start,
              children: [
                _buildMessageBubble(context),
                if (message.messageType == SoulMessageType.welcome)
                  _buildChoiceButtons(context),
              ],
            ),
          ),
          if (message.isUser) const SizedBox(width: 12),
          if (message.isUser) _buildUserAvatar(),
        ],
      ),
    );
  }

  Widget _buildAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: _getEnergyGradient(message.energyLevel),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: _getEnergyColor(message.energyLevel).withOpacity(0.3),
            blurRadius: 8,
            spreadRadius: 2,
          ),
        ],
      ),
      child: const Icon(
        Icons.auto_awesome,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildUserAvatar() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Icon(
        Icons.person,
        color: Colors.white,
        size: 20,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: message.isUser 
          ? const LinearGradient(
              colors: [Color(0xFF6B46C1), Color(0xFF9333EA)],
            )
          : LinearGradient(
              colors: [
                Colors.white.withOpacity(0.1),
                Colors.white.withOpacity(0.05),
              ],
            ),
        borderRadius: BorderRadius.circular(20),
        border: message.isUser 
          ? null 
          : Border.all(
              color: _getEnergyColor(message.energyLevel).withOpacity(0.3),
              width: 1,
            ),
        boxShadow: [
          BoxShadow(
            color: message.isUser 
              ? Colors.purple.withOpacity(0.3)
              : _getEnergyColor(message.energyLevel).withOpacity(0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (message.messageType != SoulMessageType.normal)
            _buildMessageTypeIndicator(),
          Text(
            message.content,
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              height: 1.5,
              fontWeight: message.isUser 
                ? FontWeight.w500 
                : FontWeight.w400,
            ),
          ),
          if (message.tarotCards != null && message.tarotCards!.isNotEmpty)
            _buildTarotCards(),
        ],
      ),
    );
  }

  Widget _buildMessageTypeIndicator() {
    String indicator;
    switch (message.messageType) {
      case SoulMessageType.welcome:
        indicator = '🌌 高我连接';
        break;
      case SoulMessageType.tarotRequest:
        indicator = '🔮 塔罗引导';
        break;
      case SoulMessageType.tarotReading:
        indicator = '✨ 灵魂解读';
        break;
      case SoulMessageType.insight:
        indicator = '💎 深度洞察';
        break;
      case SoulMessageType.healing:
        indicator = '💝 疗愈夸夸';
        break;
      case SoulMessageType.guidance:
        indicator = '🌟 智慧指引';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: _getEnergyColor(message.energyLevel).withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        indicator,
        style: TextStyle(
          color: _getEnergyColor(message.energyLevel),
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildTarotCards() {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.black.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🔮 选中的塔罗牌',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: message.tarotCards!.map((card) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                card,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                ),
              ),
            )).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildChoiceButtons(BuildContext context) {
    return SoulChoiceButtons(
      onChoice: (choice) {
        // 处理选择
        _handleChoice(context, choice);
      },
      language: 'zh', // 这里应该从LanguageManager获取
    );
  }

  void _handleChoice(BuildContext context, String choice) {
    // 根据选择类型处理
    switch (choice) {
      case 'explore':
        // 进入探索模式，可以触发塔罗请求
        if (onTarotRequest != null) {
          onTarotRequest!();
        }
        break;
      case 'share':
      case 'happy':
      case 'growth':
      case 'effort':
      case 'grateful':
        // 进入夸夸模式
        _triggerPraiseMode(context, choice);
        break;
      case 'diary':
        // 查看日记
        _viewDiary(context);
        break;
    }
  }

  void _triggerPraiseMode(BuildContext context, String type) {
    // 这里可以发送特定的消息来触发夸夸模式
    // 实际实现中需要通过回调或状态管理来处理
  }

  void _viewDiary(BuildContext context) async {
    // 获取最近的日记并显示
    final recentDiaries = await DiaryService.getRecentDiaries(days: 7, limit: 3);

    if (!context.mounted) return;

    if (recentDiaries.isEmpty) {
      // 如果没有日记，创建一些示例日记
      await DiaryService.createSampleDiaries();
      if (!context.mounted) return;

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已为您创建一些示例日记，高我正在学习了解您...'),
          backgroundColor: Colors.purple,
        ),
      );
      return;
    }

    // 显示日记列表对话框
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📝 最近的日记'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: recentDiaries.length,
            itemBuilder: (context, index) {
              final diary = recentDiaries[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: Text(
                    diary.moodEmoji,
                    style: const TextStyle(fontSize: 20),
                  ),
                  title: Text(
                    diary.summary,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  subtitle: Text(
                    '${diary.createdAt.toString().substring(0, 10)} • 心情: ${diary.moodScore ?? "未评分"}',
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () {
                    Navigator.of(context).pop();
                    _showDiaryDetail(context, diary);
                  },
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showDiaryDetail(BuildContext context, diary) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('📖 ${diary.createdAt.toString().substring(0, 10)}'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (diary.moodScore != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Row(
                    children: [
                      Text(diary.moodEmoji, style: const TextStyle(fontSize: 20)),
                      const SizedBox(width: 8),
                      Text('心情评分: ${diary.moodScore}/10'),
                    ],
                  ),
                ),
              if (diary.tags.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: Wrap(
                    spacing: 4,
                    children: diary.tags.map<Widget>((tag) => Chip(
                      label: Text(tag, style: const TextStyle(fontSize: 10)),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    )).toList(),
                  ),
                ),
              Text(diary.content),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
        ],
      ),
    );
  }

  Color _getEnergyColor(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const Color(0xFFFFD700); // 金色
      case EnergyLevel.mystical:
        return const Color(0xFF9333EA); // 紫色
      case EnergyLevel.healing:
        return const Color(0xFF10B981); // 绿色
      case EnergyLevel.wisdom:
        return const Color(0xFF3B82F6); // 蓝色
      case EnergyLevel.love:
        return const Color(0xFFEC4899); // 粉色
      case EnergyLevel.neutral:
        return const Color(0xFFE5E7EB); // 白色
    }
  }

  Gradient _getEnergyGradient(EnergyLevel level) {
    switch (level) {
      case EnergyLevel.divine:
        return const LinearGradient(
          colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
        );
      case EnergyLevel.mystical:
        return const LinearGradient(
          colors: [Color(0xFF9333EA), Color(0xFF6B46C1)],
        );
      case EnergyLevel.healing:
        return const LinearGradient(
          colors: [Color(0xFF10B981), Color(0xFF059669)],
        );
      case EnergyLevel.wisdom:
        return const LinearGradient(
          colors: [Color(0xFF3B82F6), Color(0xFF1D4ED8)],
        );
      case EnergyLevel.love:
        return const LinearGradient(
          colors: [Color(0xFFEC4899), Color(0xFFDB2777)],
        );
      case EnergyLevel.neutral:
        return const LinearGradient(
          colors: [Color(0xFFE5E7EB), Color(0xFFD1D5DB)],
        );
    }
  }
}
