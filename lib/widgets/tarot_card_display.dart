import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:ai_tarot_reading/utils/tarot_image_manager.dart';
import 'package:ai_tarot_reading/services/tarot_data_service.dart';
import 'package:ai_tarot_reading/utils/language_manager.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// 塔罗牌显示组件 - 支持正逆位显示
class TarotCardDisplay extends StatelessWidget {
  final TarotCard card;
  final double width;
  final double height;
  final bool showDetails;
  final VoidCallback? onTap;
  final bool enableAnimation;

  const TarotCardDisplay({
    super.key,
    required this.card,
    this.width = 160,
    this.height = 240,
    this.showDetails = true,
    this.onTap,
    this.enableAnimation = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildCardImage(),
          if (showDetails) ...[
            const SizedBox(height: 12),
            _buildCardInfo(),
          ],
        ],
      ),
    );
  }

  Widget _buildCardImage() {
    Widget cardImage = Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Stack(
          children: [
            // 卡牌图片
            Transform(
              alignment: Alignment.center,
              transform: Matrix4.identity()
                ..rotateZ(card.isReversed ? 3.14159 : 0), // 逆位时旋转180度
              child: TarotImageManager.buildCardImage(
                cardName: card.name,
                width: width,
                height: height,
                fit: BoxFit.cover,
              ),
            ),
            
            // 逆位指示器
            if (card.isReversed)
              Positioned(
                top: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.8),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    '逆位',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );

    // 添加动画效果
    if (enableAnimation) {
      cardImage = cardImage.animate()
          .fadeIn(duration: 500.ms)
          .scale(
            begin: const Offset(0.9, 0.9),
            end: const Offset(1.0, 1.0),
            duration: 500.ms,
          );
    }

    return cardImage;
  }

  Widget _buildCardInfo() {
    return Column(
      children: [
        // 卡牌名称
        Text(
          card.isReversed ? '${card.name} (逆位)' : card.name,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: card.isReversed ? Colors.red.shade700 : Colors.purple.shade700,
          ),
          textAlign: TextAlign.center,
        ),
        
        const SizedBox(height: 8),
        
        // 卡牌含义
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: card.isReversed 
                ? Colors.red.shade50 
                : Colors.purple.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: card.isReversed 
                  ? Colors.red.shade200 
                  : Colors.purple.shade200,
            ),
          ),
          child: Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                card.getTranslatedMeaning(languageManager.translate),
                style: TextStyle(
                  fontSize: 14,
                  color: card.isReversed ? Colors.red.shade800 : Colors.purple.shade800,
                ),
                textAlign: TextAlign.center,
              );
            },
          ),
        ),
        
        const SizedBox(height: 8),
        
        // 关键词标签
        Consumer<LanguageManager>(
          builder: (context, languageManager, child) {
            final keywords = TarotDataService.instance.getTranslatedKeywords(card, languageManager);
            return Wrap(
              alignment: WrapAlignment.center,
              spacing: 6,
              runSpacing: 4,
              children: keywords.take(4).map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: card.isReversed
                        ? Colors.red.shade100
                        : Colors.purple.shade100,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: card.isReversed ? Colors.red.shade700 : Colors.purple.shade700,
                    ),
                  ),
                );
              }).toList(),
            );
          },
        ),
      ],
    );
  }
}

/// 简化版塔罗牌显示组件 - 仅显示卡牌图片
class SimpleTarotCardDisplay extends StatelessWidget {
  final TarotCard card;
  final double width;
  final double height;
  final VoidCallback? onTap;
  final bool showOrientationIndicator;

  const SimpleTarotCardDisplay({
    super.key,
    required this.card,
    this.width = 100,
    this.height = 150,
    this.onTap,
    this.showOrientationIndicator = true,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // 卡牌图片
              Transform(
                alignment: Alignment.center,
                transform: Matrix4.identity()
                  ..rotateZ(card.isReversed ? 3.14159 : 0), // 逆位时旋转180度
                child: TarotImageManager.buildCardImage(
                  cardName: card.name,
                  width: width,
                  height: height,
                  fit: BoxFit.cover,
                ),
              ),
              
              // 逆位指示器
              if (card.isReversed && showOrientationIndicator)
                Positioned(
                  top: 4,
                  right: 4,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.red,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: const Icon(
                      Icons.rotate_left,
                      color: Colors.white,
                      size: 10,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 塔罗牌详情显示组件 - 显示完整的正逆位信息
class TarotCardDetailDisplay extends StatelessWidget {
  final TarotCard card;

  const TarotCardDetailDisplay({
    super.key,
    required this.card,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          Row(
            children: [
              Icon(
                card.isMajorArcana ? Icons.auto_awesome : Icons.diamond,
                color: card.isReversed ? Colors.red.shade600 : Colors.purple.shade600,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Consumer<LanguageManager>(
                  builder: (context, languageManager, child) {
                    final translatedName = card.getTranslatedName(languageManager.translate);
                    final positionText = card.isReversed
                        ? languageManager.translate('reversed_position')
                        : languageManager.translate('upright_position');
                    return Text(
                      card.isReversed ? '$translatedName ($positionText)' : translatedName,
                      style: TextStyle(
                        fontSize: 22,
                        fontWeight: FontWeight.bold,
                        color: card.isReversed ? Colors.red.shade700 : Colors.purple.shade700,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 4),
          
          // 卡牌类型
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              return Text(
                card.isMajorArcana
                    ? languageManager.translate('major_arcana')
                    : languageManager.translate('minor_arcana'),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey.shade600,
                  fontStyle: FontStyle.italic,
                ),
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // 含义部分
          Consumer<LanguageManager>(
            builder: (context, languageManager, child) {
              final title = card.isReversed
                  ? languageManager.translate('reversed_meaning')
                  : languageManager.translate('upright_meaning');
              return _buildSection(
                title: title,
                content: card.getTranslatedMeaning(languageManager.translate),
                color: card.isReversed ? Colors.red : Colors.purple,
              );
            },
          ),
          
          const SizedBox(height: 16),
          
          // 关键词部分
          _buildKeywordsSection(),
          
          // 如果是正位，也显示逆位含义作为对比
          if (!card.isReversed) ...[
            const SizedBox(height: 16),
            _buildSection(
              title: '逆位含义 (对比)',
              content: TarotCardsData.getReversedMeaning(card),
              color: Colors.red,
              isSecondary: true,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required String content,
    required Color color,
    bool isSecondary = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: color.shade700,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSecondary ? color.shade50 : color.shade100,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSecondary ? color.shade200 : color.shade300,
              width: isSecondary ? 1 : 2,
            ),
          ),
          child: Text(
            content,
            style: TextStyle(
              fontSize: 14,
              color: color.shade800,
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildKeywordsSection() {
    return Consumer<LanguageManager>(
      builder: (context, languageManager, child) {
        final keywords = TarotDataService.instance.getTranslatedKeywords(card, languageManager);
        final color = card.isReversed ? Colors.red : Colors.purple;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              languageManager.translate('keywords'),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              runSpacing: 6,
              children: keywords.map((keyword) {
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: color.shade100,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: color.shade300),
                  ),
                  child: Text(
                    keyword,
                    style: TextStyle(
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                      color: color.shade700,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        );
      },
    );
  }
} 