import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/figma_service.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:flutter_animate/flutter_animate.dart';

/// Figma设计的塔罗牌组件
class FigmaCardComponent extends StatelessWidget {
  final TarotCard card;
  final bool isRevealed;
  final bool isSelected;
  final VoidCallback? onTap;
  final double width;
  final double height;

  const FigmaCardComponent({
    super.key,
    required this.card,
    this.isRevealed = true,
    this.isSelected = false,
    this.onTap,
    this.width = 120,
    this.height = 180,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: width,
        height: height,
        child: Stack(
          children: [
            // 卡牌背景
            _buildCardBackground(),
            
            // 卡牌内容
            if (isRevealed) _buildCardContent() else _buildCardBack(),
            
            // 选中状态指示器
            if (isSelected) _buildSelectionIndicator(),
            
            // 悬浮效果
            _buildHoverEffect(),
          ],
        ),
      ),
    ).animate(target: isSelected ? 1 : 0)
        .scale(begin: const Offset(1.0, 1.0), end: const Offset(1.05, 1.05))
        .then()
        .shimmer(duration: 1.seconds);
  }

  Widget _buildCardBackground() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['lg']!),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
          if (isSelected)
            BoxShadow(
              color: _getCardColor().withValues(alpha: 0.4),
              blurRadius: 16,
              offset: const Offset(0, 0),
            ),
        ],
      ),
    );
  }

  Widget _buildCardContent() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['lg']!),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCardColor().withValues(alpha: 0.1),
            _getCardColor().withValues(alpha: 0.3),
          ],
        ),
        border: Border.all(
          color: _getCardColor().withValues(alpha: 0.5),
          width: 2,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(FigmaDesignTokens.spacing['sm']!),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // 卡牌编号
            _buildCardNumber(),
            
            // 卡牌图标/图片
            _buildCardIcon(),
            
            // 卡牌名称
            _buildCardName(),
            
            // 卡牌关键词
            _buildCardKeywords(),
          ],
        ),
      ),
    );
  }

  Widget _buildCardBack() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['lg']!),
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF1F2937),
            Color(0xFF374151),
            Color(0xFF4B5563),
          ],
        ),
        border: Border.all(
          color: const Color(0xFF6B7280),
          width: 2,
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.auto_awesome,
              size: 32,
              color: Colors.white.withValues(alpha: 0.8),
            ),
            SizedBox(height: FigmaDesignTokens.spacing['sm']!),
            Text(
              '✦',
              style: TextStyle(
                fontSize: 24,
                color: Colors.white.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardNumber() {
    return Align(
      alignment: Alignment.topLeft,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: FigmaDesignTokens.spacing['xs']!,
          vertical: 2,
        ),
        decoration: BoxDecoration(
          color: _getCardColor().withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['sm']!),
        ),
        child: Text(
          card.id,
          style: const TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  Widget _buildCardIcon() {
    return Container(
      width: 48,
      height: 48,
      decoration: BoxDecoration(
        color: _getCardColor().withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['md']!),
      ),
      child: Icon(
        _getCardIcon(),
        size: 24,
        color: Colors.white,
      ),
    );
  }

  Widget _buildCardName() {
    return Text(
      card.name,
      style: const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildCardKeywords() {
    return Wrap(
      spacing: 2,
      runSpacing: 2,
      children: card.keywords.take(2).map((keyword) {
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            keyword,
            style: const TextStyle(
              fontSize: 8,
              color: Colors.white,
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildSelectionIndicator() {
    return Positioned(
      top: -2,
      right: -2,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: _getCardColor(),
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: const Icon(
          Icons.check,
          size: 12,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildHoverEffect() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(FigmaDesignTokens.borderRadius['lg']!),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white.withValues(alpha: 0.0),
            Colors.white.withValues(alpha: 0.1),
          ],
        ),
      ),
    );
  }

  Color _getCardColor() {
    // 根据卡牌类型返回不同颜色
    if (card.suit == 'Major Arcana') {
      return const Color(0xFF6B46C1); // 紫色
    } else if (card.suit == 'Cups') {
      return const Color(0xFF3B82F6); // 蓝色
    } else if (card.suit == 'Wands') {
      return const Color(0xFFEF4444); // 红色
    } else if (card.suit == 'Swords') {
      return const Color(0xFF6B7280); // 灰色
    } else if (card.suit == 'Pentacles') {
      return const Color(0xFF10B981); // 绿色
    }
    return const Color(0xFF6B46C1);
  }

  IconData _getCardIcon() {
    // 根据卡牌类型返回不同图标
    if (card.suit == 'Major Arcana') {
      return Icons.auto_awesome;
    } else if (card.suit == 'Cups') {
      return Icons.local_drink;
    } else if (card.suit == 'Wands') {
      return Icons.whatshot;
    } else if (card.suit == 'Swords') {
      return Icons.flash_on;
    } else if (card.suit == 'Pentacles') {
      return Icons.monetization_on;
    }
    return Icons.auto_awesome;
  }
}
