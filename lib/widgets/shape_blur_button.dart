import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

class ShapeBlurButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final double width;
  final double height;
  final Color baseColor;
  final Color textColor;
  final double shapeSize;
  final double roundness;
  final double borderSize;
  final double circleSize;
  final double circleEdge;

  const ShapeBlurButton({
    super.key,
    required this.text,
    this.onPressed,
    this.width = 280,
    this.height = 56,
    this.baseColor = const Color(0xFF8B5CF6),
    this.textColor = Colors.white,
    this.shapeSize = 1.2,
    this.roundness = 0.4,
    this.borderSize = 0.05,
    this.circleSize = 0.3,
    this.circleEdge = 0.5,
  });

  @override
  State<ShapeBlurButton> createState() => _ShapeBlurButtonState();
}

class _ShapeBlurButtonState extends State<ShapeBlurButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  Offset _mousePosition = Offset.zero;
  Offset _dampedMousePosition = Offset.zero;
  bool _isHovering = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 16),
      vsync: this,
    )..repeat();

    _animationController.addListener(() {
      setState(() {
        // 平滑鼠标位置
        _dampedMousePosition = Offset.lerp(
          _dampedMousePosition,
          _mousePosition,
          0.1,
        )!;
      });
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateMousePosition(Offset globalPosition, Size size) {
    setState(() {
      _mousePosition = Offset(
        globalPosition.dx / size.width,
        globalPosition.dy / size.height,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isHovering = true),
        onExit: (_) => setState(() => _isHovering = false),
        onHover: (event) {
          final RenderBox renderBox = context.findRenderObject() as RenderBox;
          final localPosition = renderBox.globalToLocal(event.position);
          _updateMousePosition(localPosition, renderBox.size);
        },
        child: GestureDetector(
          onTapDown: (details) {
            final RenderBox renderBox = context.findRenderObject() as RenderBox;
            final localPosition = renderBox.globalToLocal(details.globalPosition);
            _updateMousePosition(localPosition, renderBox.size);
          },
          onTap: widget.onPressed,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(widget.width, widget.height),
                painter: ShapeBlurPainter(
                  mousePosition: _dampedMousePosition,
                  baseColor: widget.baseColor,
                  shapeSize: widget.shapeSize,
                  roundness: widget.roundness,
                  borderSize: widget.borderSize,
                  circleSize: widget.circleSize,
                  circleEdge: widget.circleEdge,
                  isHovering: _isHovering,
                ),
                child: Container(
                  width: widget.width,
                  height: widget.height,
                  alignment: Alignment.center,
                  child: Text(
                    widget.text,
                    style: TextStyle(
                      color: widget.textColor,
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      letterSpacing: 0.5,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class ShapeBlurPainter extends CustomPainter {
  final Offset mousePosition;
  final Color baseColor;
  final double shapeSize;
  final double roundness;
  final double borderSize;
  final double circleSize;
  final double circleEdge;
  final bool isHovering;

  ShapeBlurPainter({
    required this.mousePosition,
    required this.baseColor,
    required this.shapeSize,
    required this.roundness,
    required this.borderSize,
    required this.circleSize,
    required this.circleEdge,
    required this.isHovering,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill;

    // 创建基础渐变
    final gradient = ui.Gradient.linear(
      Offset.zero,
      Offset(size.width, size.height),
      [
        baseColor,
        baseColor.withOpacity(0.8),
        baseColor.withOpacity(0.6),
      ],
      [0.0, 0.5, 1.0],
    );

    // 绘制基础按钮形状
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Radius.circular(size.height * roundness),
    );

    paint.shader = gradient;
    canvas.drawRRect(rect, paint);

    // 绘制鼠标交互效果
    if (isHovering) {
      _drawMouseEffect(canvas, size);
    }

    // 绘制边框效果
    _drawBorderEffect(canvas, size);
  }

  void _drawMouseEffect(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..blendMode = BlendMode.overlay;

    // 计算鼠标位置
    final mouseX = mousePosition.dx * size.width;
    final mouseY = mousePosition.dy * size.height;

    // 创建径向渐变
    final gradient = ui.Gradient.radial(
      Offset(mouseX, mouseY),
      circleSize * math.min(size.width, size.height),
      [
        Colors.white.withOpacity(0.3),
        Colors.white.withOpacity(0.1),
        Colors.transparent,
      ],
      [0.0, 0.5, 1.0],
    );

    paint.shader = gradient;

    // 绘制圆形光晕
    canvas.drawCircle(
      Offset(mouseX, mouseY),
      circleSize * math.min(size.width, size.height),
      paint,
    );
  }

  void _drawBorderEffect(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderSize * math.min(size.width, size.height)
      ..color = Colors.white.withOpacity(0.2);

    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        paint.strokeWidth / 2,
        paint.strokeWidth / 2,
        size.width - paint.strokeWidth,
        size.height - paint.strokeWidth,
      ),
      Radius.circular(size.height * roundness),
    );

    canvas.drawRRect(rect, paint);

    // 添加内部光晕
    final innerPaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1
      ..color = Colors.white.withOpacity(0.1);

    final innerRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(2, 2, size.width - 4, size.height - 4),
      Radius.circular(size.height * roundness - 2),
    );

    canvas.drawRRect(innerRect, innerPaint);
  }

  @override
  bool shouldRepaint(ShapeBlurPainter oldDelegate) {
    return oldDelegate.mousePosition != mousePosition ||
           oldDelegate.isHovering != isHovering ||
           oldDelegate.baseColor != baseColor;
  }
}
