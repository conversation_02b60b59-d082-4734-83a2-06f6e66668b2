import 'package:flutter/material.dart';
import 'dart:math' as math;

class GalaxySpiralTransition extends StatefulWidget {
  final VoidCallback onComplete;
  
  const GalaxySpiralTransition({
    Key? key,
    required this.onComplete,
  }) : super(key: key);

  @override
  State<GalaxySpiralTransition> createState() => _GalaxySpiralTransitionState();
}

class _GalaxySpiralTransitionState extends State<GalaxySpiralTransition>
    with TickerProviderStateMixin {
  late AnimationController _spiralController;
  late AnimationController _colorController;
  late AnimationController _scaleController;
  
  late Animation<double> _spiralAnimation;
  late Animation<double> _colorAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // 漩涡旋转动画
    _spiralController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );
    
    // 颜色变化动画
    _colorController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    // 缩放动画
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _spiralAnimation = Tween<double>(
      begin: 0.0,
      end: 4 * math.pi,
    ).animate(CurvedAnimation(
      parent: _spiralController,
      curve: Curves.easeInOut,
    ));
    
    _colorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _colorController,
      curve: Curves.easeInOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 20.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInQuart,
    ));
    
    _startAnimation();
  }

  void _startAnimation() async {
    // 同时开始漩涡和颜色动画
    _spiralController.forward();
    _colorController.forward();
    
    // 等待2秒后开始放大动画
    await Future.delayed(const Duration(milliseconds: 2000));
    _scaleController.forward();
    
    // 等待放大动画完成后回调
    await Future.delayed(const Duration(milliseconds: 1500));
    widget.onComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          radius: 1.0,
          colors: [
            Color(0xFF0a0a0a),
            Color(0xFF1a1a2e),
            Color(0xFF16213e),
          ],
        ),
      ),
      child: AnimatedBuilder(
        animation: Listenable.merge([
          _spiralAnimation,
          _colorAnimation,
          _scaleAnimation,
        ]),
        builder: (context, child) {
          return Transform.scale(
            scale: 1.0 + _scaleAnimation.value,
            child: CustomPaint(
              painter: GalaxySpiralPainter(
                spiralProgress: _spiralAnimation.value,
                colorProgress: _colorAnimation.value,
              ),
              size: Size.infinite,
            ),
          );
        },
      ),
    );
  }

  @override
  void dispose() {
    _spiralController.dispose();
    _colorController.dispose();
    _scaleController.dispose();
    super.dispose();
  }
}

class GalaxySpiralPainter extends CustomPainter {
  final double spiralProgress;
  final double colorProgress;

  GalaxySpiralPainter({
    required this.spiralProgress,
    required this.colorProgress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = math.min(size.width, size.height) / 2;

    // 绘制多层漩涡
    for (int layer = 0; layer < 5; layer++) {
      _drawSpiralLayer(canvas, center, maxRadius, layer);
    }
    
    // 绘制中心光点
    _drawCenterLight(canvas, center);
  }

  void _drawSpiralLayer(Canvas canvas, Offset center, double maxRadius, int layer) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0 + layer * 0.5;

    final layerRadius = maxRadius * (0.8 - layer * 0.15);
    final spiralTurns = 3 + layer;
    final points = <Offset>[];

    // 生成漩涡路径点
    for (double t = 0; t <= spiralProgress; t += 0.01) {
      final angle = t * spiralTurns;
      final radius = layerRadius * (t / spiralProgress) * 
                    (1 + 0.3 * math.sin(t * 10 + layer));
      
      final x = center.dx + radius * math.cos(angle);
      final y = center.dy + radius * math.sin(angle);
      points.add(Offset(x, y));
    }

    // 绘制渐变色漩涡
    for (int i = 0; i < points.length - 1; i++) {
      final progress = i / points.length;
      final hue = (progress * 360 + colorProgress * 360 + layer * 60) % 360;
      final color = HSVColor.fromAHSV(
        0.6 + 0.4 * progress,
        hue,
        0.8 + 0.2 * math.sin(progress * math.pi),
        0.7 + 0.3 * progress,
      ).toColor();

      paint.color = color;
      canvas.drawLine(points[i], points[i + 1], paint);
    }
  }

  void _drawCenterLight(Canvas canvas, Offset center) {
    // 中心发光效果
    final lightPaint = Paint()
      ..shader = RadialGradient(
        colors: [
          Colors.white.withOpacity(0.8),
          Colors.purple.withOpacity(0.6),
          Colors.blue.withOpacity(0.3),
          Colors.transparent,
        ],
        stops: const [0.0, 0.3, 0.6, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: 50));

    canvas.drawCircle(center, 50 * colorProgress, lightPaint);
    
    // 内部亮点
    final corePaint = Paint()
      ..color = Colors.white.withOpacity(colorProgress)
      ..style = PaintingStyle.fill;
    
    canvas.drawCircle(center, 10 * colorProgress, corePaint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
