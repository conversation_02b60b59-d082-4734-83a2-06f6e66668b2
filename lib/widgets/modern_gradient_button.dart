import 'package:flutter/material.dart';
import 'dart:ui';

/// 苹果Liquid Glass风格的现代化渐变按钮
/// 基于最新iOS 26设计语言，融合液态玻璃视觉效果
class LiquidGlassButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final LiquidGlassButtonStyle style;
  final double? width;
  final double height;
  final IconData? icon;
  final bool isLoading;
  final bool enabled;

  const LiquidGlassButton({
    super.key,
    required this.text,
    this.onPressed,
    this.style = LiquidGlassButtonStyle.primary,
    this.width,
    this.height = 52,
    this.icon,
    this.isLoading = false,
    this.enabled = true,
  });

  @override
  State<LiquidGlassButton> createState() => _LiquidGlassButtonState();
}

class _LiquidGlassButtonState extends State<LiquidGlassButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enabled && !widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
      widget.onPressed?.call();
    }
  }

  void _onTapCancel() {
    if (widget.enabled && !widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final buttonColors = _getButtonColors();
    final isDisabled = !widget.enabled || widget.isLoading;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: _onTapDown,
            onTapUp: _onTapUp,
            onTapCancel: _onTapCancel,
            child: Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(26),
                boxShadow: [
                  // 外层阴影 - 立体效果
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                    spreadRadius: 0,
                  ),
                  // 内层阴影 - 深度效果
                  BoxShadow(
                    color: Colors.black.withOpacity(0.08),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                  // 发光效果
                  if (_glowAnimation.value > 0)
                    BoxShadow(
                      color: buttonColors.glowColor.withOpacity(
                        _glowAnimation.value * 0.6,
                      ),
                      blurRadius: 30,
                      offset: const Offset(0, 0),
                      spreadRadius: 5,
                    ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(26),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                  child: Container(
                    decoration: BoxDecoration(
                      // Liquid Glass渐变效果
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: isDisabled
                            ? [
                                Colors.grey.withOpacity(0.3),
                                Colors.grey.withOpacity(0.1),
                              ]
                            : [
                                buttonColors.startColor.withOpacity(0.9),
                                buttonColors.middleColor.withOpacity(0.8),
                                buttonColors.endColor.withOpacity(0.9),
                              ],
                        stops: const [0.0, 0.5, 1.0],
                      ),
                      // 液态玻璃边框
                      border: Border.all(
                        color: Colors.white.withOpacity(0.4),
                        width: 1.5,
                      ),
                      borderRadius: BorderRadius.circular(26),
                    ),
                    child: Container(
                      decoration: BoxDecoration(
                        // 内部光泽效果
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.white.withOpacity(0.3),
                            Colors.transparent,
                            Colors.black.withOpacity(0.1),
                          ],
                          stops: const [0.0, 0.7, 1.0],
                        ),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: Center(
                        child: widget.isLoading
                            ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    buttonColors.textColor,
                                  ),
                                ),
                              )
                            : Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  if (widget.icon != null) ...[
                                    Icon(
                                      widget.icon,
                                      color: buttonColors.textColor,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 8),
                                  ],
                                  Text(
                                    widget.text,
                                    style: TextStyle(
                                      color: buttonColors.textColor,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 0.5,
                                    ),
                                  ),
                                ],
                              ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _LiquidGlassButtonColors _getButtonColors() {
    switch (widget.style) {
      case LiquidGlassButtonStyle.primary:
        return _LiquidGlassButtonColors(
          startColor: const Color(0xFF00D2FF), // 青蓝
          middleColor: const Color(0xFF3A7BD5), // 蓝色
          endColor: const Color(0xFF9D50BB), // 紫色
          textColor: Colors.white,
          glowColor: const Color(0xFF00D2FF),
        );
      case LiquidGlassButtonStyle.success:
        return _LiquidGlassButtonColors(
          startColor: const Color(0xFF4FACFE),
          middleColor: const Color(0xFF00F2FE),
          endColor: const Color(0xFF43E97B),
          textColor: Colors.white,
          glowColor: const Color(0xFF43E97B),
        );
      case LiquidGlassButtonStyle.warning:
        return _LiquidGlassButtonColors(
          startColor: const Color(0xFFFFCE54),
          middleColor: const Color(0xFFFF8A80),
          endColor: const Color(0xFFFC466B),
          textColor: Colors.white,
          glowColor: const Color(0xFFFFCE54),
        );
      case LiquidGlassButtonStyle.danger:
        return _LiquidGlassButtonColors(
          startColor: const Color(0xFFFC466B),
          middleColor: const Color(0xFFFF6B9D),
          endColor: const Color(0xFFF093FB),
          textColor: Colors.white,
          glowColor: const Color(0xFFFC466B),
        );
      case LiquidGlassButtonStyle.purple:
        return _LiquidGlassButtonColors(
          startColor: const Color(0xFF9D50BB),
          middleColor: const Color(0xFFB565A7),
          endColor: const Color(0xFFE684AE),
          textColor: Colors.white,
          glowColor: const Color(0xFF9D50BB),
        );
      case LiquidGlassButtonStyle.glass:
        return _LiquidGlassButtonColors(
          startColor: Colors.white.withOpacity(0.4),
          middleColor: Colors.white.withOpacity(0.2),
          endColor: Colors.white.withOpacity(0.3),
          textColor: const Color(0xFF333333),
          glowColor: Colors.white,
        );
    }
  }
}

/// 按钮样式枚举
enum LiquidGlassButtonStyle {
  primary,
  success,
  warning,
  danger,
  purple,
  glass,
}

/// 按钮颜色配置
class _LiquidGlassButtonColors {
  final Color startColor;
  final Color middleColor;
  final Color endColor;
  final Color textColor;
  final Color glowColor;

  const _LiquidGlassButtonColors({
    required this.startColor,
    required this.middleColor,
    required this.endColor,
    required this.textColor,
    required this.glowColor,
  });
}

/// Liquid Glass卡片组件
class LiquidGlassCard extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double borderRadius;
  final bool showBorder;
  final Color? backgroundColor;

  const LiquidGlassCard({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding = const EdgeInsets.all(20),
    this.margin,
    this.borderRadius = 20,
    this.showBorder = true,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 40,
            offset: const Offset(0, 12),
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(borderRadius),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 20, sigmaY: 20),
          child: Container(
            padding: padding,
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.white.withOpacity(0.7),
              borderRadius: BorderRadius.circular(borderRadius),
              border: showBorder
                  ? Border.all(
                      color: Colors.white.withOpacity(0.4),
                      width: 1.5,
                    )
                  : null,
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}

/// Liquid Glass输入框组件
class LiquidGlassTextField extends StatefulWidget {
  final String? hintText;
  final String? labelText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final bool obscureText;
  final IconData? prefixIcon;
  final IconData? suffixIcon;
  final VoidCallback? onSuffixIconPressed;

  const LiquidGlassTextField({
    super.key,
    this.hintText,
    this.labelText,
    this.controller,
    this.onChanged,
    this.obscureText = false,
    this.prefixIcon,
    this.suffixIcon,
    this.onSuffixIconPressed,
  });

  @override
  State<LiquidGlassTextField> createState() => _LiquidGlassTextFieldState();
}

class _LiquidGlassTextFieldState extends State<LiquidGlassTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: _isFocused ? 20 : 10,
            offset: const Offset(0, 4),
          ),
          if (_isFocused)
            BoxShadow(
              color: const Color(0xFF00D2FF).withOpacity(0.3),
              blurRadius: 20,
              offset: const Offset(0, 0),
              spreadRadius: 2,
            ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _isFocused
                    ? const Color(0xFF00D2FF).withOpacity(0.6)
                    : Colors.white.withOpacity(0.4),
                width: _isFocused ? 2 : 1.5,
              ),
            ),
            child: TextField(
              controller: widget.controller,
              focusNode: _focusNode,
              onChanged: widget.onChanged,
              obscureText: widget.obscureText,
              style: const TextStyle(
                color: Color(0xFF333333),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              decoration: InputDecoration(
                hintText: widget.hintText,
                labelText: widget.labelText,
                hintStyle: TextStyle(
                  color: const Color(0xFF666666).withOpacity(0.7),
                  fontSize: 16,
                ),
                labelStyle: TextStyle(
                  color: _isFocused
                      ? const Color(0xFF00D2FF)
                      : const Color(0xFF666666),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 16,
                ),
                prefixIcon: widget.prefixIcon != null
                    ? Icon(
                        widget.prefixIcon,
                        color: _isFocused
                            ? const Color(0xFF00D2FF)
                            : const Color(0xFF666666),
                        size: 20,
                      )
                    : null,
                suffixIcon: widget.suffixIcon != null
                    ? IconButton(
                        onPressed: widget.onSuffixIconPressed,
                        icon: Icon(
                          widget.suffixIcon,
                          color: _isFocused
                              ? const Color(0xFF00D2FF)
                              : const Color(0xFF666666),
                          size: 20,
                        ),
                      )
                    : null,
              ),
            ),
          ),
        ),
      ),
    );
  }
} 