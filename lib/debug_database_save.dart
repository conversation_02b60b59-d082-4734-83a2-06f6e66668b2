import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:ai_tarot_reading/services/supabase_data_service.dart';
import 'package:ai_tarot_reading/services/supabase_auth_service.dart';
import 'package:uuid/uuid.dart';

class DebugDatabaseSave extends StatefulWidget {
  const DebugDatabaseSave({super.key});

  @override
  State<DebugDatabaseSave> createState() => _DebugDatabaseSaveState();
}

class _DebugDatabaseSaveState extends State<DebugDatabaseSave> {
  String _debugOutput = '';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('调试数据库保存'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testDatabaseSave,
              child: _isLoading 
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text('测试数据库保存'),
            ),
            const SizedBox(height: 16),
            const Text(
              '调试输出:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugOutput,
                    style: const TextStyle(
                      fontSize: 12,
                      fontFamily: 'monospace',
                      color: Colors.black87,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testDatabaseSave() async {
    setState(() {
      _isLoading = true;
      _debugOutput = '开始测试数据库保存...\n\n';
    });

    try {
      final authService = Provider.of<SupabaseAuthService>(context, listen: false);
      final dataService = SupabaseDataService();

      // 1. 检查用户认证状态
      _appendOutput('1. 检查用户认证状态');
      _appendOutput('当前用户: ${authService.currentUser?.email ?? "未登录"}');
      _appendOutput('用户ID: ${authService.currentUser?.id ?? "无"}');
      _appendOutput('是否已登录: ${authService.isSignedIn}');
      _appendOutput('DataService认证状态: ${dataService.isAuthenticated}');
      _appendOutput('DataService用户ID: ${dataService.currentUserId ?? "无"}');
      _appendOutput('');

      if (!authService.isSignedIn) {
        _appendOutput('❌ 错误: 用户未登录，无法保存数据');
        return;
      }

      // 2. 准备测试数据
      _appendOutput('2. 准备测试数据');
      final testData = {
        'id': const Uuid().v4(),
        'question': '测试问题：我今天运势如何？',
        'spread_type': 'single',
        'cards': [
          {
            'id': '0',
            'name': '愚者',
            'description': '新的开始',
            'meaning': '新的旅程即将开始',
            'keywords': ['新开始', '冒险'],
            'imageUrl': '',
            'isMajorArcana': true,
            'isReversed': false,
          }
        ],
        'interpretation': '测试解读内容...',
        'reading_type': 'debug_test',
        'cards_drawn': 1,
      };
      _appendOutput('测试数据准备完成');
      _appendOutput('');

      // 3. 尝试保存数据
      _appendOutput('3. 尝试保存到数据库');
      await dataService.saveTarotReading(testData);
      _appendOutput('✅ 数据保存成功!');
      _appendOutput('');

      // 4. 验证数据是否保存成功
      _appendOutput('4. 验证数据保存');
      final readings = await dataService.getTarotReadings(5);
      _appendOutput('获取到 ${readings.length} 条记录');
      
      // 查找刚才保存的记录
      final savedRecord = readings.where((r) => r['id'] == testData['id']).firstOrNull;
      if (savedRecord != null) {
        _appendOutput('✅ 找到刚保存的记录:');
        _appendOutput('  ID: ${savedRecord['id']}');
        _appendOutput('  问题: ${savedRecord['question']}');
        _appendOutput('  用户ID: ${savedRecord['user_id']}');
        _appendOutput('  创建时间: ${savedRecord['created_at']}');
      } else {
        _appendOutput('⚠️ 未找到刚保存的记录');
      }
      _appendOutput('');

      // 5. 列出最近的记录
      _appendOutput('5. 最近的解读记录:');
      for (int i = 0; i < readings.length && i < 3; i++) {
        final reading = readings[i];
        _appendOutput('  记录 ${i + 1}:');
        _appendOutput('    ID: ${reading['id']}');
        _appendOutput('    问题: ${reading['question']}');
        _appendOutput('    类型: ${reading['reading_type']}');
        _appendOutput('    创建时间: ${reading['created_at']}');
        _appendOutput('');
      }

    } catch (e, stackTrace) {
      _appendOutput('❌ 测试失败:');
      _appendOutput('错误信息: $e');
      _appendOutput('堆栈跟踪:');
      _appendOutput('$stackTrace');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _appendOutput(String text) {
    setState(() {
      _debugOutput += '$text\n';
    });
  }
} 