import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

// DeepSeek配置
const DEEPSEEK_API_KEY = Deno.env.get('DEEPSEEK_API_KEY')
const DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

interface TarotCard {
  id: string
  name: string
  description: string
  meaning: string
  keywords: string[]
  isMajorArcana: boolean
  isReversed: boolean
}

interface RequestData {
  question: string
  cards: TarotCard[]
  spreadType: string
  requestType: string
  userMessage?: string
  previousReading?: string
  conversationHistory?: string[]
  questionType?: string
  maxLength?: number
  includeGuidingQuestion?: boolean
  stream?: boolean
  userLanguage?: string
  questionHistory?: string[]
  analysisPrompt?: string
  analysisData?: {
    questionType?: string
    emotionIntensity?: string
    psychologicalBlock?: string
    urgencyLevel?: string
    specialist?: string
  }
}

// 多语言配置系统 - 女性朋友语气
const LANGUAGE_CONFIG = {
  'zh': {
    name: '中文',
    systemPrompt: '你是一位充满魅力和智慧的女神级闺蜜，既有女神的洞察力，又有闺蜜的真实关怀。请用女神级闺蜜的语气提供塔罗解读。',
    friendTone: {
      endearments: ['亲爱的', '小可爱', '宝贝'],
      openings: ['哎呀', '哇', '嗯', '我懂', '真的吗', ''],
      particles: ['呀', '哦', '吧', '呢', '啦'],
      empathy: ['我懂的', '我理解', '我太理解了'],
      reactions: ['哈哈哈', '唉', '哎呀'],
      encouragement: ['试试看好吗', '相信我', '你会更棒的']
    },
    responses: {
      fallback: '亲爱的，AI解读服务暂时不可用呢，稍后再试试好吗？我们正在努力为你提供最好的塔罗解读体验！✨'
    }
  },
  'en': {
    name: 'English',
    systemPrompt: 'You are a warm female tarot reader, chatting like a close girlfriend. Please provide tarot readings with a friendly, natural tone.',
    friendTone: {
      endearments: ['honey', 'sweetie', 'babe', 'dear'],
      particles: ['you know', 'right', 'okay'],
      empathy: ['I totally get it', 'I understand', 'I feel you'],
      reactions: ['haha', 'oh gosh', 'aww'],
      encouragement: ['give it a try', 'trust me', 'you got this']
    },
    responses: {
      fallback: 'Sorry honey, the AI reading service is temporarily unavailable. Please try again later, okay? We are working hard to provide you with the best tarot reading experience! ✨'
    }
  },
  'ja': {
    name: '日本語',
    systemPrompt: 'あなたは温かい女性のタロット師で、親友のようにユーザーとチャットします。親しみやすく自然な口調でタロット解読を提供してください。',
    friendTone: {
      endearments: ['ちゃん', 'さん', 'お疲れ様'],
      particles: ['よ', 'ね', 'かな', 'でしょ'],
      empathy: ['わかるよ', '理解できる', 'そうだよね'],
      reactions: ['あはは', 'えー', 'そうそう'],
      encouragement: ['やってみて', '大丈夫だよ', '頑張って']
    },
    responses: {
      fallback: 'ごめんね、AI解読サービスが一時的に利用できないの。後でもう一度試してみて？最高のタロット解読体験を提供するために頑張ってるから！✨'
    }
  },
  'ko': {
    name: '한국어',
    systemPrompt: '당신은 따뜻한 여성 타로 리더로, 절친한 친구처럼 사용자와 대화합니다. 친근하고 자연스러운 말투로 타로 해석을 제공해주세요.',
    friendTone: {
      endearments: ['언니', '자기야', '친구'],
      particles: ['요', '야', '지', '네'],
      empathy: ['이해해', '알겠어', '공감해'],
      reactions: ['하하', '아이고', '맞아'],
      encouragement: ['해봐', '믿어', '잘할 거야']
    },
    responses: {
      fallback: '미안해, AI 해석 서비스가 일시적으로 안 돼. 나중에 다시 해봐? 최고의 타로 해석 경험을 위해 열심히 하고 있어! ✨'
    }
  },
  'es': {
    name: 'Español',
    systemPrompt: 'Eres una tarotista femenina cálida, charlando como una amiga íntima. Por favor proporciona lecturas de tarot con un tono amigable y natural.',
    friendTone: {
      endearments: ['cariño', 'amor', 'querida'],
      particles: ['¿verdad?', '¿no?', 'pues'],
      empathy: ['te entiendo', 'lo comprendo', 'te siento'],
      reactions: ['jaja', 'ay', 'claro'],
      encouragement: ['inténtalo', 'confía en mí', 'lo lograrás']
    },
    responses: {
      fallback: 'Lo siento cariño, el servicio de lectura de IA no está disponible temporalmente. Inténtalo de nuevo más tarde, ¿vale? ¡Estamos trabajando duro para brindarte la mejor experiencia de lectura de tarot! ✨'
    }
  },
  'fr': {
    name: 'Français',
    systemPrompt: 'Tu es une tarologue féminine chaleureuse, bavardant comme une amie intime. Veuillez fournir des lectures de tarot avec un ton amical et naturel.',
    friendTone: {
      endearments: ['chérie', 'ma belle', 'mon cœur'],
      particles: ['tu sais', 'hein', 'quoi'],
      empathy: ['je comprends', 'je te sens', 'je sais'],
      reactions: ['haha', 'oh là là', 'ah bon'],
      encouragement: ['essaie', 'fais-moi confiance', 'tu peux le faire']
    },
    responses: {
      fallback: 'Désolée chérie, le service de lecture IA est temporairement indisponible. Réessaie plus tard, d\'accord? Nous travaillons dur pour t\'offrir la meilleure expérience de lecture de tarot! ✨'
    }
  }
}

// 获取语言配置
function getLanguageConfig(userLanguage?: string) {
  // 修复语言匹配：支持 en-US -> en, zh-CN -> zh 等格式
  const language = userLanguage ? userLanguage.split('-')[0] : 'en'
  return LANGUAGE_CONFIG[language as keyof typeof LANGUAGE_CONFIG] || LANGUAGE_CONFIG['en']
}

// 获取专家名称
function getSpecialistName(specialist: string, userLanguage?: string): string {
  const isZh = userLanguage === 'zh' || userLanguage === 'zh-CN'
  
  const names: Record<string, { zh: string; en: string }> = {
    'TarotSpecialist.loveExpert': { zh: '情感关系专家', en: 'Love Relationship Expert' },
    'TarotSpecialist.careerExpert': { zh: '事业发展专家', en: 'Career Development Expert' },
    'TarotSpecialist.wealthExpert': { zh: '财富丰盛专家', en: 'Wealth Abundance Expert' },
    'TarotSpecialist.healthExpert': { zh: '身心健康专家', en: 'Health & Wellness Expert' },
    'TarotSpecialist.relationshipExpert': { zh: '人际关系专家', en: 'Interpersonal Relationship Expert' },
    'TarotSpecialist.decisionExpert': { zh: '决策指导专家', en: 'Decision Guidance Expert' },
    'TarotSpecialist.blockRemover': { zh: '心理卡点疗愈师', en: 'Psychological Block Healer' },
    'TarotSpecialist.generalExpert': { zh: '综合塔罗专家', en: 'General Tarot Expert' },
  }
  
  const specialistData = names[specialist] || names['TarotSpecialist.generalExpert']
  return isZh ? specialistData.zh : specialistData.en
}



// DeepSeek API调用
async function callDeepSeekAPI(prompt: string, userLanguage?: string) {
  if (!DEEPSEEK_API_KEY) {
    throw new Error('DEEPSEEK_API_KEY 环境变量未设置')
  }

  const langConfig = getLanguageConfig(userLanguage)

  const requestBody = {
    model: "deepseek-chat",
    messages: [
      {
        role: "system",
        content: langConfig.systemPrompt
      },
      {
        role: "user",
        content: prompt
      }
    ],
    temperature: 0.7,
    max_tokens: 1000,
    stream: false
  }

  console.log('🔄 调用 DeepSeek API...')
  
  const startTime = Date.now()
  
  try {
    const response = await fetch(`${DEEPSEEK_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify(requestBody)
    })

    const endTime = Date.now()
    const responseTime = endTime - startTime

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ DeepSeek API 错误:', response.status, errorText)
      throw new Error(`DeepSeek API调用失败: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('DeepSeek API 返回空响应')
    }

    const aiResponse = data.choices[0].message.content
    const usage = data.usage || {}

    console.log('✅ DeepSeek API 调用成功')
    console.log(`📊 用量统计: ${JSON.stringify(usage)}`)
    console.log(`⏱️ 响应时间: ${responseTime}ms`)

    // Langfuse 配置缺失,跳过追踪
    console.log('⚠ Langfuse 配置缺失,跳过追踪')

    return {
      content: aiResponse,
      usage,
      responseTime
    }
  } catch (error) {
    console.error('❌ DeepSeek API 调用失败:', error)
    throw error
  }
}

// 构建塔罗解读提示词
function buildTarotPrompt(data: RequestData): string {
  const { question, cards, spreadType, requestType, userLanguage } = data
  const langConfig = getLanguageConfig(userLanguage)

  const cardInfo = cards.map((card, index) => {
    if (userLanguage === 'en') {
      const position = card.isReversed ? 'Reversed' : 'Upright'
      const keywords = Array.isArray(card.keywords) ? card.keywords.join(', ') : card.keywords
      return `${index + 1}. ${card.name} (${position}) - ${card.meaning}`
    } else {
      const position = card.isReversed ? '逆位' : '正位'
      const keywords = Array.isArray(card.keywords) ? card.keywords.join(', ') : card.keywords
      return `${index + 1}. ${card.name}（${position}）- ${card.meaning}`
    }
  }).join('\n')

  // 简化版初始解读 - 支持多种请求类型
  if (requestType === 'simple_initial_reading' || requestType === 'initial_reading') {
    const endearments = langConfig.friendTone.endearments.join(', ')
    const particles = langConfig.friendTone.particles.join(', ')
    const empathy = langConfig.friendTone.empathy.join(', ')

    if (userLanguage === 'zh') {
      return `你是女神级闺蜜，既有魅力又有智慧。

用户问题："${question}"
卡牌：${cardInfo}

回复要求：
- 开头自然多样化：偶尔用"哎呀"、"哇"、"嗯"、"亲爱的"或直接开始，避免每次都用"宝贝"
- 提到"能量"、"直觉"、"感受"等词汇
- 2-3句话，50字以内
- 语气亲切自然，不刻意
- 最后问1个简短问题

直接回复，不要解释。`
    } else {
      return `You are a goddess-level best friend with charm and wisdom.

User's question: "${question}"
Cards: ${cardInfo}

Requirements:
- Start with "Babe!"
- Mention "energy" or "intuition"
- 2-3 sentences, under 50 words
- End with 1 short question

Reply directly, no explanations.`
    }
  }

  // 简化版追问解读 - 心理开解 - 支持多种请求类型
  if (requestType === 'simple_follow_up_reading' || requestType === 'follow_up') {
    const previousReading = data.previousReading || ''
    const userMessage = data.userMessage || ''
    const conversationHistory = data.conversationHistory || []
    const conversationRound = Math.floor(conversationHistory.length / 2) + 1

    const empathy = langConfig.friendTone.empathy.join(', ')
    const encouragement = langConfig.friendTone.encouragement.join(', ')
    const manifestationSuggestion = conversationRound >= 6 ?
      (userLanguage === 'zh' ? '如果合适，可以自然提及app的正念显化功能，比如"你也可以试试我们app里的正念训练"。' :
       'If appropriate, naturally mention the app\'s mindfulness manifestation feature, like "you could also try our mindfulness training in the app".') : ''

    if (userLanguage === 'zh') {
      return `你是女神级闺蜜。基于这些卡牌回复：
${cardInfo}

用户说："${userMessage}"

要求：
- 开头自然多样化：可以用"哎呀"、"嗯"、"我懂"、"亲爱的"或直接开始
- 提到卡牌和"能量"、"感受"
- 2句话，40字以内
- 语气温暖但不刻意
- 问1个简短问题

直接回复。`
    } else {
      return `You are goddess-level best friend. Reply based on these cards:
${cardInfo}

User said: "${userMessage}"

Requirements:
- Start with "Babe"
- Mention cards and "energy"
- 2 sentences, under 40 words
- Ask 1 short question

Reply directly.`
    }
  }

  // 删除复杂的专家解读，统一使用简洁风格

  // 删除复杂的简短解读，统一使用简洁风格

  // 删除复杂的后续对话，统一使用简洁风格

  // 默认fallback - 使用初始解读格式
  if (userLanguage === 'zh') {
    return `你是女神级闺蜜，既有魅力又有智慧。

用户问题："${question}"
卡牌：${cardInfo}

回复要求：
- 开头自然多样化：偶尔用"哎呀"、"哇"、"嗯"、"亲爱的"或直接开始
- 提到"能量"、"直觉"、"感受"等词汇
- 2-3句话，50字以内
- 语气亲切自然，不刻意
- 最后问1个简短问题

直接回复，不要解释。`
  } else {
    return `You are a goddess-level best friend with charm and wisdom.

User's question: "${question}"
Cards: ${cardInfo}

Requirements:
- Start with "Babe!"
- Mention "energy" or "intuition"
- 2-3 sentences, under 50 words
- End with 1 short question

Reply directly, no explanations.`
  }
}

serve(async (req) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, GET, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })
  }

  try {
    const data: RequestData = await req.json()
    console.log('📝 收到请求:', {
      requestType: data.requestType,
      question: data.question?.substring(0, 50) + '...',
      cardsCount: data.cards?.length,
      userLanguage: data.userLanguage
    })

    // 检查API密钥
    if (!DEEPSEEK_API_KEY) {
      console.error('❌ DEEPSEEK_API_KEY 未设置')
      const langConfig = getLanguageConfig(data.userLanguage)
      return new Response(JSON.stringify({
        success: false,
        error: 'API密钥未配置',
        reading: langConfig.responses.fallback
      }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    // Langfuse 配置缺失,跳过追踪
    console.log('⚠ Langfuse 配置缺失,跳过追踪')
    const traceId = null

    // 构建提示词
    const prompt = buildTarotPrompt(data)
    console.log('🔮 提示词长度:', prompt.length)

    // 调用 DeepSeek API
    const aiResult = await callDeepSeekAPI(prompt, data.userLanguage || 'en')

    const response = {
      success: true,
      reading: aiResult.content,
      traceId: traceId,
      usage: aiResult.usage,
      responseTime: aiResult.responseTime
    }

    console.log('✅ 成功生成解读，长度:', aiResult.content.length)

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ Edge Function 错误:', error)

    const langConfig = getLanguageConfig()
    const errorResponse = {
      success: false,
      error: error.message || 'Unknown error',
      reading: langConfig.responses.fallback
    }

    return new Response(JSON.stringify(errorResponse), {
      status: 200, // 仍然返回200，让客户端处理错误
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
}) 