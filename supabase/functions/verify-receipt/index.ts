import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

interface ReceiptData {
  receiptData: string
  password?: string // App Store Connect共享密钥（可选）
}

interface VerifyReceiptResponse {
  success: boolean
  status?: number
  receipt?: any
  latest_receipt_info?: any[]
  pending_renewal_info?: any[]
  error?: string
  environment?: 'production' | 'sandbox'
}

// App Store收据验证URL
const PRODUCTION_URL = 'https://buy.itunes.apple.com/verifyReceipt'
const SANDBOX_URL = 'https://sandbox.itunes.apple.com/verifyReceipt'

async function verifyReceiptWithApple(receiptData: string, url: string, password?: string): Promise<any> {
  const requestBody: any = {
    'receipt-data': receiptData
  }
  
  // 如果提供了共享密钥，添加到请求中（用于自动续订订阅）
  if (password) {
    requestBody.password = password
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

serve(async (req) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Method not allowed' 
    }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }

  try {
    const { receiptData, password }: ReceiptData = await req.json()

    if (!receiptData) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Receipt data is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    console.log('🔍 开始验证收据...')

    // 步骤1: 首先尝试生产环境验证
    let verificationResult: any
    let environment: 'production' | 'sandbox' = 'production'

    try {
      console.log('📱 尝试生产环境验证...')
      verificationResult = await verifyReceiptWithApple(receiptData, PRODUCTION_URL, password)
      
      // 检查是否是沙盒收据在生产环境的错误
      if (verificationResult.status === 21007) {
        console.log('🧪 检测到沙盒收据，切换到沙盒环境验证...')
        verificationResult = await verifyReceiptWithApple(receiptData, SANDBOX_URL, password)
        environment = 'sandbox'
      }
    } catch (error) {
      console.error('❌ 生产环境验证失败:', error)
      // 如果生产环境失败，尝试沙盒环境
      console.log('🧪 回退到沙盒环境验证...')
      try {
        verificationResult = await verifyReceiptWithApple(receiptData, SANDBOX_URL, password)
        environment = 'sandbox'
      } catch (sandboxError) {
        console.error('❌ 沙盒环境验证也失败:', sandboxError)
        throw sandboxError
      }
    }

    console.log(`✅ 收据验证完成 - 环境: ${environment}, 状态: ${verificationResult.status}`)

    // 检查验证结果状态
    const response: VerifyReceiptResponse = {
      success: verificationResult.status === 0,
      status: verificationResult.status,
      environment: environment
    }

    if (verificationResult.status === 0) {
      // 验证成功
      response.receipt = verificationResult.receipt
      response.latest_receipt_info = verificationResult.latest_receipt_info
      response.pending_renewal_info = verificationResult.pending_renewal_info
    } else {
      // 验证失败，返回错误信息
      const errorMessages: { [key: number]: string } = {
        21000: 'App Store无法读取提供的JSON对象',
        21002: 'receipt-data属性中的数据格式错误或缺失',
        21003: '收据无法验证',
        21004: '提供的共享密钥与账户文件中的共享密钥不匹配',
        21005: '收据服务器暂时无法提供收据',
        21006: '此收据有效，但订阅已过期',
        21007: '此收据来自沙盒环境，但发送到了生产环境进行验证',
        21008: '此收据来自生产环境，但发送到了沙盒环境进行验证',
        21010: '此收据无法验证，请重新验证',
      }
      
      response.error = errorMessages[verificationResult.status] || `未知错误 (状态码: ${verificationResult.status})`
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ 收据验证异常:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message || '收据验证失败'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
})
