import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface DiaryEmbeddingRequest {
  diary_id: string
  content: string
  user_id: string
}

serve(async (req) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Method not allowed' 
    }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }

  try {
    const { diary_id, content, user_id }: DiaryEmbeddingRequest = await req.json()

    if (!diary_id || !content || !user_id) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Missing required fields'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    console.log('🔍 开始处理日记embedding...')

    // 1. 内容预处理和匿名化
    const processedContent = await preprocessDiaryContent(content)
    
    // 2. 生成embedding
    const embedding = await generateEmbedding(processedContent.summary)
    
    // 3. 提取关键词
    const keywords = await extractKeywords(content)
    
    // 4. 存储到数据库
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { data, error } = await supabase
      .from('diary_embeddings')
      .insert({
        diary_id,
        user_id,
        content_summary: processedContent.summary,
        embedding,
        emotion_keywords: keywords.emotions,
        growth_keywords: keywords.growth,
      })

    if (error) {
      console.error('❌ 数据库插入错误:', error)
      throw error
    }

    // 5. 更新高我记忆
    await updateHigherSelfMemories(supabase, user_id, content, keywords)

    console.log('✅ 日记embedding处理完成')

    return new Response(JSON.stringify({
      success: true,
      data: {
        embedding_id: data[0]?.id,
        keywords: keywords,
        summary: processedContent.summary
      }
    }), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ 日记embedding处理失败:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message || '处理失败'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
})

async function preprocessDiaryContent(content: string) {
  // 移除敏感信息，生成摘要
  const summary = content
    .replace(/\b\d{11}\b/g, '[手机号]') // 手机号
    .replace(/\b\d{4}-\d{4}-\d{4}-\d{4}\b/g, '[银行卡]') // 银行卡
    .replace(/[\u4e00-\u9fa5]{2,3}(?=先生|女士|老师)/g, '[姓名]') // 中文姓名
    .substring(0, 500) // 限制长度

  return {
    summary,
    sensitive_removed: content.length !== summary.length
  }
}

async function generateEmbedding(text: string): Promise<number[]> {
  try {
    // 调用OpenAI Embedding API
    const response = await fetch('https://api.openai.com/v1/embeddings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${Deno.env.get('OPENAI_API_KEY')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        input: text,
        model: 'text-embedding-ada-002',
      }),
    })

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`)
    }

    const data = await response.json()
    return data.data[0].embedding
  } catch (error) {
    console.error('❌ Embedding生成失败:', error)
    // 返回零向量作为fallback
    return new Array(1536).fill(0)
  }
}

async function extractKeywords(content: string) {
  // 情感关键词
  const emotionPatterns = {
    happy: ['开心', '快乐', '高兴', '兴奋', '满足', '幸福'],
    sad: ['难过', '伤心', '沮丧', '失落', '痛苦'],
    anxious: ['焦虑', '担心', '紧张', '不安', '害怕'],
    angry: ['生气', '愤怒', '恼火', '烦躁'],
    grateful: ['感谢', '感恩', '感激', '庆幸'],
    proud: ['骄傲', '自豪', '成就感', '满意']
  }

  // 成长关键词
  const growthPatterns = {
    learning: ['学习', '学会', '掌握', '理解', '领悟'],
    progress: ['进步', '提升', '改善', '成长', '发展'],
    challenge: ['挑战', '困难', '克服', '突破', '坚持'],
    reflection: ['反思', '思考', '总结', '感悟', '体会']
  }

  const emotions: string[] = []
  const growth: string[] = []

  // 检测情感关键词
  for (const [emotion, patterns] of Object.entries(emotionPatterns)) {
    if (patterns.some(pattern => content.includes(pattern))) {
      emotions.push(emotion)
    }
  }

  // 检测成长关键词
  for (const [growthType, patterns] of Object.entries(growthPatterns)) {
    if (patterns.some(pattern => content.includes(pattern))) {
      growth.push(growthType)
    }
  }

  return { emotions, growth }
}

async function updateHigherSelfMemories(supabase: any, userId: string, content: string, keywords: any) {
  // 基于日记内容更新高我对用户的记忆
  const memories = []

  // 如果包含成长关键词，记录成长模式
  if (keywords.growth.length > 0) {
    memories.push({
      user_id: userId,
      memory_type: 'growth',
      content: `用户在${keywords.growth.join('、')}方面有所体现`,
      confidence_score: 0.7
    })
  }

  // 如果包含积极情感，记录优势
  const positiveEmotions = keywords.emotions.filter((e: string) => 
    ['happy', 'grateful', 'proud'].includes(e))
  
  if (positiveEmotions.length > 0) {
    memories.push({
      user_id: userId,
      memory_type: 'strength',
      content: `用户具有积极的情感表达能力，经常体验${positiveEmotions.join('、')}`,
      confidence_score: 0.6
    })
  }

  // 批量插入记忆
  if (memories.length > 0) {
    await supabase
      .from('higher_self_memories')
      .insert(memories)
  }
}
