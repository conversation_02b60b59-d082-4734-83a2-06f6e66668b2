-- 创建日记表
CREATE TABLE IF NOT EXISTS diary_entries (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  mood_score INTEGER CHECK (mood_score >= 1 AND mood_score <= 10),
  tags TEXT[], -- 情感标签：开心、焦虑、成长等
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建日记向量表（用于语义搜索）
CREATE TABLE IF NOT EXISTS diary_embeddings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  diary_id UUID REFERENCES diary_entries(id) ON DELETE CASCADE,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  content_summary TEXT, -- 内容摘要（去除敏感信息）
  embedding vector(1536), -- OpenAI embedding维度
  emotion_keywords TEXT[], -- 情感关键词
  growth_keywords TEXT[], -- 成长关键词
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建高我记忆表（存储高我对用户的了解）
CREATE TABLE IF NOT EXISTS higher_self_memories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  memory_type TEXT NOT NULL, -- 'strength', 'growth', 'pattern', 'concern'
  content TEXT NOT NULL,
  confidence_score FLOAT DEFAULT 0.5, -- 0-1，记忆的可信度
  source_diary_ids UUID[], -- 来源日记ID
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 启用向量搜索扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建向量索引
CREATE INDEX IF NOT EXISTS diary_embeddings_vector_idx 
ON diary_embeddings USING ivfflat (embedding vector_cosine_ops);

-- 创建RLS策略
ALTER TABLE diary_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE diary_embeddings ENABLE ROW LEVEL SECURITY;
ALTER TABLE higher_self_memories ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can only access their own diary entries" 
ON diary_entries FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own diary embeddings" 
ON diary_embeddings FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Users can only access their own higher self memories" 
ON higher_self_memories FOR ALL USING (auth.uid() = user_id);

-- 创建触发器自动更新时间戳
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_diary_entries_updated_at 
BEFORE UPDATE ON diary_entries 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_higher_self_memories_updated_at 
BEFORE UPDATE ON higher_self_memories 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
