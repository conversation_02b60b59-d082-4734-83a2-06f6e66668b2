-- 创建相似日记搜索函数
CREATE OR REPLACE FUNCTION search_similar_diaries(
  query_embedding vector(1536),
  target_user_id uuid,
  match_threshold float DEFAULT 0.7,
  match_count int DEFAULT 5
)
RETURNS TABLE (
  id uuid,
  user_id uuid,
  content text,
  mood_score int,
  tags text[],
  created_at timestamptz,
  updated_at timestamptz,
  similarity float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    de.id,
    de.user_id,
    de.content,
    de.mood_score,
    de.tags,
    de.created_at,
    de.updated_at,
    (1 - (dee.embedding <=> query_embedding)) as similarity
  FROM diary_entries de
  JOIN diary_embeddings dee ON de.id = dee.diary_id
  WHERE
    de.user_id = target_user_id
    AND (1 - (dee.embedding <=> query_embedding)) > match_threshold
  ORDER BY dee.embedding <=> query_embedding
  LIMIT match_count;
END;
$$;

-- 创建用户成长轨迹分析函数
CREATE OR REPLACE FUNCTION analyze_user_growth_pattern(
  target_user_id uuid,
  days_back int DEFAULT 30
)
RETURNS TABLE (
  growth_type text,
  frequency int,
  latest_date timestamptz,
  confidence_score float
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT
    unnest(dee.growth_keywords) as growth_type,
    COUNT(*)::int as frequency,
    MAX(de.created_at) as latest_date,
    AVG(CASE
      WHEN de.mood_score IS NOT NULL THEN de.mood_score::float / 10.0
      ELSE 0.5
    END) as confidence_score
  FROM diary_entries de
  JOIN diary_embeddings dee ON de.id = dee.diary_id
  WHERE
    de.user_id = target_user_id
    AND de.created_at >= NOW() - INTERVAL '1 day' * days_back
    AND array_length(dee.growth_keywords, 1) > 0
  GROUP BY unnest(dee.growth_keywords)
  HAVING COUNT(*) >= 2
  ORDER BY frequency DESC, confidence_score DESC;
END;
$$;

-- 创建情感模式分析函数
CREATE OR REPLACE FUNCTION analyze_emotion_patterns(
  target_user_id uuid,
  days_back int DEFAULT 30
)
RETURNS TABLE (
  emotion_type text,
  frequency int,
  avg_mood_score float,
  trend text
)
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  WITH emotion_data AS (
    SELECT
      unnest(dee.emotion_keywords) as emotion,
      de.mood_score,
      de.created_at,
      ROW_NUMBER() OVER (PARTITION BY unnest(dee.emotion_keywords) ORDER BY de.created_at) as rn,
      COUNT(*) OVER (PARTITION BY unnest(dee.emotion_keywords)) as total_count
    FROM diary_entries de
    JOIN diary_embeddings dee ON de.id = dee.diary_id
    WHERE
      de.user_id = target_user_id
      AND de.created_at >= NOW() - INTERVAL '1 day' * days_back
      AND array_length(dee.emotion_keywords, 1) > 0
  ),
  emotion_stats AS (
    SELECT 
      emotion,
      COUNT(*)::int as frequency,
      AVG(mood_score)::float as avg_mood_score,
      -- 计算趋势：比较前半段和后半段的平均心情
      AVG(CASE WHEN rn <= total_count / 2 THEN mood_score END) as early_avg,
      AVG(CASE WHEN rn > total_count / 2 THEN mood_score END) as late_avg
    FROM emotion_data
    WHERE mood_score IS NOT NULL
    GROUP BY emotion
    HAVING COUNT(*) >= 2
  )
  SELECT 
    emotion as emotion_type,
    frequency,
    avg_mood_score,
    CASE 
      WHEN late_avg > early_avg + 1 THEN 'improving'
      WHEN late_avg < early_avg - 1 THEN 'declining'
      ELSE 'stable'
    END as trend
  FROM emotion_stats
  ORDER BY frequency DESC, avg_mood_score DESC;
END;
$$;

-- 创建高我洞察生成函数
CREATE OR REPLACE FUNCTION generate_higher_self_insights(
  target_user_id uuid
)
RETURNS TABLE (
  insight_type text,
  content text,
  confidence_score float,
  supporting_evidence text[]
)
LANGUAGE plpgsql
AS $$
DECLARE
  growth_patterns record;
  emotion_patterns record;
  insights text[];
BEGIN
  -- 分析成长模式
  FOR growth_patterns IN
    SELECT * FROM analyze_user_growth_pattern(target_user_id, 60)
  LOOP
    IF growth_patterns.frequency >= 3 AND growth_patterns.confidence_score > 0.6 THEN
      RETURN QUERY SELECT 
        'growth'::text,
        format('你在%s方面展现出持续的成长，这是你的一个重要优势', growth_patterns.growth_type),
        growth_patterns.confidence_score,
        ARRAY[format('在过去60天中有%s次相关记录', growth_patterns.frequency)]::text[];
    END IF;
  END LOOP;

  -- 分析情感模式
  FOR emotion_patterns IN
    SELECT * FROM analyze_emotion_patterns(target_user_id, 60)
  LOOP
    IF emotion_patterns.frequency >= 3 THEN
      IF emotion_patterns.trend = 'improving' THEN
        RETURN QUERY SELECT 
          'emotional_growth'::text,
          format('你在%s方面的情感状态正在改善，这显示了你的内在力量', emotion_patterns.emotion_type),
          emotion_patterns.avg_mood_score / 10.0,
          ARRAY[format('平均心情评分%.1f，趋势向好', emotion_patterns.avg_mood_score)]::text[];
      ELSIF emotion_patterns.avg_mood_score >= 7.0 THEN
        RETURN QUERY SELECT 
          'strength'::text,
          format('你在%s方面保持着积极的状态，这是你的天然优势', emotion_patterns.emotion_type),
          emotion_patterns.avg_mood_score / 10.0,
          ARRAY[format('平均心情评分%.1f', emotion_patterns.avg_mood_score)]::text[];
      END IF;
    END IF;
  END LOOP;

  RETURN;
END;
$$;
