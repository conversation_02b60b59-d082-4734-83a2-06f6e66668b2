import 'package:flutter/material.dart';
import 'package:ai_tarot_reading/services/ai_tarot_specialists.dart';
import 'package:ai_tarot_reading/services/ai_emotion_analyst.dart';
import 'package:ai_tarot_reading/models/tarot_card.dart';
import 'package:ai_tarot_reading/data/tarot_cards_data.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:ai_tarot_reading/config/supabase_config.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // 初始化Supabase
  await Supabase.initialize(
    url: SupabaseConfig.supabaseUrl,
    anonKey: SupabaseConfig.supabaseAnonKey,
  );
  
  print('🧪 开始测试AI解读功能...');
  
  // 测试1: 情感分析
  await testEmotionAnalysis();
  
  // 测试2: 专家解读
  await testSpecialistReading();
  
  print('✅ 测试完成');
}

Future<void> testEmotionAnalysis() async {
  print('\n📊 测试情感分析...');
  
  try {
    final analysis = await AIEmotionAnalyst.analyzeUserQuestion(
      currentQuestion: '我和男朋友最近总是吵架，我们的关系还有希望吗？',
      questionHistory: [],
      userLanguage: 'zh',
    );
    
    print('✅ 情感分析成功:');
    print('   - 问题类型: ${analysis.questionType}');
    print('   - 情感强度: ${analysis.emotionIntensity}');
    print('   - 心理卡点: ${analysis.psychologicalBlock}');
    print('   - 分析原因: ${analysis.analysisReason}');
    
  } catch (e) {
    print('❌ 情感分析失败: $e');
  }
}

Future<void> testSpecialistReading() async {
  print('\n🔮 测试专家解读...');
  
  try {
    // 模拟情感分析结果
    final analysis = EmotionAnalysisResult(
      questionType: QuestionType.love,
      emotionIntensity: EmotionIntensity.high,
      repeatPattern: RepeatPattern.none,
      psychologicalBlock: PsychologicalBlock.anxiety,
      urgencyLevel: UrgencyLevel.medium,
      analysisReason: '用户对感情关系表现出高度关注和焦虑',
      recommendedApproach: '需要情感专家的深度引导',
    );
    
    // 获取测试卡牌
    final cards = TarotCardsData.getRandomCards(1);
    
    final result = await AITarotSpecialists.getSpecialistReading(
      analysis: analysis,
      question: '我和男朋友最近总是吵架，我们的关系还有希望吗？',
      cards: cards,
      userLanguage: 'zh',
    );
    
    print('✅ 专家解读成功:');
    print('   - 专家类型: ${result.specialist}');
    print('   - 解读长度: ${result.reading.length}字');
    print('   - 显化任务数量: ${result.manifestationTasks.length}');
    print('   - 解读内容预览: ${result.reading.substring(0, 100)}...');
    
  } catch (e) {
    print('❌ 专家解读失败: $e');
  }
}
