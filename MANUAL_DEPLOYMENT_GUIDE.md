# 🚀 手动部署收据验证函数指南

## 📋 前提条件

由于本地环境没有Docker，我们需要通过Supabase Dashboard手动部署Edge Function。

## 🔧 方法1: 通过Supabase Dashboard部署

### 步骤1: 登录Supabase Dashboard
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 登录你的账号
3. 选择项目：`ktqlxbcauxomczubqasp`

### 步骤2: 创建Edge Function
1. 在左侧菜单中点击 **Edge Functions**
2. 点击 **Create a new function**
3. 函数名称：`verify-receipt`
4. 将以下代码复制到编辑器中：

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

interface ReceiptData {
  receiptData: string
  password?: string // App Store Connect共享密钥（可选）
}

interface VerifyReceiptResponse {
  success: boolean
  status?: number
  receipt?: any
  latest_receipt_info?: any[]
  pending_renewal_info?: any[]
  error?: string
  environment?: 'production' | 'sandbox'
}

// App Store收据验证URL
const PRODUCTION_URL = 'https://buy.itunes.apple.com/verifyReceipt'
const SANDBOX_URL = 'https://sandbox.itunes.apple.com/verifyReceipt'

async function verifyReceiptWithApple(receiptData: string, url: string, password?: string): Promise<any> {
  const requestBody: any = {
    'receipt-data': receiptData
  }
  
  // 如果提供了共享密钥，添加到请求中（用于自动续订订阅）
  if (password) {
    requestBody.password = password
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestBody),
  })

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`)
  }

  return await response.json()
}

serve(async (req) => {
  // 处理 CORS
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    })
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Method not allowed' 
    }), {
      status: 405,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }

  try {
    const { receiptData, password }: ReceiptData = await req.json()

    if (!receiptData) {
      return new Response(JSON.stringify({
        success: false,
        error: 'Receipt data is required'
      }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
        },
      })
    }

    console.log('🔍 开始验证收据...')

    // 步骤1: 首先尝试生产环境验证
    let verificationResult: any
    let environment: 'production' | 'sandbox' = 'production'

    try {
      console.log('📱 尝试生产环境验证...')
      verificationResult = await verifyReceiptWithApple(receiptData, PRODUCTION_URL, password)
      
      // 检查是否是沙盒收据在生产环境的错误
      if (verificationResult.status === 21007) {
        console.log('🧪 检测到沙盒收据，切换到沙盒环境验证...')
        verificationResult = await verifyReceiptWithApple(receiptData, SANDBOX_URL, password)
        environment = 'sandbox'
      }
    } catch (error) {
      console.error('❌ 生产环境验证失败:', error)
      // 如果生产环境失败，尝试沙盒环境
      console.log('🧪 回退到沙盒环境验证...')
      try {
        verificationResult = await verifyReceiptWithApple(receiptData, SANDBOX_URL, password)
        environment = 'sandbox'
      } catch (sandboxError) {
        console.error('❌ 沙盒环境验证也失败:', sandboxError)
        throw sandboxError
      }
    }

    console.log(`✅ 收据验证完成 - 环境: ${environment}, 状态: ${verificationResult.status}`)

    // 检查验证结果状态
    const response: VerifyReceiptResponse = {
      success: verificationResult.status === 0,
      status: verificationResult.status,
      environment: environment
    }

    if (verificationResult.status === 0) {
      // 验证成功
      response.receipt = verificationResult.receipt
      response.latest_receipt_info = verificationResult.latest_receipt_info
      response.pending_renewal_info = verificationResult.pending_renewal_info
    } else {
      // 验证失败，返回错误信息
      const errorMessages: { [key: number]: string } = {
        21000: 'App Store无法读取提供的JSON对象',
        21002: 'receipt-data属性中的数据格式错误或缺失',
        21003: '收据无法验证',
        21004: '提供的共享密钥与账户文件中的共享密钥不匹配',
        21005: '收据服务器暂时无法提供收据',
        21006: '此收据有效，但订阅已过期',
        21007: '此收据来自沙盒环境，但发送到了生产环境进行验证',
        21008: '此收据来自生产环境，但发送到了沙盒环境进行验证',
        21010: '此收据无法验证，请重新验证',
      }
      
      response.error = errorMessages[verificationResult.status] || `未知错误 (状态码: ${verificationResult.status})`
    }

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })

  } catch (error) {
    console.error('❌ 收据验证异常:', error)
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message || '收据验证失败'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
    })
  }
})
```

### 步骤3: 部署函数
1. 点击 **Deploy function**
2. 等待部署完成
3. 记录函数URL：`https://ktqlxbcauxomczubqasp.supabase.co/functions/v1/verify-receipt`

## 🔧 方法2: 安装Docker后使用CLI部署

如果你想使用命令行部署：

### 步骤1: 安装Docker Desktop
1. 访问 [Docker Desktop](https://www.docker.com/products/docker-desktop/)
2. 下载并安装适合你系统的版本
3. 启动Docker Desktop

### 步骤2: 安装Supabase CLI
```bash
npm install -g supabase
```

### 步骤3: 登录并部署
```bash
# 登录Supabase
supabase login

# 部署函数
supabase functions deploy verify-receipt --project-ref ktqlxbcauxomczubqasp
```

## 🧪 验证部署

### 测试函数是否正常工作：
```bash
curl -X POST 'https://ktqlxbcauxomczubqasp.supabase.co/functions/v1/verify-receipt' \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0cWx4YmNhdXhvbWN6dWJxYXNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Mjc5OTksImV4cCI6MjA2MTQwMzk5OX0.BKL7B2Mbpl1sOsPI5QsX6EmbmHZu_RWPSh8knHkUEno' \
  -d '{"receiptData": "test"}'
```

预期响应：
```json
{
  "success": false,
  "error": "Receipt data is required"
}
```

## ✅ 完成后的下一步

1. **测试应用内购买**：
   - 在iOS设备上测试购买流程
   - 检查收据验证是否正常工作
   - 验证订阅状态更新

2. **检查日志**：
   - 在Supabase Dashboard的Edge Functions页面查看日志
   - 确认收据验证过程正常

3. **提交审核**：
   - 确保所有测试通过
   - 重新提交App Store审核

## 🔍 故障排除

如果函数部署失败：
1. 检查代码语法是否正确
2. 确认Supabase项目权限
3. 查看部署日志中的错误信息
4. 联系Supabase支持团队
