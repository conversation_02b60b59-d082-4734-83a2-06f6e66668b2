# DeepSeek API 配置指南

## 🚀 DeepSeek API 设置步骤

### 1. 获取 DeepSeek API 密钥

1. 访问 [DeepSeek 官网](https://platform.deepseek.com/)
2. 注册账户并登录
3. 进入控制台，点击 **API Keys** 
4. 创建新的 API 密钥
5. 复制生成的 API 密钥（格式通常是 `sk-xxxxxxxxxx`）

### 2. 在 Supabase 中设置环境变量

1. 登录 [Supabase Dashboard](https://supabase.com/dashboard)
2. 选择你的项目
3. 进入 **Settings** → **Edge Functions**
4. 在 **Environment Variables** 部分添加：
   - **Variable Name**: `DEEPSEEK_API_KEY`
   - **Value**: 你的 DeepSeek API 密钥

### 3. 验证配置

部署完成后，可以通过以下方式测试：

```dart
// 在Flutter应用中测试AI解读功能
final aiReading = await AITarotService.generateTarotReading(
  question: "测试问题",
  cards: [testCard],
  spreadType: "单张牌",
);
```

## 💰 API 费用说明

DeepSeek API 的优势：
- **成本低廉**: 比 OpenAI GPT 便宜很多
- **中文优化**: 专门针对中文对话优化
- **高质量**: 提供专业的对话质量
- **快速响应**: API 响应速度快

### 计费方式
- 按 Token 计费
- 输入和输出分别计费
- 具体价格请查看 [DeepSeek 定价页面](https://platform.deepseek.com/pricing)

## 🔧 高级配置

如果需要调整 AI 行为，可以修改 `supabase/functions/deepseek-tarot-reading/index.ts` 中的参数：

```typescript
// 在 callDeepSeekAPI 函数中调整
{
  model: 'deepseek-chat',
  max_tokens: 1000,      // 最大输出长度
  temperature: 0.7,      // 创造性 (0-1)
  top_p: 0.9,           // 核采样参数
}
```

## 🎯 模型选择

DeepSeek 提供多个模型：
- **deepseek-chat**: 通用对话模型（推荐）
- **deepseek-coder**: 代码生成模型
- **deepseek-reasoning**: 推理能力强的模型

## ⚠️ 注意事项

1. **API 密钥安全**: 
   - 永远不要在客户端代码中暴露 API 密钥
   - 使用 Supabase Edge Functions 保护密钥

2. **错误处理**: 
   - 系统已内置 fallback 机制
   - API 调用失败时会使用本地解读

3. **使用限制**: 
   - 注意 DeepSeek API 的使用限制
   - 建议设置合理的 rate limiting

## 🛠️ 故障排除

### 常见问题

1. **"API密钥未设置"错误**
   - 检查 Supabase 环境变量是否正确设置
   - 确认变量名为 `DEEPSEEK_API_KEY`

2. **"API调用失败"错误**
   - 检查 API 密钥是否有效
   - 确认账户余额充足
   - 检查网络连接

3. **回复质量不佳**
   - 调整 temperature 参数（0.7-0.9）
   - 优化 system prompt
   - 考虑使用 deepseek-reasoning 模型

### 查看日志

在 Supabase Dashboard 中：
1. 进入 **Edge Functions** 
2. 选择 `deepseek-tarot-reading` 函数
3. 查看 **Logs** 了解详细错误信息

## 📈 性能优化建议

1. **缓存策略**: 考虑对相同问题和牌组合进行缓存
2. **批量处理**: 对多个请求进行批量处理
3. **超时设置**: 设置合理的超时时间
4. **降级策略**: 始终准备 fallback 方案

---

配置完成后，你的 AI 塔罗占卜应用就可以使用 DeepSeek 的强大语言能力来提供专业的塔罗解读了！🔮✨ 