import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// 极简测试后端保存
class TestBackendSave extends StatefulWidget {
  const TestBackendSave({super.key});

  @override
  State<TestBackendSave> createState() => _TestBackendSaveState();
}

class _TestBackendSaveState extends State<TestBackendSave> {
  String _result = '点击测试后端保存';
  bool _isLoading = false;

  Future<void> _testSave() async {
    setState(() {
      _isLoading = true;
      _result = '正在测试...';
    });

    try {
      final supabase = Supabase.instance.client;

      // 检查当前登录状态
      final user = supabase.auth.currentUser;
      if (user == null) {
        throw Exception('请先在主页登录，然后再测试');
      }

      print('✅ 检测到已登录用户，用户ID: ${user.id}');

      // 测试数据 - 添加 user_id
      final testData = {
        'user_id': user.id,  // 添加用户ID
        'question': '测试问题',
        'spread_type': 'three',
        'cards': [
          {
            'id': '1',
            'name': '愚者',
            'description': '新的开始',
            'meaning': '代表新的开始',
            'keywords': ['新开始'],
            'imageUrl': '',
            'isMajorArcana': true,
            'isReversed': false,
          }
        ],
        'interpretation': '测试解读内容',
        'follow_up_questions': ['测试追问'],
        'follow_up_responses': ['测试回复'],
        'created_at': DateTime.now().toIso8601String(),
      };

      print('🧪 开始测试后端保存');
      print('📊 测试数据: $testData');

      // 直接保存到数据库
      await supabase.from('tarot_readings').insert(testData);

      setState(() {
        _result = '✅ 后端保存成功！';
      });
      print('✅ 后端保存测试成功');

    } catch (e) {
      setState(() {
        _result = '❌ 后端保存失败：$e';
      });
      print('❌ 后端保存失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试后端保存'),
        backgroundColor: Colors.red,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testSave,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                padding: const EdgeInsets.all(20),
              ),
              child: _isLoading
                  ? const CircularProgressIndicator(color: Colors.white)
                  : const Text(
                      '测试后端保存',
                      style: TextStyle(fontSize: 20, color: Colors.white),
                    ),
            ),
            const SizedBox(height: 20),
            Text(
              _result,
              style: const TextStyle(fontSize: 18),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  await Supabase.initialize(
    url: 'https://ktqlxbcauxomczubqasp.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imt0cWx4YmNhdXhvbWN6dWJxYXNwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDU4Mjc5OTksImV4cCI6MjA2MTQwMzk5OX0.BKL7B2Mbpl1sOsPI5QsX6EmbmHZu_RWPSh8knHkUEno',
  );
  
  runApp(MaterialApp(
    home: const TestBackendSave(),
    debugShowCheckedModeBanner: false,
  ));
}
