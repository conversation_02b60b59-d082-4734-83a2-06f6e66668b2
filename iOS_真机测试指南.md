# iOS 真机测试配置指南

## 🎯 目标
让AI塔罗占卜应用在你的iPhone 15 "soleil"上运行

## 📋 前提条件
- ✅ iPhone 15 已连接到Mac
- ✅ 拥有Apple ID
- ✅ Xcode已安装

## 🔧 配置步骤

### 步骤 1: 启用iPhone开发者模式

1. **在Mac上打开Xcode**
   ```bash
   open -a Xcode
   ```

2. **连接iPhone到Mac**
   - 使用USB线连接
   - iPhone上点击"信任此电脑"

3. **在Xcode中检测设备**
   - Xcode → Window → Devices and Simulators
   - 确保能看到你的iPhone

4. **在iPhone上启用开发者模式**
   - iPhone设置 → 隐私与安全性
   - 滚动到底部找到"开发者模式"
   - 开启开关并重启iPhone
   - 重启后确认开启

### 步骤 2: 配置iOS项目签名

1. **打开iOS项目**
   ```bash
   cd "/Users/<USER>/Desktop/AI Tarot Reading App with Figma UI"
   open ios/Runner.xcworkspace
   ```

2. **在Xcode中配置签名**
   - 选择左侧的"Runner"项目
   - 选择"Runner" Target
   - 进入"Signing & Capabilities"标签页
   - 勾选"Automatically manage signing"
   - Team选择你的Apple ID关联的开发团队
   - Bundle Identifier会自动生成

3. **解决可能的证书问题**
   - 如果提示证书问题，点击"Try Again"
   - 或者进入Xcode → Preferences → Accounts添加Apple ID

### 步骤 3: 运行到真机

1. **检查设备连接**
   ```bash
   flutter devices
   ```
   应该看到你的iPhone在列表中

2. **运行到真机**
   ```bash
   flutter run -d ********-00161CE62605A01E
   ```

3. **首次运行时的额外步骤**
   - 应用安装后可能无法启动
   - 在iPhone上：设置 → 通用 → VPN与设备管理
   - 找到你的开发者应用，点击"信任"

## 🚨 常见问题解决

### 问题1: "开发者模式"选项不存在
**解决方案**:
1. 确保iPhone通过USB连接（不是WiFi）
2. 在Xcode中打开Devices and Simulators
3. 选择你的设备，这会触发开发者模式出现

### 问题2: 签名错误
**解决方案**:
1. 在Xcode中清理项目：Product → Clean Build Folder
2. 确认Bundle Identifier是唯一的
3. 重新选择Team

### 问题3: 应用无法启动
**解决方案**:
1. iPhone设置 → 通用 → VPN与设备管理
2. 找到开发者应用并信任

### 问题4: Flutter无法识别设备
**解决方案**:
```bash
flutter doctor
flutter clean
flutter pub get
```

## 🎉 成功标志

看到以下输出表示成功：
```
Flutter run key commands.
r Hot reload. 🔥🔥🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).
```

## 🔄 快速命令参考

```bash
# 检查设备
flutter devices

# 运行到特定设备
flutter run -d ********-00161CE62605A01E

# 清理和重建
flutter clean && flutter pub get

# 检查Flutter环境
flutter doctor

# 查看iOS模拟器
flutter emulators
```

## 📱 测试DeepSeek AI功能

成功运行后，测试AI解读功能：

1. 选择占卜主题
2. 输入问题："我今天的运势如何？"
3. 选择塔罗牌
4. 等待AI解读（确保Supabase中已设置DEEPSEEK_API_KEY）
5. 测试后续对话功能

## ⚡ 热重载提示

真机运行时可以使用：
- `r` - 热重载（保持状态）
- `R` - 热重启（重置状态）
- `q` - 退出

---

完成这些步骤后，你就可以在真机上测试完整的AI塔罗占卜功能了！🎴✨ 