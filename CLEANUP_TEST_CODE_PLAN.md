# 🧹 测试代码清理计划

## 📋 **需要清理的项目**

### **🔴 高优先级 - 用户可见**

#### **1. 移除底部导航栏中的测试页面**
**文件**: `lib/screens/home_screen.dart`
**修改**: 将第2个导航项从 `TestMainPage()` 改为 `TarotReadingScreen()`

#### **2. 移除认证页面的调试面板**
**文件**: `lib/screens/auth_screen.dart`
**修改**: 
- 删除 `_buildDebugPanel()` 方法
- 删除 `_buildDebugToggleButton()` 方法
- 删除 `_showDebugPanel` 变量
- 删除调试相关的UI元素

#### **3. 移除调试边框**
**文件**: `lib/screens/home_screen.dart`
**修改**: 删除红色调试边框

### **🟡 中优先级 - 代码清理**

#### **4. 移除订阅服务中的测试方法**
**文件**: `lib/services/subscription_service.dart`
**修改**: 删除以下方法：
- `setTestSubscriptionTier()`
- `resetUsageForTesting()`
- `simulateUsageLimitReached()`
- `forceResetUsageForCurrentUser()`

#### **5. 删除根目录下的测试文件**
**文件**: 根目录下的所有 `test_*.dart` 文件
**操作**: 删除这些文件

#### **6. 删除测试相关的导入**
**文件**: 多个文件
**修改**: 删除对测试文件的导入

### **🟢 低优先级 - 优化**

#### **7. 清理调试日志**
**文件**: 多个文件
**修改**: 将 `debugPrint` 改为 `print` 或删除

## 🚀 **执行步骤**

### **步骤1: 修复底部导航栏**
1. 修改 `lib/screens/home_screen.dart`
2. 将 `TestMainPage()` 改为 `TarotReadingScreen()`
3. 更新导航标签文本

### **步骤2: 清理认证页面**
1. 删除调试面板相关代码
2. 保留核心登录功能
3. 简化错误处理

### **步骤3: 移除调试边框**
1. 删除红色调试边框
2. 保持UI美观

### **步骤4: 清理订阅服务**
1. 删除测试方法
2. 保留核心订阅功能
3. 清理调试日志

### **步骤5: 删除测试文件**
1. 删除根目录下的测试文件
2. 清理相关导入

## ✅ **验证清单**

- [ ] 底部导航栏不再显示测试页面
- [ ] 认证页面没有调试按钮
- [ ] 没有调试边框
- [ ] 订阅功能正常工作
- [ ] 没有测试文件残留
- [ ] 应用可以正常编译和运行

## 🎯 **预期结果**

清理完成后：
1. 用户界面更加专业
2. 代码更加简洁
3. 没有测试相关的干扰
4. 应用准备就绪，可以提交审核 