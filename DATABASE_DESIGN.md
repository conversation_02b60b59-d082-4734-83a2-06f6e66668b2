# 🗄️ AI塔罗应用数据库设计

## 📊 数据需求分析

### 核心功能数据
1. **用户管理**: 用户信息、认证、偏好设置
2. **塔罗解读**: 问题、牌阵、卡牌、解读结果
3. **每日塔罗**: 每日抽牌、运势、建议
4. **显化功能**: 目标设置、肯定语、计数器
5. **用户偏好**: 语言、通知、界面设置

### 数据关系
- 用户 → 塔罗解读记录 (一对多)
- 用户 → 每日塔罗 (一对多)
- 用户 → 偏好设置 (一对一)
- 用户 → 显化记录 (一对多)

## 🏗️ 数据库表结构

### 1. 用户表 (users)
```sql
CREATE TABLE public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. 塔罗解读记录表 (tarot_readings)
```sql
CREATE TABLE public.tarot_readings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    spread_type TEXT NOT NULL, -- 'single', 'three', 'celtic', etc.
    cards JSONB NOT NULL, -- 存储卡牌数组
    interpretation TEXT,
    accuracy INTEGER CHECK (accuracy >= 1 AND accuracy <= 5),
    usefulness INTEGER CHECK (usefulness >= 1 AND usefulness <= 5),
    satisfaction INTEGER CHECK (satisfaction >= 1 AND satisfaction <= 5),
    feedback TEXT,
    follow_up_questions TEXT[], -- 追问问题数组
    follow_up_responses TEXT[], -- 追问回答数组
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 3. 每日塔罗表 (daily_tarot)
```sql
CREATE TABLE public.daily_tarot (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    card JSONB, -- 塔罗牌信息
    fortune TEXT, -- 运势
    advice TEXT, -- 建议
    is_drawn BOOLEAN DEFAULT FALSE,
    manifestation_goal TEXT, -- 'wealth', 'career', 'beauty', 'fame', 'love'
    affirmation TEXT, -- 肯定语
    manifestation_journal TEXT, -- 显化日记
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);
```

### 4. 显化记录表 (manifestation_records)
```sql
CREATE TABLE public.manifestation_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    goal TEXT NOT NULL, -- 'wealth', 'career', 'beauty', 'fame', 'love'
    date DATE NOT NULL,
    count INTEGER DEFAULT 0, -- 当日计数
    total_count INTEGER DEFAULT 0, -- 总计数
    affirmations TEXT[], -- 使用的肯定语
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, goal, date)
);
```

### 5. 用户偏好设置表 (user_preferences)
```sql
CREATE TABLE public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    language TEXT DEFAULT 'zh-CN', -- 语言设置
    notifications JSONB DEFAULT '{}', -- 通知设置
    theme_settings JSONB DEFAULT '{}', -- 主题设置
    privacy_settings JSONB DEFAULT '{}', -- 隐私设置
    preferences JSONB DEFAULT '{}', -- 其他偏好设置
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 6. 通知设置表 (notification_settings)
```sql
CREATE TABLE public.notification_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    daily_reminder BOOLEAN DEFAULT TRUE, -- 每日提醒
    reminder_time TIME DEFAULT '09:00:00', -- 提醒时间
    reading_insights BOOLEAN DEFAULT TRUE, -- 解读洞察
    manifestation_reminders BOOLEAN DEFAULT TRUE, -- 显化提醒
    weekly_summary BOOLEAN DEFAULT FALSE, -- 周总结
    push_enabled BOOLEAN DEFAULT TRUE, -- 推送开启
    email_enabled BOOLEAN DEFAULT FALSE, -- 邮件通知
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 📋 JSONB字段结构

### cards字段 (塔罗牌数组)
```json
[
  {
    "id": "major-0",
    "name": "The Fool",
    "description": "...",
    "meaning": "...",
    "keywords": ["beginnings", "innocence"],
    "image_url": "images/major_00_fool.png",
    "is_major_arcana": true,
    "is_reversed": false,
    "position": "过去" // 在牌阵中的位置
  }
]
```

### card字段 (单张塔罗牌)
```json
{
  "id": "major-0",
  "name": "The Fool",
  "description": "...",
  "meaning": "...",
  "keywords": ["beginnings", "innocence"],
  "image_url": "images/major_00_fool.png",
  "is_major_arcana": true,
  "is_reversed": false
}
```

### notifications字段 (通知设置)
```json
{
  "daily_reminder": true,
  "reminder_time": "09:00",
  "reading_insights": true,
  "manifestation_reminders": true,
  "weekly_summary": false
}
```

### preferences字段 (用户偏好)
```json
{
  "auto_save_readings": true,
  "show_card_meanings": true,
  "enable_sound": false,
  "card_animation_speed": "normal",
  "default_spread": "three"
}
```

## 🔧 索引设计

### 性能优化索引
```sql
-- 塔罗解读记录索引
CREATE INDEX idx_tarot_readings_user_id ON public.tarot_readings(user_id);
CREATE INDEX idx_tarot_readings_created_at ON public.tarot_readings(created_at DESC);
CREATE INDEX idx_tarot_readings_user_date ON public.tarot_readings(user_id, created_at DESC);

-- 每日塔罗索引
CREATE INDEX idx_daily_tarot_user_date ON public.daily_tarot(user_id, date DESC);
CREATE INDEX idx_daily_tarot_date ON public.daily_tarot(date);

-- 显化记录索引
CREATE INDEX idx_manifestation_user_goal ON public.manifestation_records(user_id, goal);
CREATE INDEX idx_manifestation_date ON public.manifestation_records(date DESC);
```

## 🛡️ 安全策略 (RLS)

### 行级安全策略
```sql
-- 启用行级安全
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tarot_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tarot ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.manifestation_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_settings ENABLE ROW LEVEL SECURITY;

-- 用户只能访问自己的数据
CREATE POLICY "Users can manage own profile" ON public.users
    USING (auth.uid() = id);

CREATE POLICY "Users can manage own readings" ON public.tarot_readings
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own daily tarot" ON public.daily_tarot
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own manifestation records" ON public.manifestation_records
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    USING (auth.uid() = user_id);

CREATE POLICY "Users can manage own notifications" ON public.notification_settings
    USING (auth.uid() = user_id);
```
