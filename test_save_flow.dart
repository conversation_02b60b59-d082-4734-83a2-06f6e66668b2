import 'package:flutter/material.dart';

/// 测试保存流程的简单页面
class TestSaveFlow extends StatelessWidget {
  const TestSaveFlow({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('测试保存流程')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('测试AI解读保存流程', style: TextStyle(fontSize: 20)),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                print('🧪 开始测试保存流程');
                _testSaveFlow();
              },
              child: const Text('测试保存'),
            ),
          ],
        ),
      ),
    );
  }

  void _testSaveFlow() {
    print('🔄 模拟AI解读完成');
    print('💾 模拟调用保存逻辑');
    print('✅ 测试完成');
  }
}
