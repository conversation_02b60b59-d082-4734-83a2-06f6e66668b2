{"alwaysApply": true, "stages": [{"name": "plan_schema", "trigger": "add table", "prompt": "分析数据库 schema，列出需要创建或修改的表结构与字段。先输出 plan，然后等待确认。"}, {"name": "generate_migration", "trigger": "apply migration", "prompt": "请生成 SQL migration 脚本，并调用 MCP 的 apply_migration 工具。"}, {"name": "generate_api", "trigger": "after migration", "prompt": "生成后端 API 路由/edge function 接入 Supabase 客户端，涵盖 CRUD 与认证。"}, {"name": "code_review", "trigger": "on code changes", "prompt": "检查新增代码，找 bug，并自动修复。"}], "mcpServers": {"supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--project-ref=ktqlxbcauxomczubqasp"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************"}}}}