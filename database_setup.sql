-- 🗄️ AI塔罗应用完整数据库建表脚本
-- 在 Supabase SQL Editor 中执行此脚本

-- 启用必要的扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ============================================================================
-- 1. 用户表 (扩展auth.users)
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.users (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 2. 塔罗解读记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.tarot_readings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    question TEXT NOT NULL,
    spread_type TEXT NOT NULL CHECK (spread_type IN ('single', 'three', 'celtic', 'custom')),
    cards JSONB NOT NULL,
    interpretation TEXT,
    accuracy INTEGER CHECK (accuracy >= 1 AND accuracy <= 5),
    usefulness INTEGER CHECK (usefulness >= 1 AND usefulness <= 5),
    satisfaction INTEGER CHECK (satisfaction >= 1 AND satisfaction <= 5),
    feedback TEXT,
    follow_up_questions TEXT[],
    follow_up_responses TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 3. 每日塔罗表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.daily_tarot (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    card JSONB,
    fortune TEXT,
    advice TEXT,
    is_drawn BOOLEAN DEFAULT FALSE,
    manifestation_goal TEXT CHECK (manifestation_goal IN ('wealth', 'career', 'beauty', 'fame', 'love')),
    affirmation TEXT,
    manifestation_journal TEXT, -- 显化日记字段
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- ============================================================================
-- 4. 显化记录表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.manifestation_records (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    goal TEXT NOT NULL CHECK (goal IN ('wealth', 'career', 'beauty', 'fame', 'love')),
    date DATE NOT NULL,
    count INTEGER DEFAULT 0,
    total_count INTEGER DEFAULT 0,
    affirmations TEXT[],
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, goal, date)
);

-- ============================================================================
-- 5. 正念练习统计表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.mindfulness_stats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    date DATE NOT NULL,
    total_count INTEGER DEFAULT 0, -- 当日总练习次数
    category_counts JSONB DEFAULT '{}', -- 分类统计 {"wealth": 5, "career": 3, ...}
    session_durations INTEGER[] DEFAULT '{}', -- 每次练习时长(秒)
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, date)
);

-- ============================================================================
-- 6. 用户偏好设置表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    language TEXT DEFAULT 'zh-CN' CHECK (language IN ('zh-CN', 'zh-TW', 'en-US', 'es-ES', 'ja-JP', 'ko-KR')),
    notifications JSONB DEFAULT '{}',
    theme_settings JSONB DEFAULT '{}',
    privacy_settings JSONB DEFAULT '{}',
    preferences JSONB DEFAULT '{}',
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 7. 通知设置表
-- ============================================================================
CREATE TABLE IF NOT EXISTS public.notification_settings (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    daily_reminder BOOLEAN DEFAULT TRUE,
    reminder_time TIME DEFAULT '09:00:00',
    reading_insights BOOLEAN DEFAULT TRUE,
    manifestation_reminders BOOLEAN DEFAULT TRUE,
    weekly_summary BOOLEAN DEFAULT FALSE,
    push_enabled BOOLEAN DEFAULT TRUE,
    email_enabled BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- 7. 创建索引（优化查询性能）
-- ============================================================================

-- 塔罗解读记录索引
CREATE INDEX IF NOT EXISTS idx_tarot_readings_user_id ON public.tarot_readings(user_id);
CREATE INDEX IF NOT EXISTS idx_tarot_readings_created_at ON public.tarot_readings(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_tarot_readings_user_date ON public.tarot_readings(user_id, created_at DESC);

-- 每日塔罗索引
CREATE INDEX IF NOT EXISTS idx_daily_tarot_user_date ON public.daily_tarot(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_daily_tarot_date ON public.daily_tarot(date);

-- 显化记录索引
CREATE INDEX IF NOT EXISTS idx_manifestation_user_goal ON public.manifestation_records(user_id, goal);
CREATE INDEX IF NOT EXISTS idx_manifestation_date ON public.manifestation_records(date DESC);

-- 正念练习统计索引
CREATE INDEX IF NOT EXISTS idx_mindfulness_user_date ON public.mindfulness_stats(user_id, date DESC);
CREATE INDEX IF NOT EXISTS idx_mindfulness_date ON public.mindfulness_stats(date DESC);

-- ============================================================================
-- 8. 启用行级安全 (RLS)
-- ============================================================================

ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tarot_readings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.daily_tarot ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.manifestation_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.mindfulness_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_settings ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- 9. 创建安全策略
-- ============================================================================

-- 删除现有策略（如果存在）
DROP POLICY IF EXISTS "Users can manage own profile" ON public.users;
DROP POLICY IF EXISTS "Users can manage own readings" ON public.tarot_readings;
DROP POLICY IF EXISTS "Users can manage own daily tarot" ON public.daily_tarot;
DROP POLICY IF EXISTS "Users can manage own manifestation records" ON public.manifestation_records;
DROP POLICY IF EXISTS "Users can manage own mindfulness stats" ON public.mindfulness_stats;
DROP POLICY IF EXISTS "Users can manage own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can manage own notifications" ON public.notification_settings;

-- 用户表策略
CREATE POLICY "Users can manage own profile" ON public.users
    USING (auth.uid() = id);

-- 塔罗解读记录策略
CREATE POLICY "Users can manage own readings" ON public.tarot_readings
    USING (auth.uid() = user_id);

-- 每日塔罗策略
CREATE POLICY "Users can manage own daily tarot" ON public.daily_tarot
    USING (auth.uid() = user_id);

-- 显化记录策略
CREATE POLICY "Users can manage own manifestation records" ON public.manifestation_records
    USING (auth.uid() = user_id);

-- 正念练习统计策略
CREATE POLICY "Users can manage own mindfulness stats" ON public.mindfulness_stats
    USING (auth.uid() = user_id);

-- 用户偏好策略
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    USING (auth.uid() = user_id);

-- 通知设置策略
CREATE POLICY "Users can manage own notifications" ON public.notification_settings
    USING (auth.uid() = user_id);

-- ============================================================================
-- 10. 创建触发器函数
-- ============================================================================

-- 自动创建用户资料函数
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, full_name, avatar_url)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.raw_user_meta_data->>'full_name',
        NEW.raw_user_meta_data->>'avatar_url'
    );

    -- 创建默认偏好设置
    INSERT INTO public.user_preferences (user_id, language, preferences)
    VALUES (
        NEW.id,
        'zh-CN',
        '{
            "auto_save_readings": true,
            "show_card_meanings": true,
            "enable_sound": false,
            "card_animation_speed": "normal",
            "default_spread": "three"
        }'::jsonb
    );

    -- 创建默认通知设置
    INSERT INTO public.notification_settings (user_id)
    VALUES (NEW.id);

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 更新时间戳函数
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ============================================================================
-- 11. 创建触发器
-- ============================================================================

-- 用户注册时自动创建资料
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 自动更新时间戳触发器
DROP TRIGGER IF EXISTS update_users_updated_at ON public.users;
CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON public.user_preferences;
CREATE TRIGGER update_user_preferences_updated_at
    BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_notification_settings_updated_at ON public.notification_settings;
CREATE TRIGGER update_notification_settings_updated_at
    BEFORE UPDATE ON public.notification_settings
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_manifestation_records_updated_at ON public.manifestation_records;
CREATE TRIGGER update_manifestation_records_updated_at
    BEFORE UPDATE ON public.manifestation_records
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

DROP TRIGGER IF EXISTS update_mindfulness_stats_updated_at ON public.mindfulness_stats;
CREATE TRIGGER update_mindfulness_stats_updated_at
    BEFORE UPDATE ON public.mindfulness_stats
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- ============================================================================
-- 12. 验证安装
-- ============================================================================

-- 查看创建的表
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- 完成提示
SELECT '🎉 数据库表创建完成！所有表已经成功建立并配置了安全策略。' as message; 